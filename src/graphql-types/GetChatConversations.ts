/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ConversationFilters, MessageType, MessageStatus } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetChatConversations
// ====================================================

export interface GetChatConversations_chatConversations_list_lastMessage {
  __typename: "ChatMessageModel";
  id: string;
  createdDate: any;
  senderId: string;
  messageType: MessageType;
  content: string;
  status: MessageStatus;
  conversationId: string;
  senderName: string;
  facilityName: string | null;
}

export interface GetChatConversations_chatConversations_list_opponent_personalInformation {
  __typename: "PersonalInformation";
  displayPictureUrl: string | null;
}

export interface GetChatConversations_chatConversations_list_opponent {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  personalInformation: GetChatConversations_chatConversations_list_opponent_personalInformation | null;
}

export interface GetChatConversations_chatConversations_list {
  __typename: "ChatConversationModel";
  id: string;
  createdDate: any;
  participant1Id: string;
  participant2Id: string;
  participant1HospitalId: string | null;
  participant2HospitalId: string | null;
  participant1HospitalName: string | null;
  participant2HospitalName: string | null;
  hmoProviderId: string | null;
  unreadMessageCount: number | null;
  lastMessage: GetChatConversations_chatConversations_list_lastMessage | null;
  opponent: GetChatConversations_chatConversations_list_opponent | null;
}

export interface GetChatConversations_chatConversations {
  __typename: "ConversationListResponse";
  list: GetChatConversations_chatConversations_list[];
  totalCount: number;
}

export interface GetChatConversations {
  chatConversations: GetChatConversations_chatConversations;
}

export interface GetChatConversationsVariables {
  filters?: ConversationFilters | null;
}
