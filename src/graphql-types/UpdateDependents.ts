/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DependentInput, Gender } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateDependents
// ====================================================

export interface UpdateDependents_updateDependentInfo_phoneNumber {
  __typename: "PhoneNumberFields";
  value: string | null;
  countryCode: string | null;
  countryName: string | null;
}

export interface UpdateDependents_updateDependentInfo_hmoProfile_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface UpdateDependents_updateDependentInfo_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  memberPlan: string | null;
  memberNumber: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  primaryProviderAddress: string | null;
  provider: UpdateDependents_updateDependentInfo_hmoProfile_provider;
}

export interface UpdateDependents_updateDependentInfo_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface UpdateDependents_updateDependentInfo {
  __typename: "DependentModel";
  id: string;
  title: string | null;
  firstName: string | null;
  lastName: string | null;
  middleName: string | null;
  gender: Gender | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  relationship: string | null;
  emailAddress: string | null;
  phoneNumber: UpdateDependents_updateDependentInfo_phoneNumber | null;
  hmoProfile: UpdateDependents_updateDependentInfo_hmoProfile | null;
  profile: UpdateDependents_updateDependentInfo_profile | null;
  displayPictureUrl: string | null;
}

export interface UpdateDependents {
  updateDependentInfo: UpdateDependents_updateDependentInfo;
}

export interface UpdateDependentsVariables {
  input: DependentInput;
  id: string;
}
