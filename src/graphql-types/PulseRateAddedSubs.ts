/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PulseRateAddedSubs
// ====================================================

export interface PulseRateAddedSubs_PulseRateAdded {
  __typename: "PulseRateModel";
  id: string;
  readingDateTime: any | null;
  reading: string | null;
  checkMethod: string | null;
  checkMethodSpecify: string | null;
  rhythm: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
  vitalId: string | null;
}

export interface PulseRateAddedSubs {
  PulseRateAdded: PulseRateAddedSubs_PulseRateAdded;
}

export interface PulseRateAddedSubsVariables {
  profileId: string;
}
