/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EnrollmentAgencyInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateEnrollmentAgency
// ====================================================

export interface UpdateEnrollmentAgency_updateEnrollmentAgency_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface UpdateEnrollmentAgency_updateEnrollmentAgency_tpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
}

export interface UpdateEnrollmentAgency_updateEnrollmentAgency_primaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrollmentAgency_updateEnrollmentAgency_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrollmentAgency_updateEnrollmentAgency_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrollmentAgency_updateEnrollmentAgency_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrollmentAgency_updateEnrollmentAgency {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
  agencyCode: string | null;
  address: string | null;
  country: string | null;
  state: string | null;
  agencyType: string | null;
  profile: UpdateEnrollmentAgency_updateEnrollmentAgency_profile | null;
  tpaNonTpa: UpdateEnrollmentAgency_updateEnrollmentAgency_tpaNonTpa | null;
  localGovernmentArea: string | null;
  primaryPhoneNumber: UpdateEnrollmentAgency_updateEnrollmentAgency_primaryPhoneNumber | null;
  primaryEmailAddress: string | null;
  secondaryPhoneNumber: UpdateEnrollmentAgency_updateEnrollmentAgency_secondaryPhoneNumber | null;
  secondaryEmailAddress: string | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: UpdateEnrollmentAgency_updateEnrollmentAgency_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: UpdateEnrollmentAgency_updateEnrollmentAgency_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  accountName: string | null;
  accountNumber: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface UpdateEnrollmentAgency {
  updateEnrollmentAgency: UpdateEnrollmentAgency_updateEnrollmentAgency;
}

export interface UpdateEnrollmentAgencyVariables {
  id: string;
  input: EnrollmentAgencyInput;
}
