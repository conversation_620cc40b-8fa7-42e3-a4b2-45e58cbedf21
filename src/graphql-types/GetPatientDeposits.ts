/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PaymentDepositFilterInput, Currency } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPatientDeposits
// ====================================================

export interface GetPatientDeposits_profile_paymentDeposits_list_collectedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface GetPatientDeposits_profile_paymentDeposits_list_withdrawnBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface GetPatientDeposits_profile_paymentDeposits_list {
  __typename: "PaymentDepositModel";
  id: string;
  depositDate: any;
  depositMethod: string | null;
  currency: Currency;
  profileId: string;
  hospitalId: string;
  amountDeposited: number;
  amountUsed: number;
  additionalNote: string | null;
  collectedById: string | null;
  creatorId: string;
  lastModifierId: string | null;
  description: string | null;
  createdDate: any;
  updatedDate: any | null;
  autoGenerated: boolean | null;
  amountRefunded: number | null;
  finalDepositBalance: number | null;
  initialDepositBalance: number | null;
  isManualRefund: boolean | null;
  refundDate: any | null;
  refundedBy: string | null;
  collectedBy: GetPatientDeposits_profile_paymentDeposits_list_collectedBy | null;
  withdrawnBy: GetPatientDeposits_profile_paymentDeposits_list_withdrawnBy | null;
}

export interface GetPatientDeposits_profile_paymentDeposits {
  __typename: "PaymentDepositListResponse";
  totalCount: number;
  list: GetPatientDeposits_profile_paymentDeposits_list[];
}

export interface GetPatientDeposits_profile {
  __typename: "ProfileModel";
  id: string;
  paymentDeposits: GetPatientDeposits_profile_paymentDeposits;
}

export interface GetPatientDeposits {
  profile: GetPatientDeposits_profile;
}

export interface GetPatientDepositsVariables {
  filterOptions: PaymentDepositFilterInput;
  id: string;
}
