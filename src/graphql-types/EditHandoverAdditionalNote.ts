/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HandoverAdditionalNoteInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: EditHandoverAdditionalNote
// ====================================================

export interface EditHandoverAdditionalNote_updateHandoverAdditionalNote {
  __typename: "HandoverAdditionalNoteModel";
  id: string;
  additionalNote: string;
  handoverNoteId: string;
  creatorId: string;
  createdDate: any;
  updatedDate: any | null;
  lastModifierName: string | null;
}

export interface EditHandoverAdditionalNote {
  updateHandoverAdditionalNote: EditHandoverAdditionalNote_updateHandoverAdditionalNote;
}

export interface EditHandoverAdditionalNoteVariables {
  id: string;
  input: HandoverAdditionalNoteInput;
}
