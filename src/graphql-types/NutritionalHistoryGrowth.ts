/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: NutritionalHistoryGrowth
// ====================================================

export interface NutritionalHistoryGrowth {
  __typename: "NutritionalHistoryGrowthModel";
  id: string;
  months: number;
  years: number;
  weight: string | null;
  height: string | null;
  babyLength: string | null;
  headCircumference: string | null;
  measurementType: string;
}
