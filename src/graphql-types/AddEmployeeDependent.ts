/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CreateEmployerDependantInput, Gender } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddEmployeeDependent
// ====================================================

export interface AddEmployeeDependent_addDependants_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  memberNumber: string | null;
  primaryProviderId: string | null;
  primaryProviderAddress: string | null;
  primaryProviderName: string | null;
}

export interface AddEmployeeDependent_addDependants_profile_details {
  __typename: "ProfileDetailsModel";
  id: string;
  dateOfBirth: any | null;
}

export interface AddEmployeeDependent_addDependants_profile_user {
  __typename: "UserModel";
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
}

export interface AddEmployeeDependent_addDependants_profile {
  __typename: "ProfileModel";
  id: string;
  gender: Gender | null;
  title: string | null;
  details: AddEmployeeDependent_addDependants_profile_details | null;
  user: AddEmployeeDependent_addDependants_profile_user;
}

export interface AddEmployeeDependent_addDependants {
  __typename: "EmployeeDependantModel";
  id: string;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  relationship: string | null;
  title: string | null;
  employeeId: string;
  lastModifierName: string | null;
  creatorName: string | null;
  createdDate: any;
  updatedDate: any;
  displayPictureUrl: string | null;
  hmoProfile: AddEmployeeDependent_addDependants_hmoProfile | null;
  profile: AddEmployeeDependent_addDependants_profile | null;
}

export interface AddEmployeeDependent {
  addDependants: AddEmployeeDependent_addDependants[];
}

export interface AddEmployeeDependentVariables {
  employeeId: string;
  input: CreateEmployerDependantInput[];
}
