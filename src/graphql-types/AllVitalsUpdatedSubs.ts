/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: AllVitalsUpdatedSubs
// ====================================================

export interface AllVitalsUpdatedSubs_AllVitalsUpdated_anthropometry {
  __typename: "AnthropometryModel";
  id: string;
  bmi: string | null;
  bsa: string | null;
  readingDateTime: any | null;
  height: string | null;
  heightUnit: string | null;
  weight: string | null;
  weightUnit: string | null;
  hipCircumference: string | null;
  hipCircumferenceUnit: string | null;
  waistCircumference: string | null;
  waistCircumferenceUnit: string | null;
  skinfoldThickness: string | null;
  skinfoldThicknessUnit: string | null;
  leftUpperLimbCircumference: string | null;
  rightUpperLimbCircumference: string | null;
  upperLimbCircumferenceUnit: string | null;
  leftLowerLimbCircumference: string | null;
  rightLowerLimbCircumference: string | null;
  lowerLimbCircumferenceUnit: string | null;
  leftThighCircumference: string | null;
  rightThighCircumference: string | null;
  thighCircumferenceUnit: string | null;
  abdominalGirth: string | null;
  abdominalGirthUnit: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isAbdominalGirthCritical: boolean | null;
  isHeightCritical: boolean | null;
  isWeightCritical: boolean | null;
  isSkinfoldThicknessCritical: boolean | null;
  isWaistCircumferenceCritical: boolean | null;
  isHipCircumferenceCritical: boolean | null;
  creatorName: string | null;
}

export interface AllVitalsUpdatedSubs_AllVitalsUpdated_bloodPressure {
  __typename: "BloodPressureModel";
  id: string;
  readingDateTime: any | null;
  diastolic: string | null;
  systolic: string | null;
  meanArterialPressure: string | null;
  heartRate: string | null;
  fetalHeartRate: string | null;
  location: string | null;
  method: string | null;
  additionalNote: string | null;
  position: string | null;
  rhythm: string | null;
  concealAdditionalNote: boolean | null;
  isDiastolicCritical: boolean | null;
  isSystolicCritical: boolean | null;
  isHeartRateCritical: boolean | null;
  isFetalHeartRateCritical: boolean | null;
  creatorName: string | null;
}

export interface AllVitalsUpdatedSubs_AllVitalsUpdated_pulseRate {
  __typename: "PulseRateModel";
  id: string;
  readingDateTime: any | null;
  reading: string | null;
  checkMethod: string | null;
  checkMethodSpecify: string | null;
  rhythm: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
  creatorName: string | null;
}

export interface AllVitalsUpdatedSubs_AllVitalsUpdated_respiratoryRate {
  __typename: "RespiratoryRateModel";
  id: string;
  readingDateTime: any | null;
  reading: string | null;
  oxygenSaturation: string | null;
  spO2Site: string | null;
  O2FlowRate: string | null;
  fIO2: string | null;
  O2Therapy: string | null;
  etco2: string | null;
  etco2Unit: string | null;
  cardiacRythm: string | null;
  rhythm: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
  isO2FlowRateCritical: boolean | null;
  isOxygenSaturationCritical: boolean | null;
  isEtco2Critical: boolean | null;
  isFIO2Critical: boolean | null;
  creatorName: string | null;
}

export interface AllVitalsUpdatedSubs_AllVitalsUpdated_temperature {
  __typename: "TemperatureModel";
  id: string;
  readingDateTime: any | null;
  checkMethod: string | null;
  reading: string | null;
  readingUnit: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
  creatorName: string | null;
}

export interface AllVitalsUpdatedSubs_AllVitalsUpdated_bloodGlucose {
  __typename: "BloodGlucoseModel";
  id: string;
  readingDateTime: any | null;
  reading: string | null;
  readingUnit: string | null;
  mealTime: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
  creatorName: string | null;
}

export interface AllVitalsUpdatedSubs_AllVitalsUpdated_pain {
  __typename: "PainModel";
  id: string;
  score: string | null;
  type: string | null;
  dateTimePainStarted: any | null;
  location: string[] | null;
  specifyLocation: string | null;
  orientation: string[] | null;
  specifyOrientation: string | null;
  radiatingTowards: string | null;
  descriptors: string[] | null;
  specifyDescriptors: string | null;
  frequency: string[] | null;
  specifyFrequency: string | null;
  onset: string[] | null;
  specifyOnset: string | null;
  clinicalProgression: string[] | null;
  specifyClinicalProgression: string | null;
  aggravatingFactors: string[] | null;
  specifyAggravatingFactors: string | null;
  painCausedAsResultOfInjury: string | null;
  workRelatedInjury: string | null;
  painGoal: string | null;
  interventions: string[] | null;
  specifyInterventions: string | null;
  responsetoInterventions: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  creatorName: string | null;
}

export interface AllVitalsUpdatedSubs_AllVitalsUpdated {
  __typename: "AllVitalResponse";
  vitalId: string;
  creatorId: string | null;
  anthropometry: AllVitalsUpdatedSubs_AllVitalsUpdated_anthropometry;
  bloodPressure: AllVitalsUpdatedSubs_AllVitalsUpdated_bloodPressure;
  pulseRate: AllVitalsUpdatedSubs_AllVitalsUpdated_pulseRate;
  respiratoryRate: AllVitalsUpdatedSubs_AllVitalsUpdated_respiratoryRate;
  temperature: AllVitalsUpdatedSubs_AllVitalsUpdated_temperature;
  bloodGlucose: AllVitalsUpdatedSubs_AllVitalsUpdated_bloodGlucose;
  pain: AllVitalsUpdatedSubs_AllVitalsUpdated_pain | null;
}

export interface AllVitalsUpdatedSubs {
  AllVitalsUpdated: AllVitalsUpdatedSubs_AllVitalsUpdated;
}

export interface AllVitalsUpdatedSubsVariables {
  profileId: string;
}
