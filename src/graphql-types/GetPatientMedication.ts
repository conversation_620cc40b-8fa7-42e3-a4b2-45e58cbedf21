/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicationOptionType, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPatientMedication
// ====================================================

export interface GetPatientMedication_medication_details_medicationConsumables {
  __typename: "MedicationConsumables";
  name: string | null;
  drugInventoryId: string | null;
  unitPrice: number | null;
  quantity: string | null;
  inventoryClass: string | null;
}

export interface GetPatientMedication_medication_details_priceDetails {
  __typename: "MedPriceDetailInputType";
  type: string | null;
  name: string | null;
  pricePerUnit: string | null;
  patientType: string | null;
  paymentType: string | null;
  quantity: string | null;
}

export interface GetPatientMedication_medication_details_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface GetPatientMedication_medication_details_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: GetPatientMedication_medication_details_preauthorizationDetails_provider | null;
}

export interface GetPatientMedication_medication_details_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface GetPatientMedication_medication_details_chemoDrugs_investigationDetails {
  __typename: "ChemoInvestigationDetails";
  investigationName: string | null;
  investigationType: string | null;
  investigationPerformed: boolean | null;
  investigationVerified: boolean | null;
}

export interface GetPatientMedication_medication_details_chemoDrugs_administrationRegister {
  __typename: "OncologyDrugAdministrationRegistration";
  administeredBy: string | null;
  administratorId: string | null;
  administrationDateTime: any | null;
  period: string;
}

export interface GetPatientMedication_medication_details_chemoDrugs_oncologyConsultationHistory_chemoComments {
  __typename: "ChemoCommentInputType";
  cycleNumber: number;
  section: string;
  comment: string | null;
  creatorId: string;
  creatorName: string;
  createdDate: any;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
}

export interface GetPatientMedication_medication_details_chemoDrugs_oncologyConsultationHistory {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  chemoComments: GetPatientMedication_medication_details_chemoDrugs_oncologyConsultationHistory_chemoComments[] | null;
}

export interface GetPatientMedication_medication_details_chemoDrugs {
  __typename: "OncologyChemoDrugModel";
  id: string;
  drugName: string;
  route: string | null;
  infusionUsed: string | null;
  dosage: string | null;
  dosagePercentage: string | null;
  totalDose: string | null;
  adjustedDose: string | null;
  quantity: string | null;
  day: string;
  cycleNumber: number;
  drugId: string | null;
  note: string | null;
  section: string;
  frequency: string | null;
  combinationGroupName: string | null;
  combinationName: string | null;
  chemoDiagnosis: string | null;
  inventoryClass: string | null;
  medicationDetailsId: string | null;
  investigationDetails: GetPatientMedication_medication_details_chemoDrugs_investigationDetails[] | null;
  administrationRegister: GetPatientMedication_medication_details_chemoDrugs_administrationRegister[] | null;
  oncologyConsultationHistory: GetPatientMedication_medication_details_chemoDrugs_oncologyConsultationHistory | null;
}

export interface GetPatientMedication_medication_details_dispenseRegister_details_periods_audits {
  __typename: "dospenseRegisterAudit";
  fullName: string | null;
  dateTime: any | null;
  desc: string | null;
  checkId: number | null;
  profileId: string | null;
}

export interface GetPatientMedication_medication_details_dispenseRegister_details_periods {
  __typename: "PeriodsDetails";
  no: number | null;
  values: string | null;
  count: number | null;
  audits: GetPatientMedication_medication_details_dispenseRegister_details_periods_audits[] | null;
}

export interface GetPatientMedication_medication_details_dispenseRegister_details {
  __typename: "dispenseRegisterDetails";
  medicationName: string;
  reference: string | null;
  periodName: string;
  periods: GetPatientMedication_medication_details_dispenseRegister_details_periods[];
}

export interface GetPatientMedication_medication_details_dispenseRegister {
  __typename: "DispenseRegistersModel";
  id: string;
  details: GetPatientMedication_medication_details_dispenseRegister_details | null;
  creator: string | null;
  updater: string | null;
  updatedDate: any;
  createdDate: any;
}

export interface GetPatientMedication_medication_details_oxygenTherapy_details {
  __typename: "OxygenTherapyDetails";
  administeredBy: string | null;
  administrationDateTime: any | null;
  administrationHour: string | null;
  saturation: string | null;
  therapyType: string | null;
}

export interface GetPatientMedication_medication_details_oxygenTherapy {
  __typename: "OxygenTherapyModel";
  id: string;
  createdDate: any;
  creator: string | null;
  medicationDetailId: string | null;
  updatedDate: any;
  updater: string | null;
  details: GetPatientMedication_medication_details_oxygenTherapy_details[] | null;
}

export interface GetPatientMedication_medication_details_infusion_details {
  __typename: "MedicationInfusionDetails";
  typeOrStrength: string | null;
  volume: string | null;
  drugName: string | null;
  dosage: string | null;
  route: string | null;
  dripRate: string | null;
  batchNumber: string | null;
  prescribedBy: string | null;
  administrationDateTime: any | null;
  administeredBy: string | null;
  checkedBy: string | null;
}

export interface GetPatientMedication_medication_details_infusion {
  __typename: "MedicationInfusionModel";
  id: string;
  creator: string | null;
  updater: string | null;
  createdDate: any;
  updatedDate: any;
  medicationDetailId: string | null;
  details: GetPatientMedication_medication_details_infusion_details[] | null;
}

export interface GetPatientMedication_medication_details {
  __typename: "MedicationDetailsModel";
  id: string;
  datePrescribed: any | null;
  duration: string | null;
  medicationName: string | null;
  medicationCategory: string | null;
  purpose: string | null;
  administrationMethod: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  prescriptionNote: string | null;
  concealPrescriptionNote: boolean | null;
  type: string | null;
  quantity: string | null;
  startDate: any | null;
  endDate: any | null;
  bank: string | null;
  drugInventoryId: string | null;
  option: MedicationOptionType | null;
  unitPrice: string | null;
  medicationStatus: string | null;
  provider: string | null;
  fromBundle: string | null;
  medicationConsumables: GetPatientMedication_medication_details_medicationConsumables[] | null;
  priceDetails: GetPatientMedication_medication_details_priceDetails | null;
  discontinue: string | null;
  discontinueReason: string | null;
  adverseEffectsFollowingMedication: string | null;
  stateEffects: string | null;
  adverseEffectsInvestigated: string | null;
  outcomeOfInvestigation: string | null;
  refillNumber: number | null;
  frequency: string | null;
  isPackage: boolean;
  hospitalId: string | null;
  medicationType: string | null;
  preauthorizationDetails: GetPatientMedication_medication_details_preauthorizationDetails | null;
  diagnosis: GetPatientMedication_medication_details_diagnosis[] | null;
  chemoDrugs: GetPatientMedication_medication_details_chemoDrugs[] | null;
  inventoryClass: string | null;
  dispenseRegister: GetPatientMedication_medication_details_dispenseRegister | null;
  oxygenTherapy: GetPatientMedication_medication_details_oxygenTherapy | null;
  infusion: GetPatientMedication_medication_details_infusion | null;
}

export interface GetPatientMedication_medication_administrationNotes {
  __typename: "AdministrationNoteModel";
  id: string;
  administrationNote: string | null;
  conceal: boolean | null;
  medicationId: string;
}

export interface GetPatientMedication_medication_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface GetPatientMedication_medication_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: GetPatientMedication_medication_profile_coverageDetails_provider | null;
}

export interface GetPatientMedication_medication_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
  coverageDetails: GetPatientMedication_medication_profile_coverageDetails[] | null;
}

export interface GetPatientMedication_medication {
  __typename: "MedicationModel";
  id: string;
  prescribedBy: string | null;
  rank: string | null;
  department: string | null;
  specialty: string | null;
  verificationCode: string | null;
  details: GetPatientMedication_medication_details[] | null;
  administrationNotes: GetPatientMedication_medication_administrationNotes[] | null;
  dispenseIds: string[] | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  setReminder: boolean | null;
  reminderStartDate: any | null;
  reminderEndDate: any | null;
  medicationStartTime: string | null;
  medicationEndTime: string | null;
  interval: number | null;
  intervalUnit: string | null;
  remindMe: string | null;
  documentUrl: string[] | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  createdDate: any;
  updatedDate: any;
  clinifyId: string | null;
  billStatus: string | null;
  hospitalId: string | null;
  profileId: string | null;
  profile: GetPatientMedication_medication_profile | null;
}

export interface GetPatientMedication {
  medication: GetPatientMedication_medication;
}

export interface GetPatientMedicationVariables {
  id: string;
  clinifyId: string;
}
