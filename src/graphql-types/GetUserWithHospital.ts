/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PatientProfileType, Gender, HospitalPlan } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetUserWithHospital
// ====================================================

export interface GetUserWithHospital_profile_fileNumbers {
  __typename: "FileNumberInputType";
  coverageRef: string | null;
  fileNumber: string | null;
  existingFamily: boolean | null;
}

export interface GetUserWithHospital_profile_coverageDetails_questionnaireData {
  __typename: "QuestionnaireData";
  employmentStatus: string | null;
  gender: string | null;
  highestFormalEducationalLevel: string | null;
  maritalStatus: string | null;
  numberOfHouseholdMembers: string | null;
  numberOfMattresses: string | null;
  numberOfMobilePhones: string | null;
  numberOfRooms: string | null;
  numberOfTVs: string | null;
  numberOfVehicles: string | null;
  occupationalGroup: string | null;
  occupation: string | null;
  primaryCookingImplement: string | null;
  relationshipToHouseholdHead: string | null;
  typeOfRoof: string | null;
  typeOfToilet: string | null;
  /**
   * Score in percentage
   */
  questionnaireScore: number | null;
}

export interface GetUserWithHospital_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface GetUserWithHospital_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  coverageType: string | null;
  companyName: string | null;
  companyAddress: string | null;
  familyName: string | null;
  familyAddress: string | null;
  name: string | null;
  contactAddress: string | null;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  capturedDate: any | null;
  capturedBy: string | null;
  enrolledBy: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  paymentFrequency: string | null;
  memberStartDate: any | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  capitatedMember: string | null;
  capitatedAmount: string | null;
  employeeType: string | null;
  employeeDivision: string | null;
  occupation: string | null;
  retired: boolean | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  terminationDate: any | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionRate: string | null;
  commissionPayable: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
  employerCode: string | null;
  activationDatetime: any | null;
  totalPremiumAmountPaid: string | null;
  questionnaireData: GetUserWithHospital_profile_coverageDetails_questionnaireData | null;
  provider: GetUserWithHospital_profile_coverageDetails_provider | null;
}

export interface GetUserWithHospital_profile_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
}

export interface GetUserWithHospital_profile_user {
  __typename: "UserModel";
  id: string;
  email: string | null;
  corporatePhoneNumber: string | null;
  country: string | null;
  phoneNumber: string | null;
  nonCorporateEmail: string | null;
}

export interface GetUserWithHospital_profile_hospital_billingInformation {
  __typename: "FacilityBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  bvn: string | null;
  branchName: string | null;
  isPreferredPayoutAccount: boolean | null;
}

export interface GetUserWithHospital_profile_hospital_sponsorBillingInformation {
  __typename: "SponsorBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  bvn: string | null;
  branchName: string | null;
  sponsorName: string | null;
}

export interface GetUserWithHospital_profile_hospital_hmoHospitals_planVisibility {
  __typename: "PlanVisibilityDto";
  id: string;
  name: string;
}

export interface GetUserWithHospital_profile_hospital_hmoHospitals_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
  documentUrl: string[] | null;
}

export interface GetUserWithHospital_profile_hospital_hmoHospitals {
  __typename: "HmoHospitalModel";
  id: string;
  hmoProviderId: string;
  planVisibility: GetUserWithHospital_profile_hospital_hmoHospitals_planVisibility[] | null;
  provider: GetUserWithHospital_profile_hospital_hmoHospitals_provider | null;
}

export interface GetUserWithHospital_profile_hospital_phoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetUserWithHospital_profile_hospital_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetUserWithHospital_profile_hospital_hospitalSupportPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetUserWithHospital_profile_hospital_hmo {
  __typename: "HmoProviderModel";
  id: string;
  providerCode: string;
}

export interface GetUserWithHospital_profile_hospital {
  __typename: "HospitalModel";
  id: string;
  clinifyId: string | null;
  name: string | null;
  state: string | null;
  lga: string | null;
  politicalWard: string | null;
  city: string | null;
  ownership: string | null;
  level: string | null;
  address: string | null;
  website: string | null;
  country: string | null;
  plan: HospitalPlan;
  licenseNumber: string | null;
  documentUrl: string[] | null;
  billingInformation: GetUserWithHospital_profile_hospital_billingInformation[] | null;
  sponsorBillingInformation: GetUserWithHospital_profile_hospital_sponsorBillingInformation[] | null;
  hmoHospitals: GetUserWithHospital_profile_hospital_hmoHospitals[] | null;
  phoneNumber: GetUserWithHospital_profile_hospital_phoneNumber | null;
  facilityLogo: string | null;
  facebook: string | null;
  twitter: string | null;
  instagram: string | null;
  secondaryPhoneNumber: GetUserWithHospital_profile_hospital_secondaryPhoneNumber | null;
  hospitalSupportPhoneNumber: GetUserWithHospital_profile_hospital_hospitalSupportPhoneNumber | null;
  supportMail: string | null;
  preferenceId: string | null;
  hqFacilityId: string | null;
  hmoId: string | null;
  lastPaymentDateTime: any | null;
  nextPaymentDateTime: any | null;
  hmo: GetUserWithHospital_profile_hospital_hmo | null;
}

export interface GetUserWithHospital_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
  active: boolean;
  isDefault: boolean;
  createdDate: any | null;
  updatedDate: any | null;
  patientProfileType: PatientProfileType | null;
  dataAccessType: string | null;
  type: string;
  typeAlias: string | null;
  title: string | null;
  gender: Gender | null;
  patientStatus: string | null;
  deathDateTime: any | null;
  deathLocation: string | null;
  causeOfDeath: string | null;
  creatorName: string | null;
  lastModifierName: string | null;
  billStatus: string | null;
  createdFromHmo: boolean | null;
  shareData: boolean | null;
  fileNumbers: GetUserWithHospital_profile_fileNumbers[] | null;
  coverageDetails: GetUserWithHospital_profile_coverageDetails[] | null;
  serviceDetails: GetUserWithHospital_profile_serviceDetails[] | null;
  user: GetUserWithHospital_profile_user;
  hospital: GetUserWithHospital_profile_hospital | null;
}

export interface GetUserWithHospital {
  profile: GetUserWithHospital_profile;
}

export interface GetUserWithHospitalVariables {
  id: string;
}
