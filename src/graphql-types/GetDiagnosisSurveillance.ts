/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DiagnosisSurveillanceInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetDiagnosisSurveillance
// ====================================================

export interface GetDiagnosisSurveillance_getDiagnosisSurveillance {
  __typename: "DiagnosisSurveillanceResponse";
  count: number | null;
  diagnosis: string | null;
  lga: string | null;
  state: string | null;
}

export interface GetDiagnosisSurveillance {
  getDiagnosisSurveillance: GetDiagnosisSurveillance_getDiagnosisSurveillance[];
}

export interface GetDiagnosisSurveillanceVariables {
  filter: DiagnosisSurveillanceInput;
  isHmo?: boolean | null;
}
