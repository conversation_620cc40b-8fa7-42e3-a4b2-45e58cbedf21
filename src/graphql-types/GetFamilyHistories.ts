/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ProfileInfosFilterInput, Gender } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetFamilyHistories
// ====================================================

export interface GetFamilyHistories_profile_familyHistories_list_conditions {
  __typename: "ConditionInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
  ageOfOnset: string | null;
}

export interface GetFamilyHistories_profile_familyHistories_list_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface GetFamilyHistories_profile_familyHistories_list {
  __typename: "FamilyHistoryModel";
  id: string;
  title: string | null;
  firstName: string | null;
  lastName: string | null;
  middleName: string | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  relationship: string | null;
  conditions: GetFamilyHistories_profile_familyHistories_list_conditions[];
  gender: Gender | null;
  additionalNote: string | null;
  profile: GetFamilyHistories_profile_familyHistories_list_profile | null;
}

export interface GetFamilyHistories_profile_familyHistories {
  __typename: "FamilyHistoriesResponse";
  totalCount: number;
  list: GetFamilyHistories_profile_familyHistories_list[];
}

export interface GetFamilyHistories_profile {
  __typename: "ProfileModel";
  id: string;
  familyHistories: GetFamilyHistories_profile_familyHistories;
}

export interface GetFamilyHistories {
  profile: GetFamilyHistories_profile;
}

export interface GetFamilyHistoriesVariables {
  filterInput: ProfileInfosFilterInput;
  id: string;
}
