/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { excludeBillDetailInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: ExcludeBillDetail
// ====================================================

export interface ExcludeBillDetail_excludeBillDetail_details_subBills {
  __typename: "SubBillDetailsModel";
  id: string;
}

export interface ExcludeBillDetail_excludeBillDetail_details {
  __typename: "BillDetailsModel";
  id: string;
  excluded: boolean | null;
  subBills: ExcludeBillDetail_excludeBillDetail_details_subBills[] | null;
}

export interface ExcludeBillDetail_excludeBillDetail {
  __typename: "BillModel";
  id: string;
  details: ExcludeBillDetail_excludeBillDetail_details[] | null;
}

export interface ExcludeBillDetail {
  excludeBillDetail: ExcludeBillDetail_excludeBillDetail;
}

export interface ExcludeBillDetailVariables {
  billId: string;
  inputs: excludeBillDetailInput[];
}
