/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetFamilyRevenueData
// ====================================================

export interface GetFamilyRevenueData_getFamilyRevenueData_data {
  __typename: "categoryData";
  name: number | null;
  category: string | null;
  totalAmount: number | null;
  totalAmountOutstanding: number | null;
  totalAmountPaid: number | null;
  totalAmountDue: number | null;
  patientFullName: string | null;
  visitDate: string | null;
  serviceName: string | null;
  coverageInformationId: string | null;
  familyName: string | null;
  patientPhoneNumber: string | null;
  patientEmailAddress: string | null;
  billId: string | null;
  quantity: string | null;
  raisedBy: string | null;
  bankName: string | null;
  accountNumber: string | null;
  splitPaymentTypes: string | null;
  splitAmountPaid: number[] | null;
  splitBankNames: string | null;
  splitAccountNumbers: string | null;
}

export interface GetFamilyRevenueData_getFamilyRevenueData {
  __typename: "PaymentTypeData";
  data: GetFamilyRevenueData_getFamilyRevenueData_data[] | null;
}

export interface GetFamilyRevenueData {
  getFamilyRevenueData: GetFamilyRevenueData_getFamilyRevenueData;
}

export interface GetFamilyRevenueDataVariables {
  filter?: FinanceAnalyticsFilter | null;
}
