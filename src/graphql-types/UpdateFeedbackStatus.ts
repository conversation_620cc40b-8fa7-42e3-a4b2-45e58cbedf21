/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdateFeedbackStatusInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateFeedbackStatus
// ====================================================

export interface UpdateFeedbackStatus_updateFeedbackStatus_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface UpdateFeedbackStatus_updateFeedbackStatus_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface UpdateFeedbackStatus_updateFeedbackStatus {
  __typename: "FeedbackModel";
  id: string;
  feedbackDateTime: any | null;
  title: string;
  message: string;
  comment: string | null;
  category: string;
  isResolved: boolean | null;
  documentUrl: string[] | null;
  createdDate: any;
  creatorName: string | null;
  reviewerName: string | null;
  hospital: UpdateFeedbackStatus_updateFeedbackStatus_hospital | null;
  createdBy: UpdateFeedbackStatus_updateFeedbackStatus_createdBy;
}

export interface UpdateFeedbackStatus {
  updateFeedbackStatus: UpdateFeedbackStatus_updateFeedbackStatus;
}

export interface UpdateFeedbackStatusVariables {
  input: UpdateFeedbackStatusInput;
}
