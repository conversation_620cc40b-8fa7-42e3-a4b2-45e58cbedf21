/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { RequestPackageInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateRequestPackage
// ====================================================

export interface UpdateRequestPackage_updateRequestPackage_serviceDetails {
  __typename: "ServiceDetailsFields";
  serviceType: string | null;
  serviceName: string | null;
}

export interface UpdateRequestPackage_updateRequestPackage_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface UpdateRequestPackage_updateRequestPackage {
  __typename: "RequestPackageModel";
  id: string;
  requestDate: any;
  packageName: string;
  priority: string | null;
  category: string | null;
  orderedBy: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  price: string;
  paymentType: string | null;
  patientType: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  serviceDetails: UpdateRequestPackage_updateRequestPackage_serviceDetails[] | null;
  createdDate: any;
  updatedDate: any;
  profileId: string | null;
  profile: UpdateRequestPackage_updateRequestPackage_profile | null;
}

export interface UpdateRequestPackage {
  updateRequestPackage: UpdateRequestPackage_updateRequestPackage;
}

export interface UpdateRequestPackageVariables {
  input: RequestPackageInput;
  id: string;
}
