/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPrivateRevenueData
// ====================================================

export interface GetPrivateRevenueData_getPrivateRevenueData_data {
  __typename: "categoryData";
  name: number | null;
  category: string | null;
  totalAmount: number | null;
  totalAmountOutstanding: number | null;
  totalAmountPaid: number | null;
  totalAmountDue: number | null;
  patientFullName: string | null;
  visitDate: string | null;
  serviceName: string | null;
  patientPhoneNumber: string | null;
  patientEmailAddress: string | null;
  billId: string | null;
  quantity: string | null;
  raisedBy: string | null;
  bankName: string | null;
  accountNumber: string | null;
  splitPaymentTypes: string | null;
  splitAmountPaid: number[] | null;
  splitBankNames: string | null;
  splitAccountNumbers: string | null;
}

export interface GetPrivateRevenueData_getPrivateRevenueData {
  __typename: "PaymentTypeData";
  data: GetPrivateRevenueData_getPrivateRevenueData_data[] | null;
}

export interface GetPrivateRevenueData {
  getPrivateRevenueData: GetPrivateRevenueData_getPrivateRevenueData;
}

export interface GetPrivateRevenueDataVariables {
  filter?: FinanceAnalyticsFilter | null;
}
