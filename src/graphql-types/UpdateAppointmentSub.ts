/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UpdateAppointmentSub
// ====================================================

export interface UpdateAppointmentSub_AppointmentUpdated_specialist {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  type: string;
  fullName: string;
}

export interface UpdateAppointmentSub_AppointmentUpdated {
  __typename: "OrganisationAppointmentModel";
  id: string;
  specialist: UpdateAppointmentSub_AppointmentUpdated_specialist | null;
  createdDate: any;
  updatedDate: any;
  status: string | null;
  category: string | null;
  rank: string | null;
  reason: string | null;
  appointmentDateTime: any | null;
  startDateTime: any | null;
  endDateTime: any | null;
  specialty: string | null;
  paymentType: string | null;
  patientType: string | null;
  duration: string | null;
  confirmedBy: string | null;
  deliveryMethod: string | null;
  urgency: string | null;
  additionalNote: string | null;
}

export interface UpdateAppointmentSub {
  AppointmentUpdated: UpdateAppointmentSub_AppointmentUpdated;
}

export interface UpdateAppointmentSubVariables {
  profileId: string;
  hospitalId: string;
}
