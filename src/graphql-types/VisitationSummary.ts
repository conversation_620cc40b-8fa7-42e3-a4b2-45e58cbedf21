/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: VisitationSummary
// ====================================================

export interface VisitationSummary_groupedData_totalVisitations {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface VisitationSummary_groupedData_totalAppointmentsBooked {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface VisitationSummary_groupedData_totalAppointmentsCancelled {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface VisitationSummary_groupedData_totalAppointmentsCompleted {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface VisitationSummary_groupedData_totalAppointmentsMissed {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface VisitationSummary_groupedData_totalRegisteredPatients {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface VisitationSummary_groupedData_totalRegisteredPatientsUnderOneYear {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface VisitationSummary_groupedData_totalEmergency {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface VisitationSummary_groupedData_totalNonEmergency {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface VisitationSummary_groupedData_totalInPatient {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface VisitationSummary_groupedData_totalOutPatient {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface VisitationSummary_groupedData_totalReferral {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
  totalReferralIn: number | null;
  totalReferralOut: number | null;
}

export interface VisitationSummary_groupedData_totalWaitTimeInMinutes {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface VisitationSummary_groupedData {
  __typename: "GroupedVisitationSummary";
  totalVisitations: VisitationSummary_groupedData_totalVisitations[] | null;
  totalAppointmentsBooked: VisitationSummary_groupedData_totalAppointmentsBooked[] | null;
  totalAppointmentsCancelled: VisitationSummary_groupedData_totalAppointmentsCancelled[] | null;
  totalAppointmentsCompleted: VisitationSummary_groupedData_totalAppointmentsCompleted[] | null;
  totalAppointmentsMissed: VisitationSummary_groupedData_totalAppointmentsMissed[] | null;
  totalRegisteredPatients: VisitationSummary_groupedData_totalRegisteredPatients[] | null;
  totalRegisteredPatientsUnderOneYear: VisitationSummary_groupedData_totalRegisteredPatientsUnderOneYear[] | null;
  totalEmergency: VisitationSummary_groupedData_totalEmergency[] | null;
  totalNonEmergency: VisitationSummary_groupedData_totalNonEmergency[] | null;
  totalInPatient: VisitationSummary_groupedData_totalInPatient[] | null;
  totalOutPatient: VisitationSummary_groupedData_totalOutPatient[] | null;
  totalReferral: VisitationSummary_groupedData_totalReferral[] | null;
  totalWaitTimeInMinutes: VisitationSummary_groupedData_totalWaitTimeInMinutes[] | null;
}

export interface VisitationSummary {
  __typename: "VisitationSummary";
  name: number | null;
  totalRegisteredPatients: number | null;
  totalRegisteredPatientsUnderOneYear: number | null;
  totalAppointmentsBooked: number | null;
  totalAppointmentsCompleted: number | null;
  totalAppointmentsMissed: number | null;
  totalAppointmentsCancelled: number | null;
  totalVisitations: number | null;
  totalEmergency: number | null;
  totalNonEmergency: number | null;
  totalInPatient: number | null;
  totalOutPatient: number | null;
  totalAdmissions: number | null;
  totalReferralOut: number | null;
  totalReferralIn: number | null;
  totalWaitTimeInMinutes: number | null;
  groupedData: VisitationSummary_groupedData | null;
}
