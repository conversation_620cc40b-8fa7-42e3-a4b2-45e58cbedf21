/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetServiceRevenueData
// ====================================================

export interface GetServiceRevenueData_getServiceRevenueData_cardSummary {
  __typename: "ServiceSummary";
  service_type: string | null;
  count: number | null;
}

export interface GetServiceRevenueData_getServiceRevenueData_data {
  __typename: "FinanceData";
  name: number | null;
  category: string | null;
  totalAmount: number | null;
  totalAmountDue: number | null;
  totalAmountOutstanding: number | null;
  totalDiscount: number | null;
  totalAmountPaid: number | null;
  totalRevenue: number | null;
  visitationDate: any | null;
  serviceType: string | null;
  serviceName: string | null;
  quantity: string | null;
  raisedBy: string | null;
  patientName: string | null;
  patientClinifyId: string | null;
  patientPhoneNumber: string | null;
  patientEmailAddress: string | null;
  coverageType: string | null;
  coverageName: string | null;
  enrolleeId: string | null;
}

export interface GetServiceRevenueData_getServiceRevenueData {
  __typename: "ServiceRevenueResponse";
  cardSummary: GetServiceRevenueData_getServiceRevenueData_cardSummary[] | null;
  data: GetServiceRevenueData_getServiceRevenueData_data[] | null;
}

export interface GetServiceRevenueData {
  getServiceRevenueData: GetServiceRevenueData_getServiceRevenueData;
}

export interface GetServiceRevenueDataVariables {
  filter?: FinanceAnalyticsFilter | null;
}
