/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: SendReceiptToPatient
// ====================================================

export interface SendReceiptToPatient_sendReceiptToPatient {
  __typename: "BillSentResponse";
  sent: boolean;
  email: string;
  id: string;
}

export interface SendReceiptToPatient {
  sendReceiptToPatient: SendReceiptToPatient_sendReceiptToPatient;
}

export interface SendReceiptToPatientVariables {
  id: string;
  copyEmail?: string | null;
  origin: string;
  receiptSize: string;
}
