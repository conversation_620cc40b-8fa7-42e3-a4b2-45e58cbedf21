/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHMORevenueData
// ====================================================

export interface GetHMORevenueData_getHMORevenueData_data {
  __typename: "categoryData";
  name: number | null;
  category: string | null;
  serviceName: string | null;
  totalAmount: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
  totalAmountDue: number | null;
  patientFullName: string | null;
  enrolleeId: string | null;
  visitDate: string | null;
  providerId: string | null;
  patientPhoneNumber: string | null;
  patientEmailAddress: string | null;
  billId: string | null;
  quantity: string | null;
  raisedBy: string | null;
  splitPaymentTypes: string | null;
  splitAmountPaid: number[] | null;
  splitBankNames: string | null;
  splitAccountNumbers: string | null;
}

export interface GetHMORevenueData_getHMORevenueData {
  __typename: "PaymentTypeData";
  data: GetHMORevenueData_getHMORevenueData_data[] | null;
}

export interface GetHMORevenueData {
  getHMORevenueData: GetHMORevenueData_getHMORevenueData;
}

export interface GetHMORevenueDataVariables {
  filter?: FinanceAnalyticsFilter | null;
}
