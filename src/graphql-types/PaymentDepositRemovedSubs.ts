/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Currency } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: PaymentDepositRemovedSubs
// ====================================================

export interface PaymentDepositRemovedSubs_PaymentDepositRemoved {
  __typename: "PaymentDepositModel";
  id: string;
  amountDeposited: number;
  amountUsed: number;
  profileId: string;
  hospitalId: string;
  currency: Currency;
  autoGenerated: boolean | null;
  isManualRefund: boolean | null;
  amountRefunded: number | null;
}

export interface PaymentDepositRemovedSubs {
  PaymentDepositRemoved: PaymentDepositRemovedSubs_PaymentDepositRemoved;
}

export interface PaymentDepositRemovedSubsVariables {
  hospitalId: string;
  profileId: string;
}
