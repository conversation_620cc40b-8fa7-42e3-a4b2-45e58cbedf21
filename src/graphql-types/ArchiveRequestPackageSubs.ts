/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: ArchiveRequestPackageSubs
// ====================================================

export interface ArchiveRequestPackageSubs_RequestPackageArchived_serviceDetails {
  __typename: "ServiceDetailsFields";
  serviceType: string | null;
  serviceName: string | null;
}

export interface ArchiveRequestPackageSubs_RequestPackageArchived_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface ArchiveRequestPackageSubs_RequestPackageArchived {
  __typename: "RequestPackageModel";
  id: string;
  requestDate: any;
  packageName: string;
  priority: string | null;
  category: string | null;
  orderedBy: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  price: string;
  paymentType: string | null;
  patientType: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  serviceDetails: ArchiveRequestPackageSubs_RequestPackageArchived_serviceDetails[] | null;
  createdDate: any;
  updatedDate: any;
  profileId: string | null;
  profile: ArchiveRequestPackageSubs_RequestPackageArchived_profile | null;
}

export interface ArchiveRequestPackageSubs {
  RequestPackageArchived: ArchiveRequestPackageSubs_RequestPackageArchived[];
}

export interface ArchiveRequestPackageSubsVariables {
  profileId: string;
}
