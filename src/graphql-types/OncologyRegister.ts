/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: OncologyRegister
// ====================================================

export interface OncologyRegister_treatmentChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface OncologyRegister_treatmentChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: OncologyRegister_treatmentChart_periods_cycles[];
}

export interface OncologyRegister_treatmentChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: OncologyRegister_treatmentChart_periods[];
}

export interface OncologyRegister_therapyChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface OncologyRegister_therapyChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: OncologyRegister_therapyChart_periods_cycles[];
}

export interface OncologyRegister_therapyChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: OncologyRegister_therapyChart_periods[];
}

export interface OncologyRegister {
  __typename: "OncologyRegisterModel";
  id: string;
  creatorName: string | null;
  lastModifierName: string | null;
  treatmentChart: OncologyRegister_treatmentChart | null;
  therapyChart: OncologyRegister_therapyChart | null;
  createdDate: any;
  updatedDate: any;
}
