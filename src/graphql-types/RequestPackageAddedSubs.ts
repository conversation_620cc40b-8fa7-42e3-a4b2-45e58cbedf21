/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: RequestPackageAddedSubs
// ====================================================

export interface RequestPackageAddedSubs_RequestPackageAdded_serviceDetails {
  __typename: "ServiceDetailsFields";
  serviceType: string | null;
  serviceName: string | null;
}

export interface RequestPackageAddedSubs_RequestPackageAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface RequestPackageAddedSubs_RequestPackageAdded {
  __typename: "RequestPackageModel";
  id: string;
  requestDate: any;
  packageName: string;
  priority: string | null;
  category: string | null;
  orderedBy: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  price: string;
  paymentType: string | null;
  patientType: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  serviceDetails: RequestPackageAddedSubs_RequestPackageAdded_serviceDetails[] | null;
  createdDate: any;
  updatedDate: any;
  profileId: string | null;
  profile: RequestPackageAddedSubs_RequestPackageAdded_profile | null;
}

export interface RequestPackageAddedSubs {
  RequestPackageAdded: RequestPackageAddedSubs_RequestPackageAdded;
}

export interface RequestPackageAddedSubsVariables {
  profileId: string;
}
