/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FacilityStaffsAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetFacilityStaffsSummary
// ====================================================

export interface GetFacilityStaffsSummary_getFacilityStaffsSummary_staffSummary {
  __typename: "StaffSummaryType";
  hospitalName: string | null;
  hospitalId: string | null;
  totalActiveStaffs: number | null;
  totalFemaleStaffs: number | null;
  totalInActiveStaffs: number | null;
  totalMaleStaffs: number | null;
  totalStaffs: number | null;
}

export interface GetFacilityStaffsSummary_getFacilityStaffsSummary_rolesSummary {
  __typename: "RoleSummaryType";
  hospitalName: string | null;
  count: number | null;
}

export interface GetFacilityStaffsSummary_getFacilityStaffsSummary {
  __typename: "FacilityStaffsData";
  name: number | null;
  totalStaffs: number | null;
  totalMaleStaffs: number | null;
  totalFemaleStaffs: number | null;
  totalActiveStaffs: number | null;
  totalInActiveStaffs: number | null;
  totalRoles: number | null;
  staffSummary: GetFacilityStaffsSummary_getFacilityStaffsSummary_staffSummary[] | null;
  rolesSummary: GetFacilityStaffsSummary_getFacilityStaffsSummary_rolesSummary[] | null;
}

export interface GetFacilityStaffsSummary {
  getFacilityStaffsSummary: GetFacilityStaffsSummary_getFacilityStaffsSummary;
}

export interface GetFacilityStaffsSummaryVariables {
  filter?: FacilityStaffsAnalyticsFilter | null;
}
