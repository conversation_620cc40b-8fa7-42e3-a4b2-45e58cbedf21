/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PreauthorizationFilterInput, PreauthorizationStatus, Gender } from "./globalTypes";

// ====================================================
// GraphQL query operation: UserPreauthorizations
// ====================================================

export interface UserPreauthorizations_profile_preauthorizations_list_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UserPreauthorizations_profile_preauthorizations_list_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UserPreauthorizations_profile_preauthorizations_list_utilizations_flags {
  __typename: "FlagDto";
  flag: string | null;
  ruleId: string | null;
}

export interface UserPreauthorizations_profile_preauthorizations_list_utilizations_utilisationStatus {
  __typename: "ClaimsApprovalInputType";
  status: string | null;
  comment: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusDescription: string | null;
  serviceAmount: number | null;
  vettingGroup: string;
  creatorId: string;
  creatorName: string;
  createdDate: any;
  updatedDate: any | null;
}

export interface UserPreauthorizations_profile_preauthorizations_list_utilizations_transferFund_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  title: string | null;
}

export interface UserPreauthorizations_profile_preauthorizations_list_utilizations_transferFund {
  __typename: "TransferFundModel";
  id: string;
  amount: number;
  serviceChargeAmount: number | null;
  transferReference: string | null;
  destinationBankCode: string | null;
  destinationBankName: string | null;
  destinationAccountNumber: string | null;
  destinationAccountName: string | null;
  createdDate: any;
  originatorName: string | null;
  narration: string | null;
  createdBy: UserPreauthorizations_profile_preauthorizations_list_utilizations_transferFund_createdBy | null;
}

export interface UserPreauthorizations_profile_preauthorizations_list_utilizations_aiReason {
  __typename: "AIReason";
  reason: string | null;
  status: string | null;
}

export interface UserPreauthorizations_profile_preauthorizations_list_utilizations {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  medicationCategory: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  frequency: string | null;
  duration: string | null;
  birthCount: string | null;
  deliveryDateTime: any | null;
  gestationalAge: string | null;
  specialty: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusDescription: string | null;
  creatorName: string | null;
  createdDate: any;
  lastModifierName: string | null;
  updatedDate: any;
  /**
   * Service type from which this utilization is created
   */
  serviceName: string | null;
  confirmation: boolean | null;
  percentageCovered: number | null;
  amountCovered: number | null;
  paymentModel: string | null;
  autoApprovalSource: string | null;
  flags: UserPreauthorizations_profile_preauthorizations_list_utilizations_flags[] | null;
  utilisationStatus: UserPreauthorizations_profile_preauthorizations_list_utilizations_utilisationStatus[] | null;
  transferFundId: string | null;
  transferFund: UserPreauthorizations_profile_preauthorizations_list_utilizations_transferFund | null;
  aiReason: UserPreauthorizations_profile_preauthorizations_list_utilizations_aiReason | null;
}

export interface UserPreauthorizations_profile_preauthorizations_list_flags {
  __typename: "FlagDto";
  flag: string | null;
  ruleId: string | null;
}

export interface UserPreauthorizations_profile_preauthorizations_list_enrolleePhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UserPreauthorizations_profile_preauthorizations_list_profile_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  value: string | null;
  countryCode: string | null;
  countryName: string | null;
}

export interface UserPreauthorizations_profile_preauthorizations_list_profile_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  dateOfBirth: any | null;
}

export interface UserPreauthorizations_profile_preauthorizations_list_profile_user {
  __typename: "UserModel";
  id: string;
  phoneNumber: string | null;
  nonCorporateEmail: string | null;
}

export interface UserPreauthorizations_profile_preauthorizations_list_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
  gender: Gender | null;
  secondaryPhoneNumber: UserPreauthorizations_profile_preauthorizations_list_profile_secondaryPhoneNumber | null;
  personalInformation: UserPreauthorizations_profile_preauthorizations_list_profile_personalInformation | null;
  user: UserPreauthorizations_profile_preauthorizations_list_profile_user;
}

export interface UserPreauthorizations_profile_preauthorizations_list {
  __typename: "PreauthorisationModel";
  id: string;
  requestDateTime: any;
  visitId: string | null;
  /**
   * This is the claimiId from HMO Provider
   */
  claimId: string | null;
  batchNumber: string | null;
  requestedBy: string | null;
  provider: UserPreauthorizations_profile_preauthorizations_list_provider;
  serviceType: string;
  serviceTypeCode: string;
  treatmentStartDate: any | null;
  treatmentEndDate: any | null;
  serviceName: string | null;
  priority: string | null;
  diagnosis: UserPreauthorizations_profile_preauthorizations_list_diagnosis[] | null;
  claimStatus: string | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  presentingComplain: string | null;
  code: string | null;
  status: PreauthorizationStatus;
  facilityName: string | null;
  facilityAddress: string | null;
  rank: string | null;
  department: string | null;
  specialty: string | null;
  isExternalPlanType: boolean | null;
  externalPlanTypeId: string | null;
  utilizations: UserPreauthorizations_profile_preauthorizations_list_utilizations[] | null;
  totalQuantity: string;
  grandTotal: string;
  referredBy: string | null;
  referralCode: string | null;
  referredFrom: string | null;
  referredTo: string | null;
  profileId: string | null;
  flags: UserPreauthorizations_profile_preauthorizations_list_flags[] | null;
  enrolleeNumber: string | null;
  enrolleePhoneNumber: UserPreauthorizations_profile_preauthorizations_list_enrolleePhoneNumber | null;
  enrolleeEmail: string | null;
  singleVisitPACode: boolean;
  createdDate: any;
  responseDateTime: any | null;
  hospitalId: string;
  isCompleted: boolean | null;
  profile: UserPreauthorizations_profile_preauthorizations_list_profile | null;
}

export interface UserPreauthorizations_profile_preauthorizations {
  __typename: "PreauthorisationResponse";
  totalCount: number;
  list: UserPreauthorizations_profile_preauthorizations_list[];
}

export interface UserPreauthorizations_profile {
  __typename: "ProfileModel";
  id: string;
  preauthorizations: UserPreauthorizations_profile_preauthorizations | null;
}

export interface UserPreauthorizations {
  profile: UserPreauthorizations_profile;
}

export interface UserPreauthorizationsVariables {
  id: string;
  filterOptions?: PreauthorizationFilterInput | null;
}
