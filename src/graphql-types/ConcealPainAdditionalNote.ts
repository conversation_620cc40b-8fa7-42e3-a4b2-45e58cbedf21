/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealPainAdditionalNote
// ====================================================

export interface ConcealPainAdditionalNote_concealPainAdditionalNote {
  __typename: "PainModel";
  id: string;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
}

export interface ConcealPainAdditionalNote {
  concealPainAdditionalNote: ConcealPainAdditionalNote_concealPainAdditionalNote;
}

export interface ConcealPainAdditionalNoteVariables {
  id: string;
  concealStatus: boolean;
  clinifyId: string;
}
