/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { WalkInFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHospitalReferralList
// ====================================================

export interface GetHospitalReferralList_hospital_walkInReferrals_list_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface GetHospitalReferralList_hospital_walkInReferrals_list {
  __typename: "WalkInReferralModel";
  id: string;
  referralDateTime: any | null;
  referredBy: string | null;
  referralReason: string | null;
  concealReferralReason: boolean | null;
  referralFacilityName: string | null;
  referralFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: GetHospitalReferralList_hospital_walkInReferrals_list_patientInformation | null;
}

export interface GetHospitalReferralList_hospital_walkInReferrals {
  __typename: "WalkInReferralResponse";
  totalCount: number;
  list: GetHospitalReferralList_hospital_walkInReferrals_list[];
}

export interface GetHospitalReferralList_hospital {
  __typename: "HospitalModel";
  id: string;
  walkInReferrals: GetHospitalReferralList_hospital_walkInReferrals;
}

export interface GetHospitalReferralList {
  hospital: GetHospitalReferralList_hospital;
}

export interface GetHospitalReferralListVariables {
  filterOptions?: WalkInFilterInput | null;
  hospitalId?: string | null;
}
