/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: FeedbackUpdatedSubs
// ====================================================

export interface FeedbackUpdatedSubs_FeedbackUpdated_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface FeedbackUpdatedSubs_FeedbackUpdated_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface FeedbackUpdatedSubs_FeedbackUpdated {
  __typename: "FeedbackModel";
  id: string;
  feedbackDateTime: any | null;
  title: string;
  message: string;
  comment: string | null;
  category: string;
  isResolved: boolean | null;
  documentUrl: string[] | null;
  createdDate: any;
  creatorName: string | null;
  reviewerName: string | null;
  hospital: FeedbackUpdatedSubs_FeedbackUpdated_hospital | null;
  createdBy: FeedbackUpdatedSubs_FeedbackUpdated_createdBy;
}

export interface FeedbackUpdatedSubs {
  FeedbackUpdated: FeedbackUpdatedSubs_FeedbackUpdated;
}

export interface FeedbackUpdatedSubsVariables {
  hospitalId: string;
  hmoProviderId?: string | null;
}
