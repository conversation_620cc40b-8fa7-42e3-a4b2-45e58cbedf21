/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AdmissionAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetAdmissionByDepartment
// ====================================================

export interface GetAdmissionByDepartment_getAdmissionByDepartment {
  __typename: "CategoryDataResponse";
  category: string | null;
  count: number | null;
  name: number | null;
}

export interface GetAdmissionByDepartment {
  getAdmissionByDepartment: GetAdmissionByDepartment_getAdmissionByDepartment[];
}

export interface GetAdmissionByDepartmentVariables {
  filter?: AdmissionAnalyticsFilter | null;
}
