/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PayoutFilterInput, PayoutStatus, Currency, CommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL query operation: FetchRequestPayouts
// ====================================================

export interface FetchRequestPayouts_hospital_requestPayouts_list_receiverAccount {
  __typename: "BankAccountInformation";
  bankName: string;
  accountNumber: string;
  accountName: string | null;
  bankCode: string | null;
  accountType: string | null;
}

export interface FetchRequestPayouts_hospital_requestPayouts_list_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface FetchRequestPayouts_hospital_requestPayouts_list_virtualServicesPayments_bill_receiverProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface FetchRequestPayouts_hospital_requestPayouts_list_virtualServicesPayments_bill {
  __typename: "BillModel";
  id: string;
  billingDateTime: any | null;
  raisedBy: string | null;
  receiverProfile: FetchRequestPayouts_hospital_requestPayouts_list_virtualServicesPayments_bill_receiverProfile | null;
}

export interface FetchRequestPayouts_hospital_requestPayouts_list_virtualServicesPayments {
  __typename: "VirtualServicesPaymentModel";
  id: string;
  billId: string | null;
  bill: FetchRequestPayouts_hospital_requestPayouts_list_virtualServicesPayments_bill | null;
  amountDue: number;
  amountPaid: number;
  paymentStatus: string;
  commissionPayer: CommissionPayer | null;
  commissionFeeAmount: number | null;
  payoutId: string | null;
  payoutStatus: PayoutStatus;
  createdDate: any;
  updatedDate: any | null;
}

export interface FetchRequestPayouts_hospital_requestPayouts_list_invoicePayments_invoice_recipient {
  __typename: "InvoiceRecipient";
  phone: string | null;
  address: string | null;
  email: string | null;
  name: string;
  clinifyId: string | null;
}

export interface FetchRequestPayouts_hospital_requestPayouts_list_invoicePayments_invoice {
  __typename: "InvoiceModel";
  id: string;
  invoiceReference: string;
  issueDate: any;
  dueDate: any;
  recipient: FetchRequestPayouts_hospital_requestPayouts_list_invoicePayments_invoice_recipient;
}

export interface FetchRequestPayouts_hospital_requestPayouts_list_invoicePayments {
  __typename: "InvoicePaymentModel";
  id: string;
  paymentStatus: string;
  payoutStatus: PayoutStatus;
  amountDue: number;
  amountPaid: number;
  commissionFeeAmount: number | null;
  commissionFeePercentage: number | null;
  paymentMethod: string;
  invoice: FetchRequestPayouts_hospital_requestPayouts_list_invoicePayments_invoice;
}

export interface FetchRequestPayouts_hospital_requestPayouts_list_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface FetchRequestPayouts_hospital_requestPayouts_list_payout {
  __typename: "PayoutModel";
  id: string;
  amountPaid: number;
  payoutStatus: PayoutStatus;
}

export interface FetchRequestPayouts_hospital_requestPayouts_list {
  __typename: "RequestPayoutModel";
  id: string;
  requestPayoutDateTime: any;
  initiatedBy: string | null;
  createdDate: any;
  updatedDate: any;
  payoutStatus: PayoutStatus;
  payoutDescription: string | null;
  receiverAccount: FetchRequestPayouts_hospital_requestPayouts_list_receiverAccount;
  currency: Currency;
  hospitalId: string;
  receiverInitialWalletBalanceBeforePayout: number;
  receiverInitialWalletBalanceBeforeRequest: number;
  requestAmount: number;
  totalCommissionFeeAmount: number;
  transactionStartDate: any;
  transactionEndDate: any;
  creatorName: string | null;
  lastModifierName: string | null;
  createdBy: FetchRequestPayouts_hospital_requestPayouts_list_createdBy;
  virtualServicesPayments: FetchRequestPayouts_hospital_requestPayouts_list_virtualServicesPayments[];
  invoicePayments: FetchRequestPayouts_hospital_requestPayouts_list_invoicePayments[];
  hospital: FetchRequestPayouts_hospital_requestPayouts_list_hospital;
  payout: FetchRequestPayouts_hospital_requestPayouts_list_payout | null;
  additionalNote: string | null;
}

export interface FetchRequestPayouts_hospital_requestPayouts {
  __typename: "RequestPayoutListResponse";
  totalCount: number;
  list: FetchRequestPayouts_hospital_requestPayouts_list[];
}

export interface FetchRequestPayouts_hospital {
  __typename: "HospitalModel";
  id: string;
  requestPayouts: FetchRequestPayouts_hospital_requestPayouts | null;
}

export interface FetchRequestPayouts {
  hospital: FetchRequestPayouts_hospital;
}

export interface FetchRequestPayoutsVariables {
  filterOptions: PayoutFilterInput;
  hospitalId?: string | null;
}
