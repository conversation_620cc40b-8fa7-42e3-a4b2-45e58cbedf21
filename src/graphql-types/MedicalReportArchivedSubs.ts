/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicalReportStatus } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: MedicalReportArchivedSubs
// ====================================================

export interface MedicalReportArchivedSubs_MedicalReportArchived_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface MedicalReportArchivedSubs_MedicalReportArchived_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface MedicalReportArchivedSubs_MedicalReportArchived_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface MedicalReportArchivedSubs_MedicalReportArchived_reportType {
  __typename: "ReportTypeInputType";
  ref: string | null;
  name: string;
  itemId: string | null;
}

export interface MedicalReportArchivedSubs_MedicalReportArchived_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface MedicalReportArchivedSubs_MedicalReportArchived_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface MedicalReportArchivedSubs_MedicalReportArchived {
  __typename: "MedicalReportModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  updatedBy: MedicalReportArchivedSubs_MedicalReportArchived_updatedBy | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  createdBy: MedicalReportArchivedSubs_MedicalReportArchived_createdBy;
  creatorId: string | null;
  creatorName: string | null;
  profile: MedicalReportArchivedSubs_MedicalReportArchived_profile | null;
  reportDate: any | null;
  reportType: MedicalReportArchivedSubs_MedicalReportArchived_reportType[] | null;
  patientClinifyId: string | null;
  patientFullname: string | null;
  patientPhone: string | null;
  patientEmail: string | null;
  report: string[] | null;
  rank: string | null;
  department: string | null;
  doctorName: string | null;
  specialty: string | null;
  serviceDetails: MedicalReportArchivedSubs_MedicalReportArchived_serviceDetails[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  hospital: MedicalReportArchivedSubs_MedicalReportArchived_hospital | null;
  hospitalId: string | null;
  status: MedicalReportStatus | null;
}

export interface MedicalReportArchivedSubs {
  MedicalReportArchived: MedicalReportArchivedSubs_MedicalReportArchived;
}

export interface MedicalReportArchivedSubsVariables {
  hospitalId: string;
}
