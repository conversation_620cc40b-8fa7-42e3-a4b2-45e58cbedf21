/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PayoutStatus, CommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: AddInvoicePaymentSubs
// ====================================================

export interface AddInvoicePaymentSubs_InvoicePaymentAdded_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
}

export interface AddInvoicePaymentSubs_InvoicePaymentAdded_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
}

export interface AddInvoicePaymentSubs_InvoicePaymentAdded {
  __typename: "InvoicePaymentModel";
  id: string;
  invoiceId: string;
  amountPaid: number;
  paymentMethod: string;
  paymentStatus: string;
  amountDue: number;
  commissionFeeAmount: number | null;
  commissionFeePercentage: number | null;
  createdDate: any;
  updatedDate: any | null;
  payoutStatus: PayoutStatus;
  commissionPayer: CommissionPayer | null;
  discountAmount: number;
  createdBy: AddInvoicePaymentSubs_InvoicePaymentAdded_createdBy;
  updatedBy: AddInvoicePaymentSubs_InvoicePaymentAdded_updatedBy | null;
  creatorName: string;
  lastModifierName: string | null;
}

export interface AddInvoicePaymentSubs {
  InvoicePaymentAdded: AddInvoicePaymentSubs_InvoicePaymentAdded;
}

export interface AddInvoicePaymentSubsVariables {
  hospitalId: string;
  profileId: string;
}
