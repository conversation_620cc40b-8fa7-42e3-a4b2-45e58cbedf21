/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateInventoryClass
// ====================================================

export interface UpdateInventoryClass_updateInventoryClass {
  __typename: "FacilityPreferenceModel";
  id: string;
  showServiceDetails: boolean | null;
  rolesServiceDetailsIsHidden: string[] | null;
  inventoryClass: string | null;
  hospitalId: string;
}

export interface UpdateInventoryClass {
  updateInventoryClass: UpdateInventoryClass_updateInventoryClass;
}

export interface UpdateInventoryClassVariables {
  inventoryClass: string;
}
