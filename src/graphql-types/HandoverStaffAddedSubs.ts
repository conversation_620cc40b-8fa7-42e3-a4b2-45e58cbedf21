/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HandoverStaffAddedSubs
// ====================================================

export interface HandoverStaffAddedSubs_HandoverStaffAdded_staffProfile {
  __typename: "ProfileModel";
  fullName: string;
}

export interface HandoverStaffAddedSubs_HandoverStaffAdded {
  __typename: "HandoverStaffModel";
  id: string;
  status: string | null;
  currentShift: string | null;
  nextShift: string | null;
  staffProfileId: string | null;
  staffProfile: HandoverStaffAddedSubs_HandoverStaffAdded_staffProfile | null;
  handoverNoteId: string;
  createdDate: any;
  updatedDate: any;
  creatorId: string | null;
}

export interface HandoverStaffAddedSubs {
  HandoverStaffAdded: HandoverStaffAddedSubs_HandoverStaffAdded;
}

export interface HandoverStaffAddedSubsVariables {
  profileId: string;
  hospitalId?: string | null;
  userType: UserType;
}
