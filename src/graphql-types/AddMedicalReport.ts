/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NewMedicalReportInput, MedicalReportStatus, BillStatus } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddMedicalReport
// ====================================================

export interface AddMedicalReport_addMedicalReport_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface AddMedicalReport_addMedicalReport_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface AddMedicalReport_addMedicalReport_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface AddMedicalReport_addMedicalReport_reportType {
  __typename: "ReportTypeInputType";
  ref: string | null;
  name: string;
  itemId: string | null;
}

export interface AddMedicalReport_addMedicalReport_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface AddMedicalReport_addMedicalReport_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface AddMedicalReport_addMedicalReport_bill {
  __typename: "BillModel";
  id: string;
  billStatus: BillStatus;
  createdDate: any;
}

export interface AddMedicalReport_addMedicalReport {
  __typename: "MedicalReportModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  updatedBy: AddMedicalReport_addMedicalReport_updatedBy | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  createdBy: AddMedicalReport_addMedicalReport_createdBy;
  creatorId: string | null;
  creatorName: string | null;
  profile: AddMedicalReport_addMedicalReport_profile | null;
  reportDate: any | null;
  reportType: AddMedicalReport_addMedicalReport_reportType[] | null;
  patientClinifyId: string | null;
  patientFullname: string | null;
  patientPhone: string | null;
  patientEmail: string | null;
  report: string[] | null;
  rank: string | null;
  department: string | null;
  doctorName: string | null;
  specialty: string | null;
  serviceDetails: AddMedicalReport_addMedicalReport_serviceDetails[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  hospital: AddMedicalReport_addMedicalReport_hospital | null;
  hospitalId: string | null;
  status: MedicalReportStatus | null;
  bill: AddMedicalReport_addMedicalReport_bill | null;
  billStatus: string | null;
}

export interface AddMedicalReport {
  addMedicalReport: AddMedicalReport_addMedicalReport;
}

export interface AddMedicalReportVariables {
  input: NewMedicalReportInput;
  pin?: string | null;
}
