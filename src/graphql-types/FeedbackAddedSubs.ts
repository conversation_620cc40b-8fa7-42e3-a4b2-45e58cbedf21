/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: FeedbackAddedSubs
// ====================================================

export interface FeedbackAddedSubs_FeedbackAdded_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface FeedbackAddedSubs_FeedbackAdded_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface FeedbackAddedSubs_FeedbackAdded {
  __typename: "FeedbackModel";
  id: string;
  feedbackDateTime: any | null;
  title: string;
  message: string;
  comment: string | null;
  category: string;
  isResolved: boolean | null;
  documentUrl: string[] | null;
  createdDate: any;
  creatorName: string | null;
  reviewerName: string | null;
  hospital: FeedbackAddedSubs_FeedbackAdded_hospital | null;
  createdBy: FeedbackAddedSubs_FeedbackAdded_createdBy;
}

export interface FeedbackAddedSubs {
  FeedbackAdded: FeedbackAddedSubs_FeedbackAdded;
}

export interface FeedbackAddedSubsVariables {
  hospitalId: string;
  hmoProviderId?: string | null;
}
