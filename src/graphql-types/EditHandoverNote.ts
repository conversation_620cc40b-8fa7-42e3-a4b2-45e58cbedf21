/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EditHandoverNoteInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: EditHandoverNote
// ====================================================

export interface EditHandoverNote_updateHandoverNote_handoverBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface EditHandoverNote_updateHandoverNote {
  __typename: "HandoverNoteModel";
  id: string;
  name: string;
  handoverDateTime: any;
  updatedDate: any | null;
  createdDate: any;
  hospitalId: string;
  creatorId: string;
  department: string | null;
  handoverById: string;
  specialty: string | null;
  handoverBy: EditHandoverNote_updateHandoverNote_handoverBy;
  lastModifierName: string | null;
}

export interface EditHandoverNote {
  updateHandoverNote: EditHandoverNote_updateHandoverNote;
}

export interface EditHandoverNoteVariables {
  id: string;
  input: EditHandoverNoteInput;
}
