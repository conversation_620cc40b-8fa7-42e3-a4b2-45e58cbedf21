/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NextOfKinInput, Gender } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateNextOfKin
// ====================================================

export interface UpdateNextOfKin_updateNextOfKinInfo_phoneNumber {
  __typename: "PhoneNumberFields";
  countryName: string | null;
  countryCode: string | null;
  value: string | null;
}

export interface UpdateNextOfKin_updateNextOfKinInfo_phoneNumberAlt {
  __typename: "PhoneNumberFields";
  countryName: string | null;
  countryCode: string | null;
  value: string | null;
}

export interface UpdateNextOfKin_updateNextOfKinInfo_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface UpdateNextOfKin_updateNextOfKinInfo {
  __typename: "NextOfKinModel";
  id: string;
  firstName: string | null;
  lastName: string | null;
  gender: Gender | null;
  title: string | null;
  middleName: string | null;
  bloodGroup: string | null;
  genoType: string | null;
  phoneNumber: UpdateNextOfKin_updateNextOfKinInfo_phoneNumber | null;
  phoneNumberAlt: UpdateNextOfKin_updateNextOfKinInfo_phoneNumberAlt | null;
  email: string | null;
  emailAlt: string | null;
  relationship: string | null;
  occupation: string | null;
  address: string | null;
  profile: UpdateNextOfKin_updateNextOfKinInfo_profile | null;
}

export interface UpdateNextOfKin {
  updateNextOfKinInfo: UpdateNextOfKin_updateNextOfKinInfo;
}

export interface UpdateNextOfKinVariables {
  input: NextOfKinInput;
  id: string;
}
