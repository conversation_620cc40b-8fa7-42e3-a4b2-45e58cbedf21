/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: PostnatalUpdatedSubs
// ====================================================

export interface PostnatalUpdatedSubs_PostnatalUpdated_clinicalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface PostnatalUpdatedSubs_PostnatalUpdated_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  paymentType: string | null;
  patientType: string | null;
  reference: string | null;
}

export interface PostnatalUpdatedSubs_PostnatalUpdated_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface PostnatalUpdatedSubs_PostnatalUpdated_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: PostnatalUpdatedSubs_PostnatalUpdated_profile_coverageDetails_provider | null;
}

export interface PostnatalUpdatedSubs_PostnatalUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: PostnatalUpdatedSubs_PostnatalUpdated_profile_coverageDetails[] | null;
}

export interface PostnatalUpdatedSubs_PostnatalUpdated_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface PostnatalUpdatedSubs_PostnatalUpdated_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: PostnatalUpdatedSubs_PostnatalUpdated_preauthorizationDetails_provider | null;
}

export interface PostnatalUpdatedSubs_PostnatalUpdated {
  __typename: "PostnatalModel";
  id: string;
  provider: string | null;
  visitationDateTime: any | null;
  seenBy: string | null;
  deliveryDate: any | null;
  hasMenstruationStarted: string | null;
  menstruationStartDate: any | null;
  uterus: string | null;
  breastFeeding: string | null;
  familyPlanning: string | null;
  familyPlanningMethod: string | null;
  contraceptivePillCount: string | null;
  typeOfImplant: string | null;
  nameOfInjectable: string | null;
  firstTimeFpUser: string | null;
  sourceOfReferral: string | null;
  familyPlanningClientType: string | null;
  vulva: string | null;
  vitaminAGivenToBaby: string | null;
  vitaminADoseGivenToBaby: string | null;
  vitaminADoseUnitGivenToBaby: string | null;
  babyComplaint: string | null;
  stateBabyComplaint: string | null;
  visitationNote: string | null;
  concealVisitationNote: boolean | null;
  treatmentPlan: string | null;
  concealTreatmentPlan: boolean | null;
  clinicalDiagnosis: PostnatalUpdatedSubs_PostnatalUpdated_clinicalDiagnosis[] | null;
  rank: string | null;
  department: string | null;
  healthEducation: string | null;
  concealHealthEducation: boolean | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  postpartumDepression: string | null;
  appointmentId: string | null;
  hmoProviderId: string | null;
  serviceDetails: PostnatalUpdatedSubs_PostnatalUpdated_serviceDetails[] | null;
  isPackage: boolean;
  billStatus: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  hospitalId: string | null;
  profileId: string | null;
  profile: PostnatalUpdatedSubs_PostnatalUpdated_profile | null;
  preauthorizationDetails: PostnatalUpdatedSubs_PostnatalUpdated_preauthorizationDetails | null;
}

export interface PostnatalUpdatedSubs {
  PostnatalUpdated: PostnatalUpdatedSubs_PostnatalUpdated;
}

export interface PostnatalUpdatedSubsVariables {
  profileId: string;
}
