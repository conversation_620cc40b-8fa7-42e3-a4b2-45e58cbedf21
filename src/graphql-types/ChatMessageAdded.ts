/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MessageType, MessageStatus } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: ChatMessageAdded
// ====================================================

export interface ChatMessageAdded_ChatMessageAdded {
  __typename: "ChatMessageModel";
  id: string;
  createdDate: any;
  senderId: string;
  messageType: MessageType;
  content: string;
  status: MessageStatus;
  conversationId: string;
  senderName: string;
  facilityName: string | null;
}

export interface ChatMessageAdded {
  ChatMessageAdded: ChatMessageAdded_ChatMessageAdded;
}
