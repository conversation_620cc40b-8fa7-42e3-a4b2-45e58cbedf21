/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UndoProfileMerge
// ====================================================

export interface UndoProfileMerge_undoProfileMerge_targetProfile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface UndoProfileMerge_undoProfileMerge_sourceProfiles {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface UndoProfileMerge_undoProfileMerge {
  __typename: "MergedProfilesModel";
  id: string;
  targetProfile: UndoProfileMerge_undoProfileMerge_targetProfile;
  sourceProfiles: UndoProfileMerge_undoProfileMerge_sourceProfiles[];
}

export interface UndoProfileMerge {
  undoProfileMerge: UndoProfileMerge_undoProfileMerge;
}

export interface UndoProfileMergeVariables {
  clinifyId: string;
}
