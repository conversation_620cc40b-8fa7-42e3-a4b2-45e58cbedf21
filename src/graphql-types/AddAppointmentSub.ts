/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NextAppointmentEntities } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: AddAppointmentSub
// ====================================================

export interface AddAppointmentSub_AppointmentAdded_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface AddAppointmentSub_AppointmentAdded_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface AddAppointmentSub_AppointmentAdded_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface AddAppointmentSub_AppointmentAdded_specialist {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  type: string;
  fullName: string;
}

export interface AddAppointmentSub_AppointmentAdded_serviceDetails {
  __typename: "ServiceDetailInputType";
  type: string | null;
  name: string | null;
}

export interface AddAppointmentSub_AppointmentAdded {
  __typename: "OrganisationAppointmentModel";
  id: string;
  hospital: AddAppointmentSub_AppointmentAdded_hospital | null;
  profile: AddAppointmentSub_AppointmentAdded_profile | null;
  patientInformation: AddAppointmentSub_AppointmentAdded_patientInformation | null;
  specialist: AddAppointmentSub_AppointmentAdded_specialist | null;
  createdDate: any;
  updatedDate: any;
  status: string | null;
  category: string | null;
  liveSessionUrl: string | null;
  rank: string | null;
  reason: string | null;
  appointmentDateTime: any | null;
  startDateTime: any | null;
  endDateTime: any | null;
  specialty: string | null;
  role: string | null;
  paymentType: string | null;
  serviceDetails: AddAppointmentSub_AppointmentAdded_serviceDetails[] | null;
  patientType: string | null;
  duration: string | null;
  confirmedBy: string | null;
  deliveryMethod: string | null;
  urgency: string | null;
  additionalNote: string | null;
  recordType: NextAppointmentEntities | null;
  documentUrl: string[] | null;
}

export interface AddAppointmentSub {
  AppointmentAdded: AddAppointmentSub_AppointmentAdded;
}

export interface AddAppointmentSubVariables {
  profileId: string;
  hospitalId: string;
}
