/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: FacilityBranchFragment
// ====================================================

export interface FacilityBranchFragment_phoneNumber {
  __typename: "PhoneNumberFields";
  value: string | null;
  countryCode: string | null;
  countryName: string | null;
}

export interface FacilityBranchFragment_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface FacilityBranchFragment_orgAdmin_personalInformation {
  __typename: "PersonalInformation";
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
}

export interface FacilityBranchFragment_orgAdmin {
  __typename: "ProfileModel";
  id: string;
  title: string | null;
  personalInformation: FacilityBranchFragment_orgAdmin_personalInformation | null;
}

export interface FacilityBranchFragment_preference {
  __typename: "FacilityPreferencePublic";
  id: string | null;
  useHQFacilityInventory: boolean | null;
  useHQFacilityTariffs: boolean | null;
}

export interface FacilityBranchFragment {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  supportMail: string | null;
  website: string | null;
  address: string | null;
  phoneNumber: FacilityBranchFragment_phoneNumber | null;
  secondaryPhoneNumber: FacilityBranchFragment_secondaryPhoneNumber | null;
  /**
   * Organization administrator
   */
  orgAdmin: FacilityBranchFragment_orgAdmin;
  preference: FacilityBranchFragment_preference;
}
