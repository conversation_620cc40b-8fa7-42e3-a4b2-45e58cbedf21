/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmoClaimFilterInput, UserType } from "./globalTypes";

// ====================================================
// GraphQL query operation: HospitalHmoClaims
// ====================================================

export interface HospitalHmoClaims_hospital_preferredPayoutAccount {
  __typename: "BankAccountDetailsResponse";
  bankName: string;
  accountNumber: string;
  accountName: string | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_flags {
  __typename: "FlagDto";
  flag: string | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_utilizations_flags {
  __typename: "FlagDto";
  flag: string | null;
  ruleId: string | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_utilizations_utilisationStatus {
  __typename: "ClaimsApprovalInputType";
  status: string | null;
  comment: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusDescription: string | null;
  serviceAmount: number | null;
  vettingGroup: string;
  creatorId: string;
  creatorName: string;
  createdDate: any;
  updatedDate: any | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_utilizations_transferFund_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  title: string | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_utilizations_transferFund {
  __typename: "TransferFundModel";
  id: string;
  amount: number;
  serviceChargeAmount: number | null;
  transferReference: string | null;
  destinationBankCode: string | null;
  destinationBankName: string | null;
  destinationAccountNumber: string | null;
  destinationAccountName: string | null;
  createdDate: any;
  originatorName: string | null;
  narration: string | null;
  createdBy: HospitalHmoClaims_hospital_hmoClaims_list_utilizations_transferFund_createdBy | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_utilizations_aiReason {
  __typename: "AIReason";
  reason: string | null;
  status: string | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_utilizations {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  medicationCategory: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  frequency: string | null;
  duration: string | null;
  birthCount: string | null;
  deliveryDateTime: any | null;
  gestationalAge: string | null;
  specialty: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusDescription: string | null;
  creatorName: string | null;
  createdDate: any;
  lastModifierName: string | null;
  updatedDate: any;
  /**
   * Service type from which this utilization is created
   */
  serviceName: string | null;
  confirmation: boolean | null;
  percentageCovered: number | null;
  amountCovered: number | null;
  paymentModel: string | null;
  autoApprovalSource: string | null;
  flags: HospitalHmoClaims_hospital_hmoClaims_list_utilizations_flags[] | null;
  utilisationStatus: HospitalHmoClaims_hospital_hmoClaims_list_utilizations_utilisationStatus[] | null;
  transferFundId: string | null;
  transferFund: HospitalHmoClaims_hospital_hmoClaims_list_utilizations_transferFund | null;
  aiReason: HospitalHmoClaims_hospital_hmoClaims_list_utilizations_aiReason | null;
  paUtilProcessed: boolean | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_enrolleePhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_revertAction {
  __typename: "RevertActionInputType";
  action: string | null;
  mutatorAlias: string | null;
  receiverAlias: string | null;
  mutatorUserType: UserType | null;
  receiverUserType: UserType | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_payoutHistory {
  __typename: "HmoClaimPayoutItem";
  createdDate: any | null;
  amount: number | null;
  clinifyId: string | null;
  payoutId: string | null;
  utilizationIds: string[] | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list_financeApproval {
  __typename: "ClaimsAccountApprovalType";
  createdDate: any;
  creatorId: string;
  creatorName: string;
  approvalGroup: string;
}

export interface HospitalHmoClaims_hospital_hmoClaims_list {
  __typename: "HmoClaimModel";
  id: string;
  claimId: string | null;
  visitId: string | null;
  batchNumber: string | null;
  claimDate: any | null;
  submitDateTime: any | null;
  submittedBy: string | null;
  treatmentStartDate: any | null;
  treatmentEndDate: any | null;
  confirmation: string | null;
  provider: HospitalHmoClaims_hospital_hmoClaims_list_provider;
  serviceType: string;
  serviceTypeCode: string;
  serviceName: string | null;
  priority: string | null;
  claimIdentity: string | null;
  status: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  presentingComplain: string | null;
  externalPlanTypeId: string | null;
  isExternalPlanType: boolean | null;
  flags: HospitalHmoClaims_hospital_hmoClaims_list_flags[] | null;
  diagnosis: HospitalHmoClaims_hospital_hmoClaims_list_diagnosis[] | null;
  utilizations: HospitalHmoClaims_hospital_hmoClaims_list_utilizations[] | null;
  documentUrl: string[] | null;
  totalQuantity: string;
  grandTotal: string;
  additionalNote: string | null;
  creatorName: string | null;
  lastModifierName: string | null;
  updatedDate: any;
  referredBy: string | null;
  referralCode: string | null;
  referredFrom: string | null;
  referredTo: string | null;
  profileId: string | null;
  enrolleeNumber: string | null;
  hospitalId: string;
  createdDate: any;
  responseDateTime: any | null;
  totalRejectedAmount: string;
  totalSubmittedAmount: string;
  enrolleePhoneNumber: HospitalHmoClaims_hospital_hmoClaims_list_enrolleePhoneNumber | null;
  revertAction: HospitalHmoClaims_hospital_hmoClaims_list_revertAction | null;
  enrolleeEmail: string | null;
  profile: HospitalHmoClaims_hospital_hmoClaims_list_profile | null;
  payoutStatus: string | null;
  payoutHistory: HospitalHmoClaims_hospital_hmoClaims_list_payoutHistory[] | null;
  financeApproval: HospitalHmoClaims_hospital_hmoClaims_list_financeApproval[] | null;
}

export interface HospitalHmoClaims_hospital_hmoClaims {
  __typename: "HmoClaimResponse";
  totalCount: number;
  list: HospitalHmoClaims_hospital_hmoClaims_list[];
}

export interface HospitalHmoClaims_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
  website: string | null;
  facebook: string | null;
  twitter: string | null;
  instagram: string | null;
  supportMail: string | null;
  facilityLogo: string | null;
  preferredPayoutAccount: HospitalHmoClaims_hospital_preferredPayoutAccount | null;
  hmoClaims: HospitalHmoClaims_hospital_hmoClaims | null;
}

export interface HospitalHmoClaims {
  hospital: HospitalHmoClaims_hospital;
}

export interface HospitalHmoClaimsVariables {
  filterOptions?: HmoClaimFilterInput | null;
  hospitalId?: string | null;
  hid?: string | null;
}
