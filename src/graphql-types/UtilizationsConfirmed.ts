/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UtilizationsConfirmed
// ====================================================

export interface UtilizationsConfirmed_UtilizationsConfirmed {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  confirmation: boolean | null;
  hmoClaimId: string;
}

export interface UtilizationsConfirmed {
  UtilizationsConfirmed: UtilizationsConfirmed_UtilizationsConfirmed[];
}

export interface UtilizationsConfirmedVariables {
  hospitalId: string;
}
