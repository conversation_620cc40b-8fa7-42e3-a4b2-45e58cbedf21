/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealAudiometry
// ====================================================

export interface ConcealAudiometry_concealAudiometry {
  __typename: "ConsultationModel";
  id: string;
  concealAudiometry: boolean | null;
  audiometry: string | null;
}

export interface ConcealAudiometry {
  concealAudiometry: ConcealAudiometry_concealAudiometry;
}

export interface ConcealAudiometryVariables {
  id: string;
  concealStatus: boolean;
}
