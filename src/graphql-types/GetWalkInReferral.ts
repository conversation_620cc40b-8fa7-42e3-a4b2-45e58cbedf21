/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetWalkInReferral
// ====================================================

export interface GetWalkInReferral_walkInReferral_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface GetWalkInReferral_walkInReferral {
  __typename: "WalkInReferralModel";
  id: string;
  referralDateTime: any | null;
  referredBy: string | null;
  referralReason: string | null;
  concealReferralReason: boolean | null;
  referralFacilityName: string | null;
  referralFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: GetWalkInReferral_walkInReferral_patientInformation | null;
}

export interface GetWalkInReferral {
  walkInReferral: GetWalkInReferral_walkInReferral;
}

export interface GetWalkInReferralVariables {
  id: string;
}
