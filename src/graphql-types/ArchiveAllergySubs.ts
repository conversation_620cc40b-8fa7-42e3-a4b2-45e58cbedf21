/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: ArchiveAllergySubs
// ====================================================

export interface ArchiveAllergySubs_AllergyArchived_details_clinicalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface ArchiveAllergySubs_AllergyArchived_details {
  __typename: "AllergyDetails";
  type: string;
  trigger: string;
  reactions: string[] | null;
  severeness: string | null;
  clinicalDiagnosis: ArchiveAllergySubs_AllergyArchived_details_clinicalDiagnosis[] | null;
}

export interface ArchiveAllergySubs_AllergyArchived_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface ArchiveAllergySubs_AllergyArchived_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface ArchiveAllergySubs_AllergyArchived {
  __typename: "AllergyModel";
  id: string;
  occurenceDate: any | null;
  duration: string | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  doctorName: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  details: ArchiveAllergySubs_AllergyArchived_details[] | null;
  profileId: string | null;
  profile: ArchiveAllergySubs_AllergyArchived_profile | null;
  documentUrl: string[] | null;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
  createdDate: any;
  updatedDate: any;
  medications: ArchiveAllergySubs_AllergyArchived_medications[];
}

export interface ArchiveAllergySubs {
  AllergyArchived: ArchiveAllergySubs_AllergyArchived[];
}

export interface ArchiveAllergySubsVariables {
  profileId: string;
}
