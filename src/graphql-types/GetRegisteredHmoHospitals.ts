/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmoFilterOptions, HospitalPlan, PlanStatus } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetRegisteredHmoHospitals
// ====================================================

export interface GetRegisteredHmoHospitals_getRegisteredHmoHospitals_list_hmoHospitals {
  __typename: "HmoHospitalModel";
  id: string;
  enrolleeCount: number | null;
  enrolleeLimit: number | null;
}

export interface GetRegisteredHmoHospitals_getRegisteredHmoHospitals_list {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  plan: HospitalPlan;
  address: string | null;
  lga: string | null;
  planStatus: PlanStatus;
  hmoHospitals: GetRegisteredHmoHospitals_getRegisteredHmoHospitals_list_hmoHospitals[] | null;
}

export interface GetRegisteredHmoHospitals_getRegisteredHmoHospitals {
  __typename: "HmoHospitalResponse";
  totalCount: number;
  list: GetRegisteredHmoHospitals_getRegisteredHmoHospitals_list[];
}

export interface GetRegisteredHmoHospitals {
  getRegisteredHmoHospitals: GetRegisteredHmoHospitals_getRegisteredHmoHospitals;
}

export interface GetRegisteredHmoHospitalsVariables {
  filterOptions: HmoFilterOptions;
}
