/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HandoverNoteUpdatedSubs
// ====================================================

export interface HandoverNoteUpdatedSubs_HandoverNoteUpdated_handoverBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface HandoverNoteUpdatedSubs_HandoverNoteUpdated {
  __typename: "HandoverNoteModel";
  id: string;
  name: string;
  handoverDateTime: any;
  updatedDate: any | null;
  createdDate: any;
  hospitalId: string;
  creatorId: string;
  department: string | null;
  handoverById: string;
  specialty: string | null;
  handoverBy: HandoverNoteUpdatedSubs_HandoverNoteUpdated_handoverBy;
  lastModifierName: string | null;
}

export interface HandoverNoteUpdatedSubs {
  HandoverNoteUpdated: HandoverNoteUpdatedSubs_HandoverNoteUpdated;
}

export interface HandoverNoteUpdatedSubsVariables {
  profileId: string;
  hospitalId?: string | null;
  userType: UserType;
}
