/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ProcessReferralUtilStatus
// ====================================================

export interface ProcessReferralUtilStatus_updateReferralUtilStatus_statusHistory {
  __typename: "ReferalStatusHistoryType";
  status: string;
  creatorName: string;
  createdDate: any;
}

export interface ProcessReferralUtilStatus_updateReferralUtilStatus {
  __typename: "PreAuthReferralUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rhesusFactor: string | null;
  specialty: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusHistory: ProcessReferralUtilStatus_updateReferralUtilStatus_statusHistory[] | null;
}

export interface ProcessReferralUtilStatus {
  updateReferralUtilStatus: ProcessReferralUtilStatus_updateReferralUtilStatus[];
}

export interface ProcessReferralUtilStatusVariables {
  referralCode: string;
  status: string;
  rejectionReason: string[];
  specifyReasonForRejection: string;
}
