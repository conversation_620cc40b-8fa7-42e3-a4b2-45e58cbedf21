/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UnarchiveVitalSubs
// ====================================================

export interface UnarchiveVitalSubs_VitalsUnarchived_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface UnarchiveVitalSubs_VitalsUnarchived {
  __typename: "VitalModel";
  id: string | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  profileId: string | null;
  profile: UnarchiveVitalSubs_VitalsUnarchived_profile | null;
}

export interface UnarchiveVitalSubs {
  VitalsUnarchived: UnarchiveVitalSubs_VitalsUnarchived[];
}

export interface UnarchiveVitalSubsVariables {
  profileId: string;
}
