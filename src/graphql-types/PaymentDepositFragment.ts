/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Currency } from "./globalTypes";

// ====================================================
// GraphQL fragment: PaymentDepositFragment
// ====================================================

export interface PaymentDepositFragment {
  __typename: "PaymentDepositModel";
  id: string;
  depositDate: any;
  depositMethod: string | null;
  currency: Currency;
  profileId: string;
  hospitalId: string;
  amountDeposited: number;
  amountUsed: number;
  additionalNote: string | null;
  collectedById: string | null;
  creatorId: string;
  lastModifierId: string | null;
  description: string | null;
  createdDate: any;
  updatedDate: any | null;
  autoGenerated: boolean | null;
  amountRefunded: number | null;
  finalDepositBalance: number | null;
  initialDepositBalance: number | null;
  isManualRefund: boolean | null;
  refundDate: any | null;
  refundedBy: string | null;
}
