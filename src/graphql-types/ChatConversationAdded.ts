/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MessageType, MessageStatus } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: ChatConversationAdded
// ====================================================

export interface ChatConversationAdded_ChatConversationAdded_lastMessage {
  __typename: "ChatMessageModel";
  id: string;
  createdDate: any;
  senderId: string;
  messageType: MessageType;
  content: string;
  status: MessageStatus;
  conversationId: string;
  senderName: string;
  facilityName: string | null;
}

export interface ChatConversationAdded_ChatConversationAdded_opponent_personalInformation {
  __typename: "PersonalInformation";
  displayPictureUrl: string | null;
}

export interface ChatConversationAdded_ChatConversationAdded_opponent {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  personalInformation: ChatConversationAdded_ChatConversationAdded_opponent_personalInformation | null;
}

export interface ChatConversationAdded_ChatConversationAdded {
  __typename: "ChatConversationModel";
  id: string;
  createdDate: any;
  participant1Id: string;
  participant2Id: string;
  participant1HospitalId: string | null;
  participant2HospitalId: string | null;
  participant1HospitalName: string | null;
  participant2HospitalName: string | null;
  hmoProviderId: string | null;
  unreadMessageCount: number | null;
  lastMessage: ChatConversationAdded_ChatConversationAdded_lastMessage | null;
  opponent: ChatConversationAdded_ChatConversationAdded_opponent | null;
}

export interface ChatConversationAdded {
  ChatConversationAdded: ChatConversationAdded_ChatConversationAdded;
}
