/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { fetchAppointmentFilterInput, NextAppointmentEntities } from "./globalTypes";

// ====================================================
// GraphQL query operation: FetchAppointment
// ====================================================

export interface FetchAppointment_appointment_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface FetchAppointment_appointment_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface FetchAppointment_appointment_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface FetchAppointment_appointment_specialist {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  type: string;
  fullName: string;
}

export interface FetchAppointment_appointment_serviceDetails {
  __typename: "ServiceDetailInputType";
  type: string | null;
  name: string | null;
}

export interface FetchAppointment_appointment {
  __typename: "OrganisationAppointmentModel";
  id: string;
  hospital: FetchAppointment_appointment_hospital | null;
  profile: FetchAppointment_appointment_profile | null;
  patientInformation: FetchAppointment_appointment_patientInformation | null;
  specialist: FetchAppointment_appointment_specialist | null;
  createdDate: any;
  updatedDate: any;
  status: string | null;
  category: string | null;
  liveSessionUrl: string | null;
  rank: string | null;
  reason: string | null;
  appointmentDateTime: any | null;
  startDateTime: any | null;
  endDateTime: any | null;
  specialty: string | null;
  role: string | null;
  paymentType: string | null;
  serviceDetails: FetchAppointment_appointment_serviceDetails[] | null;
  patientType: string | null;
  duration: string | null;
  confirmedBy: string | null;
  deliveryMethod: string | null;
  urgency: string | null;
  additionalNote: string | null;
  recordType: NextAppointmentEntities | null;
  documentUrl: string[] | null;
}

export interface FetchAppointment {
  appointment: FetchAppointment_appointment;
}

export interface FetchAppointmentVariables {
  id: string;
  filterInput?: fetchAppointmentFilterInput | null;
}
