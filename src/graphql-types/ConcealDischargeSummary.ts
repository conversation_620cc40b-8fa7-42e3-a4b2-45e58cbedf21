/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealDischargeSummary
// ====================================================

export interface ConcealDischargeSummary_concealDischargeSummary {
  __typename: "DischargePatientModel";
  id: string;
  concealDischargeSummary: boolean | null;
  dischargeSummary: string | null;
}

export interface ConcealDischargeSummary {
  concealDischargeSummary: ConcealDischargeSummary_concealDischargeSummary;
}

export interface ConcealDischargeSummaryVariables {
  id: string;
  concealStatus: boolean;
}
