/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdateEmployeeInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateEmployees
// ====================================================

export interface UpdateEmployees_updateEmployees_hmoPlanType_premiumDetails {
  __typename: "PremiumDetails";
  frequency: string | null;
  category: string | null;
  amount: number | null;
}

export interface UpdateEmployees_updateEmployees_hmoPlanType {
  __typename: "HmoPlanTypeModel";
  id: string;
  name: string | null;
  planCode: string | null;
  premiumDetails: UpdateEmployees_updateEmployees_hmoPlanType_premiumDetails[] | null;
}

export interface UpdateEmployees_updateEmployees {
  __typename: "EmployeeModel";
  id: string;
  title: string | null;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  jobTitle: string | null;
  department: string | null;
  employeeId: string | null;
  planCategory: string | null;
  employeeType: string | null;
  hmoPlanType: UpdateEmployees_updateEmployees_hmoPlanType | null;
  hmoPlanTypeId: string | null;
  paymentFrequency: string | null;
  paymentDate: any | null;
  planStartDate: any | null;
  planDueDate: any | null;
  employerId: string;
  enrolledBy: string | null;
  createdDate: any;
  updatedDate: any;
  creatorName: string | null;
  lastModifierName: string | null;
  creatorId: string | null;
  lastModifierId: string | null;
  displayPictureUrl: string | null;
}

export interface UpdateEmployees {
  updateEmployees: UpdateEmployees_updateEmployees[];
}

export interface UpdateEmployeesVariables {
  input: UpdateEmployeeInput[];
}
