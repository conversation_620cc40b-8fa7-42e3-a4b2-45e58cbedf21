/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PulseRateUpdatedSubs
// ====================================================

export interface PulseRateUpdatedSubs_PulseRateUpdated {
  __typename: "PulseRateModel";
  id: string;
  readingDateTime: any | null;
  reading: string | null;
  checkMethod: string | null;
  checkMethodSpecify: string | null;
  rhythm: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
}

export interface PulseRateUpdatedSubs {
  PulseRateUpdated: PulseRateUpdatedSubs_PulseRateUpdated;
}

export interface PulseRateUpdatedSubsVariables {
  profileId: string;
}
