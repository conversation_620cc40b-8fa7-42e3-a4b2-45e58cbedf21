/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { TransferPatientInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateTransferPatient
// ====================================================

export interface UpdateTransferPatient_updateTransferPatient_transferHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  clinifyId: string | null;
}

export interface UpdateTransferPatient_updateTransferPatient {
  __typename: "TransferPatientModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  roomOption: string | null;
  transferSource: string | null;
  roomInventoryId: string | null;
  transferHospital: UpdateTransferPatient_updateTransferPatient_transferHospital | null;
}

export interface UpdateTransferPatient {
  updateTransferPatient: UpdateTransferPatient_updateTransferPatient;
}

export interface UpdateTransferPatientVariables {
  input: TransferPatientInput;
  id: string;
  clinifyId: string;
  pin?: string | null;
}
