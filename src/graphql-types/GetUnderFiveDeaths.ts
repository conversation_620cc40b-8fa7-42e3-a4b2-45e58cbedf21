/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DeathsAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetUnderFiveDeaths
// ====================================================

export interface GetUnderFiveDeaths_getUnderFiveDeaths_byCauseOfDeath {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetUnderFiveDeaths_getUnderFiveDeaths {
  __typename: "DeathsSummary";
  name: number | null;
  totalFemale: number | null;
  totalMale: number | null;
  byCauseOfDeath: GetUnderFiveDeaths_getUnderFiveDeaths_byCauseOfDeath[] | null;
}

export interface GetUnderFiveDeaths {
  getUnderFiveDeaths: GetUnderFiveDeaths_getUnderFiveDeaths;
}

export interface GetUnderFiveDeathsVariables {
  filter?: DeathsAnalyticsFilter | null;
}
