/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NursingServicesFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPatientCancerScreenings
// ====================================================

export interface GetPatientCancerScreenings_profile_cancerScreening_list_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface GetPatientCancerScreenings_profile_cancerScreening_list {
  __typename: "CancerScreeningModel";
  id: string;
  screeningToBeDone: string | null;
  screeningType: string | null;
  seenRiskAssessmentForm: string | null;
  mammogramDate: any | null;
  breastUltrasoundDate: any | null;
  cervicalScreeningDate: any | null;
  clinicalBreastExaminationDate: any | null;
  digitalRectalExaminationDate: any | null;
  prostrateSpecificAntigenDate: any | null;
  faecalImmunochemicalOccultDate: any | null;
  colonoscopyDateTime: any | null;
  hpvDnaDate: any | null;
  lastHpvVaccineDate: any | null;
  otherScreeningInformation: string | null;
  lastMenstrualPeriod: any | null;
  existingPastMedicalCondition: string | null;
  currentDrugs: string | null;
  pregnancies: string | null;
  terminationsOrMiscarriages: string | null;
  contraceptiveMethod: string | null;
  tubalLitigation: string | null;
  vasectomy: string | null;
  pastBiopsies: string | null;
  otherMedicalInformation: string | null;
  pastResultsAvailable: string | null;
  specifyPastResult: string | null;
  procedureAndFindingDocumented: string | null;
  laboratoryFormRequested: string | null;
  planOrRecommendations: string | null;
  otherComments: string | null;
  followUpAppointmentWithResult: string | null;
  recallAppointment: string[] | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean;
  documentUrl: string[] | null;
  profileId: string | null;
  profile: GetPatientCancerScreenings_profile_cancerScreening_list_profile | null;
}

export interface GetPatientCancerScreenings_profile_cancerScreening {
  __typename: "CancerScreeningResponse";
  totalCount: number;
  list: GetPatientCancerScreenings_profile_cancerScreening_list[];
}

export interface GetPatientCancerScreenings_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  /**
   * Get patient cancer screening checklists
   */
  cancerScreening: GetPatientCancerScreenings_profile_cancerScreening;
}

export interface GetPatientCancerScreenings {
  profile: GetPatientCancerScreenings_profile;
}

export interface GetPatientCancerScreeningsVariables {
  filterOptions?: NursingServicesFilterInput | null;
  id: string;
}
