/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AnaesthesiaInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateAnaesthesiaChecklist
// ====================================================

export interface UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_drugsGiven {
  __typename: "DrugGivenType";
  readingDate: any | null;
  time: string | null;
  drugName: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  routeOfAdministration: string | null;
}

export interface UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_pulseAndBp {
  __typename: "PulseAndBpType";
  readingDate: any | null;
  time: string | null;
  pulse: string | null;
  bpSystolic: string | null;
  bpDiastolic: string | null;
}

export interface UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_eventTimeGraph {
  __typename: "EventTimeType";
  readingDate: any | null;
  eventName: string | null;
  time: any | null;
}

export interface UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_oxygenLevel {
  __typename: "OxygenLevelDataType";
  readingDate: any | null;
  time: string | null;
  ecgRythm: string | null;
  saturated: string | null;
  airFGF: string | null;
  endTidal: string | null;
  oxygenFi: string | null;
  agentFe: string | null;
  airwayPressure: string | null;
  airwayPressureUnit: string | null;
}

export interface UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_additionalOxygenLevel_value {
  __typename: "TimeVolumeType";
  time: string | null;
  volume: string | null;
}

export interface UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_additionalOxygenLevel {
  __typename: "AdditionalTimeVolumeType";
  name: string | null;
  value: UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_additionalOxygenLevel_value[] | null;
}

export interface UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_fluids {
  __typename: "FliudDataType";
  readingDate: any | null;
  time: string | null;
  ivFluids: string | null;
  bloodLoss: string | null;
  urineOutput: string | null;
}

export interface UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_size {
  __typename: "TicknessValueType";
  tick: boolean | null;
  value: string | null;
}

export interface UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_cmToLips {
  __typename: "TicknessValueType";
  tick: boolean | null;
  value: string | null;
}

export interface UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_nmj {
  __typename: "TicknessSiteType";
  tick: boolean | null;
  site: string | null;
}

export interface UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist {
  __typename: "AnaesthesiaModel";
  id: string;
  drugsGiven: UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_drugsGiven[] | null;
  pulseAndBp: UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_pulseAndBp[] | null;
  eventTimeGraph: UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_eventTimeGraph[] | null;
  oxygenLevel: UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_oxygenLevel[] | null;
  additionalOxygenLevel: UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_additionalOxygenLevel[] | null;
  fluids: UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_fluids[] | null;
  inOt: boolean | null;
  rapidSequence: boolean | null;
  inhalational: boolean | null;
  equipmentCheck: boolean | null;
  preOxygenation: boolean | null;
  vascularAccessSite: string | null;
  preInduction: string | null;
  postInduction: string | null;
  nasalSpecs: boolean | null;
  faceMask: boolean | null;
  iGel: boolean | null;
  lma: boolean | null;
  endatrachealTube: boolean | null;
  size: UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_size | null;
  cmToLips: UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_cmToLips | null;
  cuff: boolean | null;
  amoured: boolean | null;
  oralNasal: boolean | null;
  grade: string | null;
  difficult: boolean | null;
  bougie: boolean | null;
  mcCoy: boolean | null;
  bougieILMA: boolean | null;
  fibreOptic: boolean | null;
  mcIntosh: boolean | null;
  capnograph: boolean | null;
  auscultation: boolean | null;
  nmj: UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_nmj | null;
  tempProbe: boolean | null;
  respiration: string | null;
  mode: string | null;
  tv: string | null;
  frequency: string | null;
  circuit: string | null;
  other: string | null;
  eyesCovered: boolean | null;
  pressurePointsPadded: boolean | null;
  urinaryCatheter: boolean | null;
  nasogastricTube: boolean | null;
  warmingBlanket: boolean | null;
  position: string | null;
  arms: string | null;
  intravenousFluidsGiven: string | null;
  localAnaestheticTechnique: string | null;
  clinicalAdverseEvent: string | null;
  createdDate: any;
  updatedDate: any;
  creatorName: string | null;
  lastModifierName: string | null;
  profile: UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist_profile | null;
}

export interface UpdateAnaesthesiaChecklist {
  updateAnaesthesiaChecklist: UpdateAnaesthesiaChecklist_updateAnaesthesiaChecklist;
}

export interface UpdateAnaesthesiaChecklistVariables {
  id: string;
  input: AnaesthesiaInput;
  pin?: string | null;
}
