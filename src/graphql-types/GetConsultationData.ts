/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetConsultationData
// ====================================================

export interface GetConsultationData_getConsultationData_data {
  __typename: "ServicesSummary";
  name: number | null;
  totalConsultations: number | null;
}

export interface GetConsultationData_getConsultationData_provisionalDiagnosis_diagnosisICD10 {
  __typename: "ListsByGender";
  male: string[] | null;
  female: string[] | null;
}

export interface GetConsultationData_getConsultationData_provisionalDiagnosis_diagnosisICD11 {
  __typename: "ListsByGender";
  male: string[] | null;
  female: string[] | null;
}

export interface GetConsultationData_getConsultationData_provisionalDiagnosis_diagnosisSNOMED {
  __typename: "ListsByGender";
  male: string[] | null;
  female: string[] | null;
}

export interface GetConsultationData_getConsultationData_provisionalDiagnosis {
  __typename: "ConsultationDiagnosisData";
  diagnosisICD10: GetConsultationData_getConsultationData_provisionalDiagnosis_diagnosisICD10 | null;
  diagnosisICD11: GetConsultationData_getConsultationData_provisionalDiagnosis_diagnosisICD11 | null;
  diagnosisSNOMED: GetConsultationData_getConsultationData_provisionalDiagnosis_diagnosisSNOMED | null;
}

export interface GetConsultationData_getConsultationData_finalDiagnosis_diagnosisICD10 {
  __typename: "ListsByGender";
  male: string[] | null;
  female: string[] | null;
}

export interface GetConsultationData_getConsultationData_finalDiagnosis_diagnosisICD11 {
  __typename: "ListsByGender";
  male: string[] | null;
  female: string[] | null;
}

export interface GetConsultationData_getConsultationData_finalDiagnosis_diagnosisSNOMED {
  __typename: "ListsByGender";
  male: string[] | null;
  female: string[] | null;
}

export interface GetConsultationData_getConsultationData_finalDiagnosis {
  __typename: "ConsultationDiagnosisData";
  diagnosisICD10: GetConsultationData_getConsultationData_finalDiagnosis_diagnosisICD10 | null;
  diagnosisICD11: GetConsultationData_getConsultationData_finalDiagnosis_diagnosisICD11 | null;
  diagnosisSNOMED: GetConsultationData_getConsultationData_finalDiagnosis_diagnosisSNOMED | null;
}

export interface GetConsultationData_getConsultationData_byChemoDiagnosis {
  __typename: "ListByChemoDiagnosis";
  chemoDiagnosis: string | null;
  male: number | null;
  female: number | null;
}

export interface GetConsultationData_getConsultationData_byConsultationDateAndDoctorName {
  __typename: "ListByConsultationDateAndDoctorName";
  consultationDate: string | null;
  doctorName: string | null;
  finalDiagnosis: string[] | null;
  provisionalDiagnosis: string[] | null;
  patientName: string | null;
  clinifyId: string | null;
  paymentType: string | null;
  priority: string | null;
}

export interface GetConsultationData_getConsultationData_byChemoDiagnosisAndDoctorName {
  __typename: "ChemoDiagnosisListByConsultationDateAndDoctorName";
  consultationDate: string | null;
  doctorName: string | null;
  chemoDiagnosis: string[] | null;
}

export interface GetConsultationData_getConsultationData {
  __typename: "ServiceDurationData";
  averageNumber: number | null;
  data: GetConsultationData_getConsultationData_data[] | null;
  provisionalDiagnosis: GetConsultationData_getConsultationData_provisionalDiagnosis | null;
  finalDiagnosis: GetConsultationData_getConsultationData_finalDiagnosis | null;
  byChemoDiagnosis: GetConsultationData_getConsultationData_byChemoDiagnosis[] | null;
  byConsultationDateAndDoctorName: GetConsultationData_getConsultationData_byConsultationDateAndDoctorName[] | null;
  byChemoDiagnosisAndDoctorName: GetConsultationData_getConsultationData_byChemoDiagnosisAndDoctorName[] | null;
}

export interface GetConsultationData {
  getConsultationData: GetConsultationData_getConsultationData;
}

export interface GetConsultationDataVariables {
  filter?: ServicesAnalyticsFilter | null;
}
