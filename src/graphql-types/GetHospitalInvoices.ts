/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InvoiceListFilterInput, InvoiceStatus, PercentOrAmount, Currency, VirtualAccountProvider, PayoutStatus } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHospitalInvoices
// ====================================================

export interface GetHospitalInvoices_hospital_invoices_list_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface GetHospitalInvoices_hospital_invoices_list_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface GetHospitalInvoices_hospital_invoices_list_recipient {
  __typename: "InvoiceRecipient";
  address: string | null;
  email: string | null;
  name: string;
  phone: string | null;
  clinifyId: string | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_senderHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
  facilityLogo: string | null;
  website: string | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_virtualAccount {
  __typename: "VirtualBankAccountModel";
  id: string;
  currency: Currency;
  updatedDate: any;
  createdDate: any;
  accountName: string;
  accountNumber: string;
  bank: VirtualAccountProvider;
  expiryDate: any | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_employeesDetails_dependents {
  __typename: "DependentInfo";
  fullName: string;
  enrolleeId: string | null;
  relationship: string | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_employeesDetails {
  __typename: "EmployeesInfo";
  enrolleeId: string | null;
  fullName: string;
  planCategory: string | null;
  planType: string | null;
  planAmount: number | null;
  paymentFrequency: string | null;
  planStartDate: any | null;
  planDueDate: any | null;
  planStatus: string | null;
  isCovered: boolean | null;
  dependents: GetHospitalInvoices_hospital_invoices_list_employeesDetails_dependents[] | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_sponsorDetails {
  __typename: "SponsorEnrolleeDetails";
  status: string | null;
  fullName: string | null;
  isCovered: boolean | null;
  memberNumber: string | null;
  planDueDate: any | null;
  planStartDate: any | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_invoiceItems {
  __typename: "InvoiceItemModel";
  id: string;
  description: string;
  updatedDate: any;
  createdDate: any;
  invoiceType: string | null;
  discountAmount: number | null;
  discountPercentage: number | null;
  invoiceId: string;
  quantity: number;
  unitPrice: number | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_employer_employees_hmoPlanType_premiumDetails {
  __typename: "PremiumDetails";
  frequency: string | null;
  category: string | null;
  amount: number | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_employer_employees_hmoPlanType {
  __typename: "HmoPlanTypeModel";
  id: string;
  premiumDetails: GetHospitalInvoices_hospital_invoices_list_employer_employees_hmoPlanType_premiumDetails[] | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_employer_employees_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  memberNumber: string | null;
  memberStatus: string | null;
  planCategory: string | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_employer_employees_dependants_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  memberNumber: string | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_employer_employees_dependants {
  __typename: "EmployeeDependantModel";
  id: string;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  relationship: string | null;
  hmoProfile: GetHospitalInvoices_hospital_invoices_list_employer_employees_dependants_hmoProfile | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_employer_employees {
  __typename: "EmployeeModel";
  id: string;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  planCategory: string | null;
  paymentFrequency: string | null;
  planStartDate: any | null;
  planDueDate: any | null;
  hmoPlanType: GetHospitalInvoices_hospital_invoices_list_employer_employees_hmoPlanType | null;
  hmoProfile: GetHospitalInvoices_hospital_invoices_list_employer_employees_hmoProfile | null;
  dependants: GetHospitalInvoices_hospital_invoices_list_employer_employees_dependants[] | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_employer {
  __typename: "EmployerModel";
  id: string;
  employerName: string;
  employees: GetHospitalInvoices_hospital_invoices_list_employer_employees[] | null;
}

export interface GetHospitalInvoices_hospital_invoices_list_invoicePayments {
  __typename: "InvoicePaymentModel";
  id: string;
  paymentStatus: string;
  paymentMethod: string;
  amountPaid: number;
  payoutStatus: PayoutStatus;
  amountDue: number;
}

export interface GetHospitalInvoices_hospital_invoices_list {
  __typename: "InvoiceModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  discountPercentage: number | null;
  discountAmount: number | null;
  profileId: string | null;
  employerId: string | null;
  creatorId: string;
  description: string | null;
  subTotal: number;
  /**
   * Returns total amount in lowest denomination
   */
  totalAmount: number;
  createdBy: GetHospitalInvoices_hospital_invoices_list_createdBy;
  lastModifierId: string | null;
  updatedBy: GetHospitalInvoices_hospital_invoices_list_updatedBy | null;
  additionalNote: string | null;
  amountPaid: number;
  bankTransactionIds: string[] | null;
  dueDate: any;
  invoiceReference: string;
  invoiceStatus: InvoiceStatus;
  issueDate: any;
  paymentDate: any | null;
  sponsorName: string | null;
  sponsorRef: string | null;
  nextYearlyPremium: number | null;
  sponsorLivesCovered: number | null;
  agencyLivesCovered: number | null;
  sponsorPremiumPerLives: number | null;
  recipient: GetHospitalInvoices_hospital_invoices_list_recipient;
  senderHospitalId: string | null;
  senderHospital: GetHospitalInvoices_hospital_invoices_list_senderHospital;
  professionalFeeAmount: number | null;
  professionalFeePercentage: number | null;
  controlledDiscount: PercentOrAmount | null;
  controlledProfessionalFee: PercentOrAmount | null;
  vatPercentage: number | null;
  vatAmount: number | null;
  controlledVat: PercentOrAmount | null;
  virtualAccount: GetHospitalInvoices_hospital_invoices_list_virtualAccount | null;
  employeesDetails: GetHospitalInvoices_hospital_invoices_list_employeesDetails[] | null;
  sponsorDetails: GetHospitalInvoices_hospital_invoices_list_sponsorDetails[] | null;
  paymentFrequency: string | null;
  plasticIdCardCount: number | null;
  plasticIdCardAmount: number | null;
  laminatedIdCardCount: number | null;
  laminatedIdCardAmount: number | null;
  creatorName: string;
  lastModifierName: string | null;
  periodStartDate: any | null;
  periodEndDate: any | null;
  invoiceItems: GetHospitalInvoices_hospital_invoices_list_invoiceItems[];
  employer: GetHospitalInvoices_hospital_invoices_list_employer | null;
  invoicePayments: GetHospitalInvoices_hospital_invoices_list_invoicePayments[] | null;
}

export interface GetHospitalInvoices_hospital_invoices {
  __typename: "InvoiceListResponse";
  totalCount: number;
  list: GetHospitalInvoices_hospital_invoices_list[];
}

export interface GetHospitalInvoices_hospital {
  __typename: "HospitalModel";
  id: string;
  invoices: GetHospitalInvoices_hospital_invoices;
}

export interface GetHospitalInvoices {
  hospital: GetHospitalInvoices_hospital;
}

export interface GetHospitalInvoicesVariables {
  hospitalId?: string | null;
  filterOptions?: InvoiceListFilterInput | null;
}
