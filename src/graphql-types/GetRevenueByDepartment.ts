/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetRevenueByDepartment
// ====================================================

export interface GetRevenueByDepartment_getRevenueByDepartment {
  __typename: "FinanceData";
  name: number | null;
  category: string | null;
  totalAmountDue: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
  totalDiscount: number | null;
  totalAmount: number | null;
  totalRevenue: number | null;
  visitationDate: any | null;
  serviceType: string | null;
  serviceName: string | null;
  quantity: string | null;
  raisedBy: string | null;
  patientName: string | null;
  patientClinifyId: string | null;
  patientPhoneNumber: string | null;
  patientEmailAddress: string | null;
  coverageType: string | null;
  coverageName: string | null;
  enrolleeId: string | null;
}

export interface GetRevenueByDepartment {
  getRevenueByDepartment: GetRevenueByDepartment_getRevenueByDepartment[];
}

export interface GetRevenueByDepartmentVariables {
  filter?: FinanceAnalyticsFilter | null;
}
