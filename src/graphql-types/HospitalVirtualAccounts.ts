/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Currency, VirtualAccountTransactionType, VirtualAccountType, VirtualAccountProvider } from "./globalTypes";

// ====================================================
// GraphQL query operation: HospitalVirtualAccounts
// ====================================================

export interface HospitalVirtualAccounts_hospital_virtualAccounts_hmo {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface HospitalVirtualAccounts_hospital_virtualAccounts {
  __typename: "VirtualBankAccountModel";
  id: string;
  currency: Currency;
  accountName: string;
  accountNumber: string;
  transactionType: VirtualAccountTransactionType;
  virtualAccountType: VirtualAccountType;
  createdDate: any;
  updatedDate: any;
  bank: VirtualAccountProvider;
  expiryDate: any | null;
  hmoId: string | null;
  hmo: HospitalVirtualAccounts_hospital_virtualAccounts_hmo | null;
}

export interface HospitalVirtualAccounts_hospital {
  __typename: "HospitalModel";
  id: string;
  virtualAccounts: HospitalVirtualAccounts_hospital_virtualAccounts[] | null;
}

export interface HospitalVirtualAccounts {
  hospital: HospitalVirtualAccounts_hospital;
}
