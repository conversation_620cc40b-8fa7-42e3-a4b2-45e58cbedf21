/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { StocksAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: TopStocksSummary
// ====================================================

export interface TopStocksSummary_topStocksSummary_mostPurchasedDrugs {
  __typename: "TopSummary";
  count: number | null;
  itemName: string | null;
  totalRevenue: number | null;
  totalQuantityRemaining: number | null;
}

export interface TopStocksSummary_topStocksSummary_mostPurchasedDevices {
  __typename: "TopSummary";
  count: number | null;
  itemName: string | null;
  totalRevenue: number | null;
  totalQuantityRemaining: number | null;
}

export interface TopStocksSummary_topStocksSummary_mostPurchasedVehicles {
  __typename: "TopSummary";
  count: number | null;
  itemName: string | null;
  totalRevenue: number | null;
  totalQuantityRemaining: number | null;
}

export interface TopStocksSummary_topStocksSummary_mostPurchasedConsumables {
  __typename: "TopSummary";
  count: number | null;
  itemName: string | null;
  totalRevenue: number | null;
  totalQuantityRemaining: number | null;
}

export interface TopStocksSummary_topStocksSummary_topSuppliers {
  __typename: "TopSummary";
  count: number | null;
  supplier: string | null;
  totalRevenue: number | null;
  totalQuantityRemaining: number | null;
}

export interface TopStocksSummary_topStocksSummary_topLabs {
  __typename: "TopSummary";
  count: number | null;
  itemName: string | null;
  totalRevenue: number | null;
  totalQuantityRemaining: number | null;
}

export interface TopStocksSummary_topStocksSummary {
  __typename: "TopStocksSummary";
  mostPurchasedDrugs: TopStocksSummary_topStocksSummary_mostPurchasedDrugs[] | null;
  mostPurchasedDevices: TopStocksSummary_topStocksSummary_mostPurchasedDevices[] | null;
  mostPurchasedVehicles: TopStocksSummary_topStocksSummary_mostPurchasedVehicles[] | null;
  mostPurchasedConsumables: TopStocksSummary_topStocksSummary_mostPurchasedConsumables[] | null;
  topSuppliers: TopStocksSummary_topStocksSummary_topSuppliers[] | null;
  topLabs: TopStocksSummary_topStocksSummary_topLabs[] | null;
}

export interface TopStocksSummary {
  topStocksSummary: TopStocksSummary_topStocksSummary;
}

export interface TopStocksSummaryVariables {
  filter?: StocksAnalyticsFilter | null;
}
