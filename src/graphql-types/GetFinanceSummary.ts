/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetFinanceSummary
// ====================================================

export interface GetFinanceSummary_getFinanceSummary_billSummary {
  __typename: "BillSummaryType";
  hospitalName: string | null;
  hospitalId: string | null;
  totalAmountOutstanding: number | null;
  totalAmountDue: number | null;
  totalDiscount: number | null;
  totalAmount: number | null;
  totalAmountPaid: number | null;
  totalRevenue: number | null;
  totalPaid: number | null;
  totalPending: number | null;
  totalCancelled: number | null;
  totalPartial: number | null;
  totalTransactions: number | null;
}

export interface GetFinanceSummary_getFinanceSummary_depositSummary {
  __typename: "DepositSummaryType";
  hospitalName: string | null;
  hospitalId: string | null;
  totalAmountDeposited: number | null;
  totalAmountRefunded: number | null;
  totalAmountWithdrawn: number | null;
  totalAmountAvailable: number | null;
}

export interface GetFinanceSummary_getFinanceSummary {
  __typename: "FinanceData";
  name: number | null;
  totalTransactions: number | null;
  totalDiscount: number | null;
  totalRevenue: number | null;
  totalAmount: number | null;
  totalAmountPaid: number | null;
  totalPending: number | null;
  totalPaid: number | null;
  totalPartial: number | null;
  totalCancelled: number | null;
  totalAmountOutstanding: number | null;
  raisedBy: string | null;
  category: string | null;
  coverageName: string | null;
  coverageType: string | null;
  enrolleeId: string | null;
  patientClinifyId: string | null;
  patientEmailAddress: string | null;
  patientName: string | null;
  patientPhoneNumber: string | null;
  quantity: string | null;
  serviceName: string | null;
  serviceType: string | null;
  visitationDate: any | null;
  totalAmountDue: number | null;
  totalAmountDeposited: number | null;
  totalAmountAvailable: number | null;
  totalAmountWithdrawn: number | null;
  totalAmountRefunded: number | null;
  billSummary: GetFinanceSummary_getFinanceSummary_billSummary[] | null;
  depositSummary: GetFinanceSummary_getFinanceSummary_depositSummary[] | null;
}

export interface GetFinanceSummary {
  getFinanceSummary: GetFinanceSummary_getFinanceSummary;
}

export interface GetFinanceSummaryVariables {
  filter?: FinanceAnalyticsFilter | null;
}
