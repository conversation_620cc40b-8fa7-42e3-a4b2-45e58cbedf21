/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: SubmitHmoClaims
// ====================================================

export interface SubmitHmoClaims_submitHmoClaims_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface SubmitHmoClaims_submitHmoClaims_flags {
  __typename: "FlagDto";
  flag: string | null;
}

export interface SubmitHmoClaims_submitHmoClaims_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface SubmitHmoClaims_submitHmoClaims_utilizations_flags {
  __typename: "FlagDto";
  flag: string | null;
  ruleId: string | null;
}

export interface SubmitHmoClaims_submitHmoClaims_utilizations_utilisationStatus {
  __typename: "ClaimsApprovalInputType";
  status: string | null;
  comment: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusDescription: string | null;
  serviceAmount: number | null;
  vettingGroup: string;
  creatorId: string;
  creatorName: string;
  createdDate: any;
  updatedDate: any | null;
}

export interface SubmitHmoClaims_submitHmoClaims_utilizations_transferFund_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  title: string | null;
}

export interface SubmitHmoClaims_submitHmoClaims_utilizations_transferFund {
  __typename: "TransferFundModel";
  id: string;
  amount: number;
  serviceChargeAmount: number | null;
  transferReference: string | null;
  destinationBankCode: string | null;
  destinationBankName: string | null;
  destinationAccountNumber: string | null;
  destinationAccountName: string | null;
  createdDate: any;
  originatorName: string | null;
  narration: string | null;
  createdBy: SubmitHmoClaims_submitHmoClaims_utilizations_transferFund_createdBy | null;
}

export interface SubmitHmoClaims_submitHmoClaims_utilizations_aiReason {
  __typename: "AIReason";
  reason: string | null;
  status: string | null;
}

export interface SubmitHmoClaims_submitHmoClaims_utilizations {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  medicationCategory: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  frequency: string | null;
  duration: string | null;
  birthCount: string | null;
  deliveryDateTime: any | null;
  gestationalAge: string | null;
  specialty: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusDescription: string | null;
  creatorName: string | null;
  createdDate: any;
  lastModifierName: string | null;
  updatedDate: any;
  /**
   * Service type from which this utilization is created
   */
  serviceName: string | null;
  confirmation: boolean | null;
  percentageCovered: number | null;
  amountCovered: number | null;
  paymentModel: string | null;
  autoApprovalSource: string | null;
  flags: SubmitHmoClaims_submitHmoClaims_utilizations_flags[] | null;
  utilisationStatus: SubmitHmoClaims_submitHmoClaims_utilizations_utilisationStatus[] | null;
  transferFundId: string | null;
  transferFund: SubmitHmoClaims_submitHmoClaims_utilizations_transferFund | null;
  aiReason: SubmitHmoClaims_submitHmoClaims_utilizations_aiReason | null;
  paUtilProcessed: boolean | null;
}

export interface SubmitHmoClaims_submitHmoClaims_enrolleePhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface SubmitHmoClaims_submitHmoClaims_revertAction {
  __typename: "RevertActionInputType";
  action: string | null;
  mutatorAlias: string | null;
  receiverAlias: string | null;
  mutatorUserType: UserType | null;
  receiverUserType: UserType | null;
}

export interface SubmitHmoClaims_submitHmoClaims_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
}

export interface SubmitHmoClaims_submitHmoClaims_payoutHistory {
  __typename: "HmoClaimPayoutItem";
  createdDate: any | null;
  amount: number | null;
  clinifyId: string | null;
  payoutId: string | null;
  utilizationIds: string[] | null;
}

export interface SubmitHmoClaims_submitHmoClaims_financeApproval {
  __typename: "ClaimsAccountApprovalType";
  createdDate: any;
  creatorId: string;
  creatorName: string;
  approvalGroup: string;
}

export interface SubmitHmoClaims_submitHmoClaims {
  __typename: "HmoClaimModel";
  id: string;
  claimId: string | null;
  visitId: string | null;
  batchNumber: string | null;
  claimDate: any | null;
  submitDateTime: any | null;
  submittedBy: string | null;
  treatmentStartDate: any | null;
  treatmentEndDate: any | null;
  confirmation: string | null;
  provider: SubmitHmoClaims_submitHmoClaims_provider;
  serviceType: string;
  serviceTypeCode: string;
  serviceName: string | null;
  priority: string | null;
  claimIdentity: string | null;
  status: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  presentingComplain: string | null;
  externalPlanTypeId: string | null;
  isExternalPlanType: boolean | null;
  flags: SubmitHmoClaims_submitHmoClaims_flags[] | null;
  diagnosis: SubmitHmoClaims_submitHmoClaims_diagnosis[] | null;
  utilizations: SubmitHmoClaims_submitHmoClaims_utilizations[] | null;
  documentUrl: string[] | null;
  totalQuantity: string;
  grandTotal: string;
  additionalNote: string | null;
  creatorName: string | null;
  lastModifierName: string | null;
  updatedDate: any;
  referredBy: string | null;
  referralCode: string | null;
  referredFrom: string | null;
  referredTo: string | null;
  profileId: string | null;
  enrolleeNumber: string | null;
  hospitalId: string;
  createdDate: any;
  responseDateTime: any | null;
  totalRejectedAmount: string;
  totalSubmittedAmount: string;
  enrolleePhoneNumber: SubmitHmoClaims_submitHmoClaims_enrolleePhoneNumber | null;
  revertAction: SubmitHmoClaims_submitHmoClaims_revertAction | null;
  enrolleeEmail: string | null;
  profile: SubmitHmoClaims_submitHmoClaims_profile | null;
  payoutStatus: string | null;
  payoutHistory: SubmitHmoClaims_submitHmoClaims_payoutHistory[] | null;
  financeApproval: SubmitHmoClaims_submitHmoClaims_financeApproval[] | null;
}

export interface SubmitHmoClaims {
  submitHmoClaims: SubmitHmoClaims_submitHmoClaims[];
}

export interface SubmitHmoClaimsVariables {
  ids: string[];
  origin: string;
}
