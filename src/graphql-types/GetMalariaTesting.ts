/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CasesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetMalariaTesting
// ====================================================

export interface GetMalariaTesting_getMalariaTesting_ageRanges {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetMalariaTesting_getMalariaTesting {
  __typename: "CasesSummary";
  name: number | null;
  totalFemale: number | null;
  totalMale: number | null;
  totalPregnant: number | null;
  ageRanges: GetMalariaTesting_getMalariaTesting_ageRanges[] | null;
}

export interface GetMalariaTesting {
  getMalariaTesting: GetMalariaTesting_getMalariaTesting[];
}

export interface GetMalariaTestingVariables {
  filter?: CasesAnalyticsFilter | null;
}
