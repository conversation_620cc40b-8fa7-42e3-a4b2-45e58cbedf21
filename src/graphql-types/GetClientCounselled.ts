/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FamilyPlanningAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetClientCounselled
// ====================================================

export interface GetClientCounselled_getClientCounselled {
  __typename: "FamilyPlanningSummary";
  name: number | null;
  totalClientCounselled: number | null;
  category: string | null;
}

export interface GetClientCounselled {
  getClientCounselled: GetClientCounselled_getClientCounselled[];
}

export interface GetClientCounselledVariables {
  filter?: FamilyPlanningAnalyticsFilter | null;
}
