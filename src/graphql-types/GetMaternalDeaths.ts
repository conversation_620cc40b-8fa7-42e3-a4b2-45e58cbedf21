/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DeathsAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetMaternalDeaths
// ====================================================

export interface GetMaternalDeaths_getMaternalDeaths_ageRanges {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetMaternalDeaths_getMaternalDeaths_byCauseOfDeath {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetMaternalDeaths_getMaternalDeaths {
  __typename: "DeathsSummary";
  name: number | null;
  ageRanges: GetMaternalDeaths_getMaternalDeaths_ageRanges[] | null;
  byCauseOfDeath: GetMaternalDeaths_getMaternalDeaths_byCauseOfDeath[] | null;
}

export interface GetMaternalDeaths {
  getMaternalDeaths: GetMaternalDeaths_getMaternalDeaths;
}

export interface GetMaternalDeathsVariables {
  filter?: DeathsAnalyticsFilter | null;
}
