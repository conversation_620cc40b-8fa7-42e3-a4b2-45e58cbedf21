/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: WalkInTransferAddedSubs
// ====================================================

export interface WalkInTransferAddedSubs_WalkInTransferAdded_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface WalkInTransferAddedSubs_WalkInTransferAdded {
  __typename: "WalkInTransferModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  transferFacilityName: string | null;
  transferFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: WalkInTransferAddedSubs_WalkInTransferAdded_patientInformation | null;
}

export interface WalkInTransferAddedSubs {
  WalkInTransferAdded: WalkInTransferAddedSubs_WalkInTransferAdded;
}

export interface WalkInTransferAddedSubsVariables {
  hospitalId: string;
}
