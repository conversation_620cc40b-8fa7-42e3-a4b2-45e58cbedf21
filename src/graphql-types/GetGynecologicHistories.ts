/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ProfileInfosFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetGynecologicHistories
// ====================================================

export interface GetGynecologicHistories_profile_gynecologicHistories_list_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface GetGynecologicHistories_profile_gynecologicHistories_list {
  __typename: "GynecologicHistoryModel";
  id: string;
  firstMenstrualAge: string | null;
  menstrualCycleLength: string | null;
  menstrualFlowDuration: string | null;
  lastMenstrualPeriod: any | null;
  menstrualFlow: string | null;
  contraceptiveUse: string | null;
  contraceptiveType: string | null;
  specifyContraceptiveType: string | null;
  newAcceptor: boolean | null;
  revisit: boolean | null;
  removeContraceptive: boolean | null;
  quantityGiven: string | null;
  miscarriageOrAbortion: string | null;
  miscarriageOrAbortionCount: number | null;
  pregnancyCount: string | null;
  currentlyPregnant: string | null;
  babyDelivered: string | null;
  menstrualStatus: string | null;
  breastFeeding: string | null;
  sourceOfReferral: string | null;
  counselledOnFp: string | null;
  conunselledOnPpfp: string | null;
  firstTimeFpUser: string | null;
  emergencyContraception: string | null;
  familyPlanningClientType: string | null;
  referredOut: string | null;
  followupVisit: any | null;
  additionalNote: string | null;
  profile: GetGynecologicHistories_profile_gynecologicHistories_list_profile | null;
}

export interface GetGynecologicHistories_profile_gynecologicHistories {
  __typename: "GynecologicHistoriesResponse";
  totalCount: number;
  list: GetGynecologicHistories_profile_gynecologicHistories_list[];
}

export interface GetGynecologicHistories_profile {
  __typename: "ProfileModel";
  id: string;
  gynecologicHistories: GetGynecologicHistories_profile_gynecologicHistories;
}

export interface GetGynecologicHistories {
  profile: GetGynecologicHistories_profile;
}

export interface GetGynecologicHistoriesVariables {
  filterInput: ProfileInfosFilterInput;
  id: string;
}
