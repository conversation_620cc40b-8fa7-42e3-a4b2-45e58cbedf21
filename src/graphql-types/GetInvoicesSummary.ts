/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InvoiceListFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetInvoicesSummary
// ====================================================

export interface GetInvoicesSummary_getInvoiceListSummary {
  __typename: "InvoiceListSummaryResponse";
  totalInvoices: number;
  totalOverdue: number;
  totalPaid: number;
  totalPending: number;
  totalDraft: number;
}

export interface GetInvoicesSummary {
  getInvoiceListSummary: GetInvoicesSummary_getInvoiceListSummary;
}

export interface GetInvoicesSummaryVariables {
  filterOptions: InvoiceListFilterInput;
}
