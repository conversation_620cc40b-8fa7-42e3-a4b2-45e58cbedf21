/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HandoverNoteRemovedSubs
// ====================================================

export interface HandoverNoteRemovedSubs_HandoverNoteRemoved {
  __typename: "HandoverNoteModel";
  id: string;
}

export interface HandoverNoteRemovedSubs {
  HandoverNoteRemoved: HandoverNoteRemovedSubs_HandoverNoteRemoved;
}

export interface HandoverNoteRemovedSubsVariables {
  profileId: string;
  hospitalId?: string | null;
  userType: UserType;
}
