/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MaternalHealthAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetAntenatalVisits
// ====================================================

export interface GetAntenatalVisits_getAntenatalVisits_antenatalAttendance {
  __typename: "CategoryCount";
  count: number | null;
  category: string | null;
}

export interface GetAntenatalVisits_getAntenatalVisits_ageRanges {
  __typename: "CategoryCount";
  count: number | null;
  category: string | null;
}

export interface GetAntenatalVisits_getAntenatalVisits {
  __typename: "MaternalHealthSummary";
  name: number | null;
  totalAntenatalVisits: number | null;
  antenatalAttendance: GetAntenatalVisits_getAntenatalVisits_antenatalAttendance[] | null;
  ageRanges: GetAntenatalVisits_getAntenatalVisits_ageRanges[] | null;
}

export interface GetAntenatalVisits {
  getAntenatalVisits: GetAntenatalVisits_getAntenatalVisits;
}

export interface GetAntenatalVisitsVariables {
  filter?: MaternalHealthAnalyticsFilter | null;
}
