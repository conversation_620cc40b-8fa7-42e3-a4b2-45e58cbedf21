/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: FacilityBillingInformation
// ====================================================

export interface FacilityBillingInformation {
  __typename: "FacilityBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  bvn: string | null;
  branchName: string | null;
  isPreferredPayoutAccount: boolean | null;
}
