/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetPatientPostOperation
// ====================================================

export interface GetPatientPostOperation_postOperationChecklist_dietOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface GetPatientPostOperation_postOperationChecklist_ambutationOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface GetPatientPostOperation_postOperationChecklist_urethralCatheterizationOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface GetPatientPostOperation_postOperationChecklist_fluidTherapyOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface GetPatientPostOperation_postOperationChecklist_antiBioticsOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface GetPatientPostOperation_postOperationChecklist_analgesicOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
  title: string | null;
  names: string[] | null;
  isMultiple: boolean | null;
}

export interface GetPatientPostOperation_postOperationChecklist_clexane40mgFor3DaysOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface GetPatientPostOperation_postOperationChecklist_vitalSigns {
  __typename: "VitalSignType";
  timeIn: string | null;
  systolic: string | null;
  diastolic: string | null;
  pulseRate: string | null;
  oxygenSaturation: string | null;
  respiratoryRate: string | null;
  temperature: string | null;
  temperatureUnit: string | null;
  nausea: string | null;
  painScore: string | null;
  state: string | null;
}

export interface GetPatientPostOperation_postOperationChecklist_medications {
  __typename: "MedicationDetailType";
  medicationName: string | null;
  prescriptionTime: any | null;
  dosage: string | null;
  dosageUnit: string | null;
}

export interface GetPatientPostOperation_postOperationChecklist_urineOutput {
  __typename: "UrineOutputDetailType";
  timeIn: string | null;
  output: string | null;
}

export interface GetPatientPostOperation_postOperationChecklist_linesDrainsTubes {
  __typename: "LinesDrainsTubesDetailType";
  intravenousInfusions: string | null;
  others: string | null;
  dressingChecked: string | null;
  dressingCheckedTime: any | null;
  drainsChecked: string | null;
  drainsCheckedTime: any | null;
  catheterChecked: string | null;
  catheterCheckedTime: any | null;
  quantityDrained: string | null;
}

export interface GetPatientPostOperation_postOperationChecklist_newOrders {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface GetPatientPostOperation_postOperationChecklist_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface GetPatientPostOperation_postOperationChecklist {
  __typename: "PostOperationModel";
  id: string;
  serviceStartDateTime: any | null;
  chartType: string | null;
  surgeryStartDateTime: any | null;
  surgeryEndDateTime: any | null;
  dietOrderSheet: GetPatientPostOperation_postOperationChecklist_dietOrderSheet[] | null;
  ambutationOrderSheet: GetPatientPostOperation_postOperationChecklist_ambutationOrderSheet[] | null;
  urethralCatheterizationOrderSheet: GetPatientPostOperation_postOperationChecklist_urethralCatheterizationOrderSheet[] | null;
  fluidTherapyOrderSheet: GetPatientPostOperation_postOperationChecklist_fluidTherapyOrderSheet[] | null;
  antiBioticsOrderSheet: GetPatientPostOperation_postOperationChecklist_antiBioticsOrderSheet[] | null;
  analgesicOrderSheet: GetPatientPostOperation_postOperationChecklist_analgesicOrderSheet[] | null;
  clexane40mgFor3DaysOrderSheet: GetPatientPostOperation_postOperationChecklist_clexane40mgFor3DaysOrderSheet[] | null;
  vitalSigns: GetPatientPostOperation_postOperationChecklist_vitalSigns[] | null;
  medications: GetPatientPostOperation_postOperationChecklist_medications[] | null;
  discontinueMedication: boolean;
  urineOutput: GetPatientPostOperation_postOperationChecklist_urineOutput[] | null;
  linesDrainsTubes: GetPatientPostOperation_postOperationChecklist_linesDrainsTubes[] | null;
  newOrders: GetPatientPostOperation_postOperationChecklist_newOrders[] | null;
  SurgeonName: string | null;
  surgeonNameSignature: string | null;
  surgeonNameSignatureType: string | null;
  surgeonNameSignatureDateTime: any | null;
  SurgeonSpecialty: string | null;
  SurgeonAssistantName: string | null;
  surgeonAssistantNameSignature: string | null;
  surgeonAssistantNameSignatureType: string | null;
  surgeonAssistantNameSignatureDateTime: any | null;
  operatingRoomNurse: string | null;
  operatingRoomNurseSignature: string | null;
  operatingRoomNurseSignatureType: string | null;
  operatingRoomNurseSignatureDateTime: any | null;
  anesthetistName: string | null;
  anesthetistNameSignature: string | null;
  anesthetistNameSignatureType: string | null;
  anesthetistNameSignatureDateTime: any | null;
  recoveryNurse: string | null;
  recoveryNurseSignature: string | null;
  recoveryNurseSignatureType: string | null;
  recoveryNurseSignatureDateTime: any | null;
  visitingSpecialistName: string | null;
  visitingSpecialistSignature: string | null;
  visitingSpecialistSignatureType: string | null;
  visitingSpecialistSignatureDateTime: any | null;
  visitingFacilityName: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean;
  profileId: string | null;
  profile: GetPatientPostOperation_postOperationChecklist_profile | null;
}

export interface GetPatientPostOperation {
  postOperationChecklist: GetPatientPostOperation_postOperationChecklist;
}

export interface GetPatientPostOperationVariables {
  clinifyId: string;
  id: string;
}
