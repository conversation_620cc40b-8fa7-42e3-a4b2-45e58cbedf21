/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PayoutStatus, Currency, CommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL query operation: FetchRequestPayout
// ====================================================

export interface FetchRequestPayout_requestPayout_receiverAccount {
  __typename: "BankAccountInformation";
  bankName: string;
  accountNumber: string;
  accountName: string | null;
  bankCode: string | null;
  accountType: string | null;
}

export interface FetchRequestPayout_requestPayout_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface FetchRequestPayout_requestPayout_virtualServicesPayments_bill_receiverProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface FetchRequestPayout_requestPayout_virtualServicesPayments_bill {
  __typename: "BillModel";
  id: string;
  billingDateTime: any | null;
  raisedBy: string | null;
  receiverProfile: FetchRequestPayout_requestPayout_virtualServicesPayments_bill_receiverProfile | null;
}

export interface FetchRequestPayout_requestPayout_virtualServicesPayments {
  __typename: "VirtualServicesPaymentModel";
  id: string;
  billId: string | null;
  bill: FetchRequestPayout_requestPayout_virtualServicesPayments_bill | null;
  amountDue: number;
  amountPaid: number;
  paymentStatus: string;
  commissionPayer: CommissionPayer | null;
  commissionFeeAmount: number | null;
  payoutId: string | null;
  payoutStatus: PayoutStatus;
  createdDate: any;
  updatedDate: any | null;
}

export interface FetchRequestPayout_requestPayout_invoicePayments_invoice_recipient {
  __typename: "InvoiceRecipient";
  phone: string | null;
  address: string | null;
  email: string | null;
  name: string;
  clinifyId: string | null;
}

export interface FetchRequestPayout_requestPayout_invoicePayments_invoice {
  __typename: "InvoiceModel";
  id: string;
  invoiceReference: string;
  issueDate: any;
  dueDate: any;
  recipient: FetchRequestPayout_requestPayout_invoicePayments_invoice_recipient;
}

export interface FetchRequestPayout_requestPayout_invoicePayments {
  __typename: "InvoicePaymentModel";
  id: string;
  paymentStatus: string;
  payoutStatus: PayoutStatus;
  amountDue: number;
  amountPaid: number;
  commissionFeeAmount: number | null;
  commissionFeePercentage: number | null;
  paymentMethod: string;
  invoice: FetchRequestPayout_requestPayout_invoicePayments_invoice;
}

export interface FetchRequestPayout_requestPayout_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface FetchRequestPayout_requestPayout_payout {
  __typename: "PayoutModel";
  id: string;
  amountPaid: number;
  payoutStatus: PayoutStatus;
}

export interface FetchRequestPayout_requestPayout {
  __typename: "RequestPayoutModel";
  id: string;
  requestPayoutDateTime: any;
  initiatedBy: string | null;
  createdDate: any;
  updatedDate: any;
  payoutStatus: PayoutStatus;
  payoutDescription: string | null;
  receiverAccount: FetchRequestPayout_requestPayout_receiverAccount;
  currency: Currency;
  hospitalId: string;
  receiverInitialWalletBalanceBeforePayout: number;
  receiverInitialWalletBalanceBeforeRequest: number;
  requestAmount: number;
  totalCommissionFeeAmount: number;
  transactionStartDate: any;
  transactionEndDate: any;
  creatorName: string | null;
  lastModifierName: string | null;
  createdBy: FetchRequestPayout_requestPayout_createdBy;
  virtualServicesPayments: FetchRequestPayout_requestPayout_virtualServicesPayments[];
  invoicePayments: FetchRequestPayout_requestPayout_invoicePayments[];
  hospital: FetchRequestPayout_requestPayout_hospital;
  payout: FetchRequestPayout_requestPayout_payout | null;
  additionalNote: string | null;
}

export interface FetchRequestPayout {
  requestPayout: FetchRequestPayout_requestPayout;
}

export interface FetchRequestPayoutVariables {
  id: string;
}
