/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetServicesByCoverageType
// ====================================================

export interface GetServicesByCoverageType_getServicesByCoverageType_list_servicesByCoverageType {
  __typename: "ByCoverageType";
  name: number | null;
  coverageName: string | null;
  coverageType: string | null;
  totalCount: number | null;
  totalAmount: number | null;
  totalDiscount: number | null;
  totalAmountDue: number | null;
  totalAmountPaid: number | null;
  totalAmountOwing: number | null;
  totalAmountOutstanding: number | null;
  patientEmail: string | null;
  patientName: string | null;
  visitDate: string | null;
  memberNumber: string | null;
  patientPhone: string | null;
}

export interface GetServicesByCoverageType_getServicesByCoverageType_list {
  __typename: "ServiceSummaryList";
  servicesByCoverageType: GetServicesByCoverageType_getServicesByCoverageType_list_servicesByCoverageType[] | null;
}

export interface GetServicesByCoverageType_getServicesByCoverageType {
  __typename: "ServiceSummaryWithList";
  list: GetServicesByCoverageType_getServicesByCoverageType_list;
}

export interface GetServicesByCoverageType {
  getServicesByCoverageType: GetServicesByCoverageType_getServicesByCoverageType;
}

export interface GetServicesByCoverageTypeVariables {
  filter?: ServicesAnalyticsFilter | null;
}
