/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NextAppointmentEntities } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: UnarchiveAppointmentsSub
// ====================================================

export interface UnarchiveAppointmentsSub_AppointmentsUnarchived_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface UnarchiveAppointmentsSub_AppointmentsUnarchived_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface UnarchiveAppointmentsSub_AppointmentsUnarchived_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface UnarchiveAppointmentsSub_AppointmentsUnarchived_specialist {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  type: string;
  fullName: string;
}

export interface UnarchiveAppointmentsSub_AppointmentsUnarchived_serviceDetails {
  __typename: "ServiceDetailInputType";
  type: string | null;
  name: string | null;
}

export interface UnarchiveAppointmentsSub_AppointmentsUnarchived {
  __typename: "OrganisationAppointmentModel";
  id: string;
  hospital: UnarchiveAppointmentsSub_AppointmentsUnarchived_hospital | null;
  profile: UnarchiveAppointmentsSub_AppointmentsUnarchived_profile | null;
  patientInformation: UnarchiveAppointmentsSub_AppointmentsUnarchived_patientInformation | null;
  specialist: UnarchiveAppointmentsSub_AppointmentsUnarchived_specialist | null;
  createdDate: any;
  updatedDate: any;
  status: string | null;
  category: string | null;
  liveSessionUrl: string | null;
  rank: string | null;
  reason: string | null;
  appointmentDateTime: any | null;
  startDateTime: any | null;
  endDateTime: any | null;
  specialty: string | null;
  role: string | null;
  paymentType: string | null;
  serviceDetails: UnarchiveAppointmentsSub_AppointmentsUnarchived_serviceDetails[] | null;
  patientType: string | null;
  duration: string | null;
  confirmedBy: string | null;
  deliveryMethod: string | null;
  urgency: string | null;
  additionalNote: string | null;
  recordType: NextAppointmentEntities | null;
  documentUrl: string[] | null;
}

export interface UnarchiveAppointmentsSub {
  AppointmentsUnarchived: UnarchiveAppointmentsSub_AppointmentsUnarchived[];
}

export interface UnarchiveAppointmentsSubVariables {
  profileId: string;
  hospitalId: string;
}
