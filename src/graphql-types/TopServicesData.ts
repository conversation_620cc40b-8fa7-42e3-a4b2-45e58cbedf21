/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: TopServicesData
// ====================================================

export interface TopServicesData_getTopServicesData_procedureResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
  totalAmount: number | null;
  totalQuantity: number | null;
  totalAmountDue: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
}

export interface TopServicesData_getTopServicesData_immunizationResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
  totalAmount: number | null;
  totalQuantity: number | null;
  totalAmountDue: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
}

export interface TopServicesData_getTopServicesData_medicationResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
}

export interface TopServicesData_getTopServicesData_investigationLabResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
  totalAmount: number | null;
  totalQuantity: number | null;
  totalAmountDue: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
}

export interface TopServicesData_getTopServicesData_investigationRadResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
  totalAmount: number | null;
  totalQuantity: number | null;
  totalAmountDue: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
}

export interface TopServicesData_getTopServicesData_admissionResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
  totalAmount: number | null;
  totalQuantity: number | null;
  totalAmountDue: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
}

export interface TopServicesData_getTopServicesData_consultationResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
  totalAmount: number | null;
  totalQuantity: number | null;
  totalAmountDue: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
}

export interface TopServicesData_getTopServicesData_medicationDispenseResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
  totalAmount: number | null;
  totalQuantity: number | null;
  totalAmountDue: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
}

export interface TopServicesData_getTopServicesData_medicationPrescribedResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
}

export interface TopServicesData_getTopServicesData_consumableDispensedResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
  totalAmount: number | null;
  totalQuantity: number | null;
  totalAmountDue: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
}

export interface TopServicesData_getTopServicesData_consumablePrescribedResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
}

export interface TopServicesData_getTopServicesData_investigationResult {
  __typename: "ServicesSummary";
  totalRequestedLaboratory: number | null;
  totalProcessedLaboratory: number | null;
  totalProcessedRadiology: number | null;
  totalRequestedRadiology: number | null;
}

export interface TopServicesData_getTopServicesData_nursingServiceResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
  totalAmount: number | null;
  totalQuantity: number | null;
  totalAmountDue: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
}

export interface TopServicesData_getTopServicesData_medicalReportResult {
  __typename: "TopServiceSummary";
  count: number | null;
  category: string | null;
  totalAmount: number | null;
  totalQuantity: number | null;
  totalAmountDue: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
}

export interface TopServicesData_getTopServicesData {
  __typename: "TopServicesSummary";
  procedureResult: TopServicesData_getTopServicesData_procedureResult[] | null;
  immunizationResult: TopServicesData_getTopServicesData_immunizationResult[] | null;
  medicationResult: TopServicesData_getTopServicesData_medicationResult[] | null;
  investigationLabResult: TopServicesData_getTopServicesData_investigationLabResult[] | null;
  investigationRadResult: TopServicesData_getTopServicesData_investigationRadResult[] | null;
  admissionResult: TopServicesData_getTopServicesData_admissionResult[] | null;
  consultationResult: TopServicesData_getTopServicesData_consultationResult[] | null;
  medicationDispenseResult: TopServicesData_getTopServicesData_medicationDispenseResult[] | null;
  medicationPrescribedResult: TopServicesData_getTopServicesData_medicationPrescribedResult[] | null;
  consumableDispensedResult: TopServicesData_getTopServicesData_consumableDispensedResult[] | null;
  consumablePrescribedResult: TopServicesData_getTopServicesData_consumablePrescribedResult[] | null;
  investigationResult: TopServicesData_getTopServicesData_investigationResult | null;
  nursingServiceResult: TopServicesData_getTopServicesData_nursingServiceResult[] | null;
  medicalReportResult: TopServicesData_getTopServicesData_medicalReportResult[] | null;
}

export interface TopServicesData {
  getTopServicesData: TopServicesData_getTopServicesData;
}

export interface TopServicesDataVariables {
  filter?: ServicesAnalyticsFilter | null;
}
