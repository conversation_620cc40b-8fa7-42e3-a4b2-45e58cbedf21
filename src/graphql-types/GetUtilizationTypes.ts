/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetUtilizationTypes
// ====================================================

export interface GetUtilizationTypes_getUtilizationTypes_list {
  __typename: "OptionObject";
  label: string;
  value: string;
}

export interface GetUtilizationTypes_getUtilizationTypes {
  __typename: "HmoUtilizationTypesResponse";
  list: GetUtilizationTypes_getUtilizationTypes_list[];
}

export interface GetUtilizationTypes {
  getUtilizationTypes: GetUtilizationTypes_getUtilizationTypes;
}

export interface GetUtilizationTypesVariables {
  providerId: string;
  utilizationId: string;
  clinifyId: string;
  visitationType: string;
  facilityId?: string | null;
}
