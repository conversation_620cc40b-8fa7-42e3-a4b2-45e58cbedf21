/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VisitationAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetVisitationsByDepartment
// ====================================================

export interface GetVisitationsByDepartment_getVisitationsByDepartment {
  __typename: "VisitationsByDepartment";
  department: string | null;
  count: number | null;
  name: number | null;
  patientFullName: string | null;
}

export interface GetVisitationsByDepartment {
  getVisitationsByDepartment: GetVisitationsByDepartment_getVisitationsByDepartment[];
}

export interface GetVisitationsByDepartmentVariables {
  filter?: VisitationAnalyticsFilter | null;
}
