/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VisitationAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetVisitationData
// ====================================================

export interface GetVisitationData_getVisitationData_groupedData_totalVisitations {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData_totalAppointmentsBooked {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData_totalAppointmentsCancelled {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData_totalAppointmentsCompleted {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData_totalAppointmentsMissed {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData_totalRegisteredPatients {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData_totalRegisteredPatientsUnderOneYear {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData_totalEmergency {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData_totalNonEmergency {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData_totalInPatient {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData_totalOutPatient {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData_totalReferral {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
  totalReferralIn: number | null;
  totalReferralOut: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData_totalWaitTimeInMinutes {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationData_getVisitationData_groupedData {
  __typename: "GroupedVisitationSummary";
  totalVisitations: GetVisitationData_getVisitationData_groupedData_totalVisitations[] | null;
  totalAppointmentsBooked: GetVisitationData_getVisitationData_groupedData_totalAppointmentsBooked[] | null;
  totalAppointmentsCancelled: GetVisitationData_getVisitationData_groupedData_totalAppointmentsCancelled[] | null;
  totalAppointmentsCompleted: GetVisitationData_getVisitationData_groupedData_totalAppointmentsCompleted[] | null;
  totalAppointmentsMissed: GetVisitationData_getVisitationData_groupedData_totalAppointmentsMissed[] | null;
  totalRegisteredPatients: GetVisitationData_getVisitationData_groupedData_totalRegisteredPatients[] | null;
  totalRegisteredPatientsUnderOneYear: GetVisitationData_getVisitationData_groupedData_totalRegisteredPatientsUnderOneYear[] | null;
  totalEmergency: GetVisitationData_getVisitationData_groupedData_totalEmergency[] | null;
  totalNonEmergency: GetVisitationData_getVisitationData_groupedData_totalNonEmergency[] | null;
  totalInPatient: GetVisitationData_getVisitationData_groupedData_totalInPatient[] | null;
  totalOutPatient: GetVisitationData_getVisitationData_groupedData_totalOutPatient[] | null;
  totalReferral: GetVisitationData_getVisitationData_groupedData_totalReferral[] | null;
  totalWaitTimeInMinutes: GetVisitationData_getVisitationData_groupedData_totalWaitTimeInMinutes[] | null;
}

export interface GetVisitationData_getVisitationData {
  __typename: "VisitationSummary";
  name: number | null;
  totalRegisteredPatients: number | null;
  totalRegisteredPatientsUnderOneYear: number | null;
  totalAppointmentsBooked: number | null;
  totalAppointmentsCompleted: number | null;
  totalAppointmentsMissed: number | null;
  totalAppointmentsCancelled: number | null;
  totalVisitations: number | null;
  totalEmergency: number | null;
  totalNonEmergency: number | null;
  totalInPatient: number | null;
  totalOutPatient: number | null;
  totalAdmissions: number | null;
  totalReferralOut: number | null;
  totalReferralIn: number | null;
  totalWaitTimeInMinutes: number | null;
  groupedData: GetVisitationData_getVisitationData_groupedData | null;
}

export interface GetVisitationData {
  getVisitationData: GetVisitationData_getVisitationData[];
}

export interface GetVisitationDataVariables {
  filter?: VisitationAnalyticsFilter | null;
}
