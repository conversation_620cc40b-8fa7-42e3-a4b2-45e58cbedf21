/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AdmissionAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetBedAvailabilityData
// ====================================================

export interface GetBedAvailabilityData_getBedAvailabilityData {
  __typename: "AdmissionSummary";
  totalBedOccupied: number | null;
  totalBedAvailable: number | null;
  name: number | null;
}

export interface GetBedAvailabilityData {
  getBedAvailabilityData: GetBedAvailabilityData_getBedAvailabilityData[];
}

export interface GetBedAvailabilityDataVariables {
  filter?: AdmissionAnalyticsFilter | null;
}
