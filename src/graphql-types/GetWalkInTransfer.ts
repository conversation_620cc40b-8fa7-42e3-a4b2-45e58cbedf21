/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetWalkInTransfer
// ====================================================

export interface GetWalkInTransfer_walkInTransfer_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface GetWalkInTransfer_walkInTransfer {
  __typename: "WalkInTransferModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  transferFacilityName: string | null;
  transferFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: GetWalkInTransfer_walkInTransfer_patientInformation | null;
}

export interface GetWalkInTransfer {
  walkInTransfer: GetWalkInTransfer_walkInTransfer;
}

export interface GetWalkInTransferVariables {
  id: string;
}
