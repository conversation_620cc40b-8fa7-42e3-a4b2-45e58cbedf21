/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ArchiveWalkInTransfers
// ====================================================

export interface ArchiveWalkInTransfers_archiveWalkInTransfers_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface ArchiveWalkInTransfers_archiveWalkInTransfers {
  __typename: "WalkInTransferModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  transferFacilityName: string | null;
  transferFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: ArchiveWalkInTransfers_archiveWalkInTransfers_patientInformation | null;
}

export interface ArchiveWalkInTransfers {
  archiveWalkInTransfers: ArchiveWalkInTransfers_archiveWalkInTransfers[];
}

export interface ArchiveWalkInTransfersVariables {
  ids: string[];
  archive?: boolean | null;
}
