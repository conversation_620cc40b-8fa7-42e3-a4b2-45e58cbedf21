/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: ChemoVerificationTemplate
// ====================================================

export interface ChemoVerificationTemplate_cycles_investigationDetails {
  __typename: "ChemoInvestigationDetails";
  investigationName: string | null;
  investigationType: string | null;
}

export interface ChemoVerificationTemplate_cycles_drugs {
  __typename: "ChemoDiagnosisDrugTemplate";
  day: string;
  dosage: string | null;
  dosagePercentage: string | null;
  infusionUsed: string | null;
  route: string | null;
  drugName: string;
  drugId: string | null;
  frequency: string | null;
  ref: string;
  note: string | null;
  inventoryClass: string | null;
}

export interface ChemoVerificationTemplate_cycles {
  __typename: "ChemoDiagnosisCycleModel";
  id: string;
  cycleNumber: number;
  investigationDetails: ChemoVerificationTemplate_cycles_investigationDetails[] | null;
  drugs: ChemoVerificationTemplate_cycles_drugs[] | null;
}

export interface ChemoVerificationTemplate {
  __typename: "ChemoDiagnosisTemplateModel";
  id: string;
  combinationName: string;
  creatorName: string | null;
  createdDate: any;
  type: string;
  section: string;
  cycles: ChemoVerificationTemplate_cycles[];
  updatedDate: any;
  lastModifierName: string | null;
}
