/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HospitalServiceInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddHospitalServices
// ====================================================

export interface AddHospitalServices_addHospitalServices_services {
  __typename: "HospitalService";
  id: string | null;
  name: string | null;
  createdOn: any | null;
  updatedOn: any | null;
  description: string | null;
  creatorName: string | null;
}

export interface AddHospitalServices_addHospitalServices {
  __typename: "HospitalModel";
  id: string;
  services: AddHospitalServices_addHospitalServices_services[] | null;
}

export interface AddHospitalServices {
  addHospitalServices: AddHospitalServices_addHospitalServices;
}

export interface AddHospitalServicesVariables {
  services: HospitalServiceInput[];
  csvImport?: boolean | null;
}
