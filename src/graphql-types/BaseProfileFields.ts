/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PatientProfileType, Gender } from "./globalTypes";

// ====================================================
// GraphQL fragment: BaseProfileFields
// ====================================================

export interface BaseProfileFields_fileNumbers {
  __typename: "FileNumberInputType";
  coverageRef: string | null;
  fileNumber: string | null;
  existingFamily: boolean | null;
}

export interface BaseProfileFields_coverageDetails_questionnaireData {
  __typename: "QuestionnaireData";
  employmentStatus: string | null;
  gender: string | null;
  highestFormalEducationalLevel: string | null;
  maritalStatus: string | null;
  numberOfHouseholdMembers: string | null;
  numberOfMattresses: string | null;
  numberOfMobilePhones: string | null;
  numberOfRooms: string | null;
  numberOfTVs: string | null;
  numberOfVehicles: string | null;
  occupationalGroup: string | null;
  occupation: string | null;
  primaryCookingImplement: string | null;
  relationshipToHouseholdHead: string | null;
  typeOfRoof: string | null;
  typeOfToilet: string | null;
  /**
   * Score in percentage
   */
  questionnaireScore: number | null;
}

export interface BaseProfileFields_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface BaseProfileFields_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  coverageType: string | null;
  companyName: string | null;
  companyAddress: string | null;
  familyName: string | null;
  familyAddress: string | null;
  name: string | null;
  contactAddress: string | null;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  capturedDate: any | null;
  capturedBy: string | null;
  enrolledBy: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  paymentFrequency: string | null;
  memberStartDate: any | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  capitatedMember: string | null;
  capitatedAmount: string | null;
  employeeType: string | null;
  employeeDivision: string | null;
  occupation: string | null;
  retired: boolean | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  terminationDate: any | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionRate: string | null;
  commissionPayable: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
  employerCode: string | null;
  activationDatetime: any | null;
  totalPremiumAmountPaid: string | null;
  questionnaireData: BaseProfileFields_coverageDetails_questionnaireData | null;
  provider: BaseProfileFields_coverageDetails_provider | null;
}

export interface BaseProfileFields_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
}

export interface BaseProfileFields {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
  active: boolean;
  isDefault: boolean;
  createdDate: any | null;
  updatedDate: any | null;
  patientProfileType: PatientProfileType | null;
  dataAccessType: string | null;
  type: string;
  typeAlias: string | null;
  title: string | null;
  gender: Gender | null;
  patientStatus: string | null;
  deathDateTime: any | null;
  deathLocation: string | null;
  causeOfDeath: string | null;
  creatorName: string | null;
  lastModifierName: string | null;
  billStatus: string | null;
  createdFromHmo: boolean | null;
  shareData: boolean | null;
  fileNumbers: BaseProfileFields_fileNumbers[] | null;
  coverageDetails: BaseProfileFields_coverageDetails[] | null;
  serviceDetails: BaseProfileFields_serviceDetails[] | null;
}
