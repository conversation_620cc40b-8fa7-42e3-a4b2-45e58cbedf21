/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { TransactionType, TransactionStatus, Currency } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetWalletTransaction
// ====================================================

export interface GetWalletTransaction_getWalletTransaction_senderWallet_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
}

export interface GetWalletTransaction_getWalletTransaction_senderWallet_hospital {
  __typename: "HospitalModel";
  id: string;
  clinifyId: string | null;
  name: string | null;
}

export interface GetWalletTransaction_getWalletTransaction_senderWallet {
  __typename: "WalletModel";
  profile: GetWalletTransaction_getWalletTransaction_senderWallet_profile | null;
  hospital: GetWalletTransaction_getWalletTransaction_senderWallet_hospital | null;
}

export interface GetWalletTransaction_getWalletTransaction_receiverWallet_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
}

export interface GetWalletTransaction_getWalletTransaction_receiverWallet_hospital {
  __typename: "HospitalModel";
  id: string;
  clinifyId: string | null;
  name: string | null;
}

export interface GetWalletTransaction_getWalletTransaction_receiverWallet {
  __typename: "WalletModel";
  profile: GetWalletTransaction_getWalletTransaction_receiverWallet_profile | null;
  hospital: GetWalletTransaction_getWalletTransaction_receiverWallet_hospital | null;
}

export interface GetWalletTransaction_getWalletTransaction_senderBankDetails {
  __typename: "BankAccountInformation";
  accountNumber: string;
  accountName: string | null;
  bankName: string;
}

export interface GetWalletTransaction_getWalletTransaction {
  __typename: "WalletTransactionModel";
  id: string;
  createdDate: any;
  amount: number;
  transactionType: TransactionType;
  transactionStatus: TransactionStatus;
  transactionDetails: string;
  currency: Currency;
  amountSent: number;
  description: string;
  senderWallet: GetWalletTransaction_getWalletTransaction_senderWallet | null;
  receiverWallet: GetWalletTransaction_getWalletTransaction_receiverWallet | null;
  senderBankDetails: GetWalletTransaction_getWalletTransaction_senderBankDetails | null;
}

export interface GetWalletTransaction {
  getWalletTransaction: GetWalletTransaction_getWalletTransaction;
}

export interface GetWalletTransactionVariables {
  id: string;
}
