/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DateRangeInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetEnrolledEnrollees
// ====================================================

export interface GetEnrolledEnrollees_getEnrolledEnrollees {
  __typename: "EnrolledEnrolleeResponse";
  memberNumber: string | null;
  membershipNumber: string | null;
  planCategory: string | null;
  title: string | null;
  fullName: string | null;
  companyName: string | null;
  gender: string | null;
  phoneNumber: string | null;
  dateOfBirth: number | null;
  maritalStatus: string | null;
  relationship: string | null;
  memberStatus: string | null;
  premiumCollected: string | null;
  enrollmentDateTime: number | null;
  createdDate: number | null;
  memberStartDate: number | null;
  memberDueDate: number | null;
  renewalCount: string | null;
  providerName: string | null;
  ownership: string | null;
  tariffBand: string | null;
  enrolledBy: string | null;
  agentName: string | null;
  agentPhoneNumber: string | null;
  salesWing: string | null;
  channelDivisions: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  administrationAgency: string | null;
  memberPlan: string | null;
  paymentFrequency: string | null;
  registrationSource: string | null;
  lga: string | null;
  commissionRate: string | null;
  commissionPayable: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  referrer: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
}

export interface GetEnrolledEnrollees {
  getEnrolledEnrollees: GetEnrolledEnrollees_getEnrolledEnrollees[];
}

export interface GetEnrolledEnrolleesVariables {
  dateRange: DateRangeInput;
}
