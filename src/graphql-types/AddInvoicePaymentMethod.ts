/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InvoicePaymentInput, PayoutStatus, CommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddInvoicePaymentMethod
// ====================================================

export interface AddInvoicePaymentMethod_addInvoicePaymentMethod_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
}

export interface AddInvoicePaymentMethod_addInvoicePaymentMethod_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
}

export interface AddInvoicePaymentMethod_addInvoicePaymentMethod {
  __typename: "InvoicePaymentModel";
  id: string;
  invoiceId: string;
  amountPaid: number;
  paymentMethod: string;
  paymentStatus: string;
  amountDue: number;
  commissionFeeAmount: number | null;
  commissionFeePercentage: number | null;
  createdDate: any;
  updatedDate: any | null;
  payoutStatus: PayoutStatus;
  commissionPayer: CommissionPayer | null;
  discountAmount: number;
  createdBy: AddInvoicePaymentMethod_addInvoicePaymentMethod_createdBy;
  updatedBy: AddInvoicePaymentMethod_addInvoicePaymentMethod_updatedBy | null;
  creatorName: string;
  lastModifierName: string | null;
}

export interface AddInvoicePaymentMethod {
  addInvoicePaymentMethod: AddInvoicePaymentMethod_addInvoicePaymentMethod;
}

export interface AddInvoicePaymentMethodVariables {
  id: string;
  input: InvoicePaymentInput;
}
