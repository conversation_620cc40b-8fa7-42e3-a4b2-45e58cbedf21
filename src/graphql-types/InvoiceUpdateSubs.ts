/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InvoiceStatus, PercentOrAmount, Currency, VirtualAccountProvider } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: InvoiceUpdateSubs
// ====================================================

export interface InvoiceUpdateSubs_InvoiceUpdated_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface InvoiceUpdateSubs_InvoiceUpdated_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface InvoiceUpdateSubs_InvoiceUpdated_recipient {
  __typename: "InvoiceRecipient";
  address: string | null;
  email: string | null;
  name: string;
  phone: string | null;
  clinifyId: string | null;
}

export interface InvoiceUpdateSubs_InvoiceUpdated_senderHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
  facilityLogo: string | null;
  website: string | null;
}

export interface InvoiceUpdateSubs_InvoiceUpdated_virtualAccount {
  __typename: "VirtualBankAccountModel";
  id: string;
  currency: Currency;
  updatedDate: any;
  createdDate: any;
  accountName: string;
  accountNumber: string;
  bank: VirtualAccountProvider;
  expiryDate: any | null;
}

export interface InvoiceUpdateSubs_InvoiceUpdated_employeesDetails_dependents {
  __typename: "DependentInfo";
  fullName: string;
  enrolleeId: string | null;
  relationship: string | null;
}

export interface InvoiceUpdateSubs_InvoiceUpdated_employeesDetails {
  __typename: "EmployeesInfo";
  enrolleeId: string | null;
  fullName: string;
  planCategory: string | null;
  planType: string | null;
  planAmount: number | null;
  paymentFrequency: string | null;
  planStartDate: any | null;
  planDueDate: any | null;
  planStatus: string | null;
  isCovered: boolean | null;
  dependents: InvoiceUpdateSubs_InvoiceUpdated_employeesDetails_dependents[] | null;
}

export interface InvoiceUpdateSubs_InvoiceUpdated_sponsorDetails {
  __typename: "SponsorEnrolleeDetails";
  status: string | null;
  fullName: string | null;
  isCovered: boolean | null;
  memberNumber: string | null;
  planDueDate: any | null;
  planStartDate: any | null;
}

export interface InvoiceUpdateSubs_InvoiceUpdated_invoiceItems {
  __typename: "InvoiceItemModel";
  id: string;
  description: string;
  updatedDate: any;
  createdDate: any;
  invoiceType: string | null;
  discountAmount: number | null;
  discountPercentage: number | null;
  invoiceId: string;
  quantity: number;
  unitPrice: number | null;
}

export interface InvoiceUpdateSubs_InvoiceUpdated {
  __typename: "InvoiceModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  discountPercentage: number | null;
  discountAmount: number | null;
  profileId: string | null;
  employerId: string | null;
  creatorId: string;
  description: string | null;
  subTotal: number;
  /**
   * Returns total amount in lowest denomination
   */
  totalAmount: number;
  createdBy: InvoiceUpdateSubs_InvoiceUpdated_createdBy;
  lastModifierId: string | null;
  updatedBy: InvoiceUpdateSubs_InvoiceUpdated_updatedBy | null;
  additionalNote: string | null;
  amountPaid: number;
  bankTransactionIds: string[] | null;
  dueDate: any;
  invoiceReference: string;
  invoiceStatus: InvoiceStatus;
  issueDate: any;
  paymentDate: any | null;
  sponsorName: string | null;
  sponsorRef: string | null;
  nextYearlyPremium: number | null;
  sponsorLivesCovered: number | null;
  agencyLivesCovered: number | null;
  sponsorPremiumPerLives: number | null;
  recipient: InvoiceUpdateSubs_InvoiceUpdated_recipient;
  senderHospitalId: string | null;
  senderHospital: InvoiceUpdateSubs_InvoiceUpdated_senderHospital;
  professionalFeeAmount: number | null;
  professionalFeePercentage: number | null;
  controlledDiscount: PercentOrAmount | null;
  controlledProfessionalFee: PercentOrAmount | null;
  vatPercentage: number | null;
  vatAmount: number | null;
  controlledVat: PercentOrAmount | null;
  virtualAccount: InvoiceUpdateSubs_InvoiceUpdated_virtualAccount | null;
  employeesDetails: InvoiceUpdateSubs_InvoiceUpdated_employeesDetails[] | null;
  sponsorDetails: InvoiceUpdateSubs_InvoiceUpdated_sponsorDetails[] | null;
  paymentFrequency: string | null;
  plasticIdCardCount: number | null;
  plasticIdCardAmount: number | null;
  laminatedIdCardCount: number | null;
  laminatedIdCardAmount: number | null;
  creatorName: string;
  lastModifierName: string | null;
  periodStartDate: any | null;
  periodEndDate: any | null;
  invoiceItems: InvoiceUpdateSubs_InvoiceUpdated_invoiceItems[];
}

export interface InvoiceUpdateSubs {
  InvoiceUpdated: InvoiceUpdateSubs_InvoiceUpdated;
}

export interface InvoiceUpdateSubsVariables {
  hospitalId: string;
  profileId: string;
}
