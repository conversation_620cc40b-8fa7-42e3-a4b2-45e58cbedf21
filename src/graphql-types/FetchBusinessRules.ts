/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: FetchBusinessRules
// ====================================================

export interface FetchBusinessRules_fetchBusinessRules_items_extra {
  __typename: "BusinessRuleItemExtra";
  frequencyUnit: string | null;
  frequencyTarget: string | null;
  frequencyTargetValue: string | null;
  frequencyTargetUnit: string | null;
  frequencyTargetQuantity: string | null;
  frequencyTargetOperator: string | null;
}

export interface FetchBusinessRules_fetchBusinessRules_items {
  __typename: "BusinessRuleItemModel";
  id: string;
  type: string;
  category: string;
  operator: string;
  value: string;
  extra: FetchBusinessRules_fetchBusinessRules_items_extra | null;
}

export interface FetchBusinessRules_fetchBusinessRules {
  __typename: "BusinessRuleModel";
  id: string;
  flag: string | null;
  matchAll: boolean | null;
  sumAll: boolean | null;
  items: FetchBusinessRules_fetchBusinessRules_items[];
}

export interface FetchBusinessRules {
  fetchBusinessRules: FetchBusinessRules_fetchBusinessRules[];
}
