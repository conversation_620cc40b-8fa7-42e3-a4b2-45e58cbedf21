/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicationBundleItemInput, MedicationOptionType } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: SaveMedicationBundleItem
// ====================================================

export interface SaveMedicationBundleItem_addMedicationBundleItem_medicationConsumables {
  __typename: "MedicationConsumables";
  drugInventoryId: string | null;
  name: string | null;
  quantity: string | null;
  unitPrice: number | null;
  inventoryClass: string | null;
}

export interface SaveMedicationBundleItem_addMedicationBundleItem_priceDetails {
  __typename: "MedPriceDetailInputType";
  type: string | null;
  name: string | null;
  pricePerUnit: string | null;
  patientType: string | null;
  paymentType: string | null;
  quantity: string | null;
}

export interface SaveMedicationBundleItem_addMedicationBundleItem_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface SaveMedicationBundleItem_addMedicationBundleItem {
  __typename: "MedicationBundleItemModel";
  id: string;
  administrationMethod: string | null;
  bank: string | null;
  bundleDate: any;
  createdDate: any;
  creatorId: string | null;
  creatorName: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  duration: string | null;
  frequency: string | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  medicationCategory: string | null;
  drugInventoryId: string | null;
  inventoryClass: string | null;
  medicationConsumables: SaveMedicationBundleItem_addMedicationBundleItem_medicationConsumables[] | null;
  priceDetails: SaveMedicationBundleItem_addMedicationBundleItem_priceDetails | null;
  provider: string | null;
  medicationName: string | null;
  option: MedicationOptionType | null;
  purpose: string | null;
  quantity: string | null;
  unitPrice: string | null;
  updatedDate: any;
  prescriptionNote: string | null;
  medicationType: string | null;
  diagnosis: SaveMedicationBundleItem_addMedicationBundleItem_diagnosis[] | null;
  medicationBundleId: string;
}

export interface SaveMedicationBundleItem {
  addMedicationBundleItem: SaveMedicationBundleItem_addMedicationBundleItem;
}

export interface SaveMedicationBundleItemVariables {
  id: string;
  input: MedicationBundleItemInput;
}
