/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { LabResultTrendFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetLabResultTrend
// ====================================================

export interface GetLabResultTrend_findLabResultTrend_testResults {
  __typename: "LabResultTrendDetail";
  name: string;
  extraValue: string | null;
  value: string | null;
  unit: string | null;
  valueTwo: string | null;
  hasExtraValue: boolean | null;
  range: string | null;
}

export interface GetLabResultTrend_findLabResultTrend {
  __typename: "LabResultTrendResponse";
  resultDate: any | null;
  testDate: any;
  testName: string;
  testResults: GetLabResultTrend_findLabResultTrend_testResults[];
}

export interface GetLabResultTrend {
  findLabResultTrend: GetLabResultTrend_findLabResultTrend[];
}

export interface GetLabResultTrendVariables {
  filter: LabResultTrendFilterInput;
}
