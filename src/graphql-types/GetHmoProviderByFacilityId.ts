/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetHmoProviderByFacilityId
// ====================================================

export interface GetHmoProviderByFacilityId_getHmoProviderByFacilityId_list {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface GetHmoProviderByFacilityId_getHmoProviderByFacilityId {
  __typename: "HmoProviderResponse";
  totalCount: number;
  list: GetHmoProviderByFacilityId_getHmoProviderByFacilityId_list[];
}

export interface GetHmoProviderByFacilityId {
  getHmoProviderByFacilityId: GetHmoProviderByFacilityId_getHmoProviderByFacilityId;
}
