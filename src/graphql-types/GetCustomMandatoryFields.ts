/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetCustomMandatoryFields
// ====================================================

export interface GetCustomMandatoryFields_getFacilityPreference_mandatoryFields {
  __typename: "MandatoryFields";
  admission: string[] | null;
  allergy: string[] | null;
  consultation: string[] | null;
  antenatal: string[] | null;
  immunization: string[] | null;
  medication: string[] | null;
  medicationDetails: string[] | null;
  procedure: string[] | null;
  bloodTransfusion: string[] | null;
  dischargePatient: string[] | null;
  transferPatient: string[] | null;
  admissionInput: string[] | null;
  admissionOutput: string[] | null;
  admissionLine: string[] | null;
  treatmentPlan: string[] | null;
  vitals: string[] | null;
  anthropometry: string[] | null;
  bloodGlucose: string[] | null;
  bloodPressure: string[] | null;
  pain: string[] | null;
  respiratoryRate: string[] | null;
  temperature: string[] | null;
  urineDipstick: string[] | null;
  visualAcuity: string[] | null;
  investigation: string[] | null;
  radiologyExam: string[] | null;
  postnatal: string[] | null;
  labourAndDelivery: string[] | null;
  nextOfKin: string[] | null;
  dependents: string[] | null;
  medicalReport: string[] | null;
  admissionNotes: string[] | null;
  nursingServices: string[] | null;
  oncology: string[] | null;
  requestProcedure: string[] | null;
  laboratory: string[] | null;
  radiology: string[] | null;
  postOperationChecklist: string[] | null;
  preChemoEducation: string[] | null;
  cancerScreening: string[] | null;
}

export interface GetCustomMandatoryFields_getFacilityPreference {
  __typename: "FacilityPreferenceModel";
  id: string;
  mandatoryFields: GetCustomMandatoryFields_getFacilityPreference_mandatoryFields | null;
  hospitalId: string;
  lastModifierName: string | null;
  updatedDate: any;
}

export interface GetCustomMandatoryFields {
  getFacilityPreference: GetCustomMandatoryFields_getFacilityPreference;
}

export interface GetCustomMandatoryFieldsVariables {
  hospitalId: string;
}
