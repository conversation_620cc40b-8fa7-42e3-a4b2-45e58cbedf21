/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DateRangeInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: FetchHmoPayments
// ====================================================

export interface FetchHmoPayments_getHospitalHmoPayments_list {
  __typename: "HmoHospitalPayment";
  QAOn: any;
  adjudicatedOn: any;
  batchDate: any;
  batchNumber: string;
  batchPaidAmount: string;
  batchRefundDate: any;
  batchStatusId: string;
  batchTotal: string;
  batchedBy: string;
  encounterMonth: any;
  maximumDate: any;
  minimumDate: any;
  provider: string;
  providerBatchStatus: string;
  receiveDateReception: any;
  reference: string;
  statusDescription: string;
  units: string;
}

export interface FetchHmoPayments_getHospitalHmoPayments {
  __typename: "HmoHospitalPaymentsResponse";
  list: FetchHmoPayments_getHospitalHmoPayments_list[];
}

export interface FetchHmoPayments {
  getHospitalHmoPayments: FetchHmoPayments_getHospitalHmoPayments;
}

export interface FetchHmoPaymentsVariables {
  filterOptions?: DateRangeInput | null;
  hospitalId: string;
  providerId: string;
}
