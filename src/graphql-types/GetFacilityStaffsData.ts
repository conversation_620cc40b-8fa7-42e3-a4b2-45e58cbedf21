/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FacilityStaffsAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetFacilityStaffsData
// ====================================================

export interface GetFacilityStaffsData_getFacilityStaffsData_staffSummary {
  __typename: "StaffSummaryType";
  hospitalName: string | null;
  hospitalId: string | null;
  totalActiveStaffs: number | null;
  totalFemaleStaffs: number | null;
  totalInActiveStaffs: number | null;
  totalMaleStaffs: number | null;
  totalStaffs: number | null;
}

export interface GetFacilityStaffsData_getFacilityStaffsData_rolesSummary {
  __typename: "RoleSummaryType";
  hospitalName: string | null;
  count: number | null;
}

export interface GetFacilityStaffsData_getFacilityStaffsData {
  __typename: "FacilityStaffsData";
  name: number | null;
  totalStaffs: number | null;
  totalMaleStaffs: number | null;
  totalFemaleStaffs: number | null;
  totalActiveStaffs: number | null;
  totalInActiveStaffs: number | null;
  totalRoles: number | null;
  staffSummary: GetFacilityStaffsData_getFacilityStaffsData_staffSummary[] | null;
  rolesSummary: GetFacilityStaffsData_getFacilityStaffsData_rolesSummary[] | null;
}

export interface GetFacilityStaffsData {
  getFacilityStaffsData: GetFacilityStaffsData_getFacilityStaffsData[];
}

export interface GetFacilityStaffsDataVariables {
  filter?: FacilityStaffsAnalyticsFilter | null;
}
