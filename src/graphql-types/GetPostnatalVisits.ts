/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MaternalHealthAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPostnatalVisits
// ====================================================

export interface GetPostnatalVisits_getPostnatalVisits {
  __typename: "MaternalHealthSummary";
  name: number | null;
  totalPostnatalVisits: number | null;
}

export interface GetPostnatalVisits {
  getPostnatalVisits: GetPostnatalVisits_getPostnatalVisits;
}

export interface GetPostnatalVisitsVariables {
  filter?: MaternalHealthAnalyticsFilter | null;
}
