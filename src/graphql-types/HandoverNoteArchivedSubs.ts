/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HandoverNoteArchivedSubs
// ====================================================

export interface HandoverNoteArchivedSubs_HandoverNoteArchived_staffs_staffProfile {
  __typename: "ProfileModel";
  fullName: string;
}

export interface HandoverNoteArchivedSubs_HandoverNoteArchived_staffs {
  __typename: "HandoverStaffModel";
  id: string;
  staffProfile: HandoverNoteArchivedSubs_HandoverNoteArchived_staffs_staffProfile | null;
}

export interface HandoverNoteArchivedSubs_HandoverNoteArchived_handoverBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface HandoverNoteArchivedSubs_HandoverNoteArchived_items_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
}

export interface HandoverNoteArchivedSubs_HandoverNoteArchived_items {
  __typename: "HandoverNoteItemModel";
  id: string;
  patientProfileId: string;
  patientInformation: HandoverNoteArchivedSubs_HandoverNoteArchived_items_patientInformation;
}

export interface HandoverNoteArchivedSubs_HandoverNoteArchived {
  __typename: "HandoverNoteModel";
  id: string;
  name: string;
  handoverDateTime: any;
  updatedDate: any | null;
  createdDate: any;
  hospitalId: string;
  creatorId: string;
  department: string | null;
  handoverById: string;
  specialty: string | null;
  staffs: HandoverNoteArchivedSubs_HandoverNoteArchived_staffs[];
  handoverBy: HandoverNoteArchivedSubs_HandoverNoteArchived_handoverBy;
  items: HandoverNoteArchivedSubs_HandoverNoteArchived_items[];
}

export interface HandoverNoteArchivedSubs {
  HandoverNoteArchived: HandoverNoteArchivedSubs_HandoverNoteArchived;
}

export interface HandoverNoteArchivedSubsVariables {
  profileId: string;
  hospitalId?: string | null;
  userType: UserType;
}
