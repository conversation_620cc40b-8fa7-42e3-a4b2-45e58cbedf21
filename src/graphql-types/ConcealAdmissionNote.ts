/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealAdmissionNote
// ====================================================

export interface ConcealAdmissionNote_concealAdmissionNote {
  __typename: "AdmissionNoteModel";
  id: string;
  creatorProfileType: string;
  note: string | null;
  conceal: boolean | null;
}

export interface ConcealAdmissionNote {
  concealAdmissionNote: ConcealAdmissionNote_concealAdmissionNote;
}

export interface ConcealAdmissionNoteVariables {
  id: string;
  concealStatus: boolean;
}
