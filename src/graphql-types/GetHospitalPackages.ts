/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PackageFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHospitalPackages
// ====================================================

export interface GetHospitalPackages_hospital_packages_list_serviceDetails {
  __typename: "ServiceDetailsFields";
  serviceType: string | null;
  serviceName: string | null;
  /**
   * Extra information about the service in json format
   */
  extraInformation: string | null;
}

export interface GetHospitalPackages_hospital_packages_list {
  __typename: "PackageModel";
  id: string;
  packageDate: any;
  name: string;
  price: string | null;
  gender: string;
  serviceDetails: GetHospitalPackages_hospital_packages_list_serviceDetails[] | null;
  createdDate: any;
  updatedDate: any;
}

export interface GetHospitalPackages_hospital_packages {
  __typename: "PackageResponse";
  totalCount: number;
  list: GetHospitalPackages_hospital_packages_list[];
}

export interface GetHospitalPackages_hospital {
  __typename: "HospitalModel";
  id: string;
  packages: GetHospitalPackages_hospital_packages;
}

export interface GetHospitalPackages {
  hospital: GetHospitalPackages_hospital;
}

export interface GetHospitalPackagesVariables {
  filterOptions?: PackageFilterInput | null;
}
