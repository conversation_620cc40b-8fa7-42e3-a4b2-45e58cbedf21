/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EnrollmentAgencyFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetEnrollmentAgencies
// ====================================================

export interface GetEnrollmentAgencies_enrollmentAgencies_list_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface GetEnrollmentAgencies_enrollmentAgencies_list_tpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
}

export interface GetEnrollmentAgencies_enrollmentAgencies_list_primaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetEnrollmentAgencies_enrollmentAgencies_list_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetEnrollmentAgencies_enrollmentAgencies_list_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetEnrollmentAgencies_enrollmentAgencies_list_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetEnrollmentAgencies_enrollmentAgencies_list {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
  agencyCode: string | null;
  address: string | null;
  country: string | null;
  state: string | null;
  agencyType: string | null;
  profile: GetEnrollmentAgencies_enrollmentAgencies_list_profile | null;
  tpaNonTpa: GetEnrollmentAgencies_enrollmentAgencies_list_tpaNonTpa | null;
  localGovernmentArea: string | null;
  primaryPhoneNumber: GetEnrollmentAgencies_enrollmentAgencies_list_primaryPhoneNumber | null;
  primaryEmailAddress: string | null;
  secondaryPhoneNumber: GetEnrollmentAgencies_enrollmentAgencies_list_secondaryPhoneNumber | null;
  secondaryEmailAddress: string | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: GetEnrollmentAgencies_enrollmentAgencies_list_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: GetEnrollmentAgencies_enrollmentAgencies_list_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  accountName: string | null;
  accountNumber: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface GetEnrollmentAgencies_enrollmentAgencies {
  __typename: "EnrollmentAgencyResponse";
  totalCount: number;
  list: GetEnrollmentAgencies_enrollmentAgencies_list[];
}

export interface GetEnrollmentAgencies {
  enrollmentAgencies: GetEnrollmentAgencies_enrollmentAgencies;
}

export interface GetEnrollmentAgenciesVariables {
  filter?: EnrollmentAgencyFilterInput | null;
}
