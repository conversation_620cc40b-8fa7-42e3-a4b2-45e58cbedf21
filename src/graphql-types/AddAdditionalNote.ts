/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AdditionalNoteInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddAdditionalNote
// ====================================================

export interface AddAdditionalNote_addAdditionalNote {
  __typename: "AdditionalNoteModel";
  id: string;
  additionalNote: string | null;
  conceal: boolean | null;
}

export interface AddAdditionalNote {
  addAdditionalNote: AddAdditionalNote_addAdditionalNote;
}

export interface AddAdditionalNoteVariables {
  input: AdditionalNoteInput;
  id: string;
  clinifyId: string;
  pin?: string | null;
}
