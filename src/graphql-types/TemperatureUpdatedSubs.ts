/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: TemperatureUpdatedSubs
// ====================================================

export interface TemperatureUpdatedSubs_TemperatureUpdated {
  __typename: "TemperatureModel";
  id: string;
  readingDateTime: any | null;
  checkMethod: string | null;
  reading: string | null;
  readingUnit: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
}

export interface TemperatureUpdatedSubs {
  TemperatureUpdated: TemperatureUpdatedSubs_TemperatureUpdated;
}

export interface TemperatureUpdatedSubsVariables {
  profileId: string;
}
