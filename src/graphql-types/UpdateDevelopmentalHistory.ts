/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DevelopmentalHistoryInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateDevelopmentalHistory
// ====================================================

export interface UpdateDevelopmentalHistory_updateDevelopmentalHistory_milestones {
  __typename: "DevelopmentalHistoryMilestoneModel";
  id: string;
  milestone: string;
  ageRange: string;
  whenMet: string | null;
}

export interface UpdateDevelopmentalHistory_updateDevelopmentalHistory {
  __typename: "DevelopmentalHistoryModel";
  id: string;
  groupName: string;
  milestones: UpdateDevelopmentalHistory_updateDevelopmentalHistory_milestones[] | null;
}

export interface UpdateDevelopmentalHistory {
  updateDevelopmentalHistory: UpdateDevelopmentalHistory_updateDevelopmentalHistory;
}

export interface UpdateDevelopmentalHistoryVariables {
  input: DevelopmentalHistoryInput;
}
