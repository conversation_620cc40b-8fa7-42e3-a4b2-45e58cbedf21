/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HandoverStaffUpdatedSubs
// ====================================================

export interface HandoverStaffUpdatedSubs_HandoverStaffUpdated_staffProfile {
  __typename: "ProfileModel";
  fullName: string;
}

export interface HandoverStaffUpdatedSubs_HandoverStaffUpdated {
  __typename: "HandoverStaffModel";
  id: string;
  status: string | null;
  currentShift: string | null;
  nextShift: string | null;
  staffProfileId: string | null;
  staffProfile: HandoverStaffUpdatedSubs_HandoverStaffUpdated_staffProfile | null;
  handoverNoteId: string;
  createdDate: any;
  updatedDate: any;
  creatorId: string | null;
}

export interface HandoverStaffUpdatedSubs {
  HandoverStaffUpdated: HandoverStaffUpdatedSubs_HandoverStaffUpdated;
}

export interface HandoverStaffUpdatedSubsVariables {
  profileId: string;
  hospitalId?: string | null;
  userType: UserType;
}
