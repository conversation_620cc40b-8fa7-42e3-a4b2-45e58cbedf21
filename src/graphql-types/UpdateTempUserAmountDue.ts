/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillStatus, VirtualAccountProvider } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateTempUserAmountDue
// ====================================================

export interface UpdateTempUserAmountDue_updateAmountDueOnTempUser_virtualBankAccount {
  __typename: "VirtualBankAccountModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bank: VirtualAccountProvider;
}

export interface UpdateTempUserAmountDue_updateAmountDueOnTempUser_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface UpdateTempUserAmountDue_updateAmountDueOnTempUser {
  __typename: "TempUserModel";
  id: string;
  amountDue: number | null;
  phoneNumber: string;
  amountPaid: number | null;
  paymentStatus: BillStatus;
  hospitalId: string | null;
  virtualBankAccount: UpdateTempUserAmountDue_updateAmountDueOnTempUser_virtualBankAccount | null;
  hospital: UpdateTempUserAmountDue_updateAmountDueOnTempUser_hospital | null;
}

export interface UpdateTempUserAmountDue {
  updateAmountDueOnTempUser: UpdateTempUserAmountDue_updateAmountDueOnTempUser;
}

export interface UpdateTempUserAmountDueVariables {
  id: string;
  amountDue: number;
}
