/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InputDetailsInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateInputDetail
// ====================================================

export interface UpdateInputDetail_updateInputDetail {
  __typename: "InputDetailsModel";
  id: string;
  administrationDateTime: any | null;
  administratorName: string | null;
  inputFluidType: string | null;
  routeOfAdministration: string | null;
  inputQuantity: string | null;
  inputQuantityUnit: string | null;
  duration: string | null;
}

export interface UpdateInputDetail {
  updateInputDetail: UpdateInputDetail_updateInputDetail;
}

export interface UpdateInputDetailVariables {
  input: InputDetailsInput;
  id: string;
  clinifyId: string;
  pin?: string | null;
}
