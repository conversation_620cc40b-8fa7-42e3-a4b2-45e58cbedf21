/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ConsultationFilterInput, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetOutPatientList
// ====================================================

export interface GetOutPatientList_hospital_consultations_list_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface GetOutPatientList_hospital_consultations_list_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: GetOutPatientList_hospital_consultations_list_profile_coverageDetails_provider | null;
}

export interface GetOutPatientList_hospital_consultations_list_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  coverageDetails: GetOutPatientList_hospital_consultations_list_profile_coverageDetails[] | null;
}

export interface GetOutPatientList_hospital_consultations_list_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface GetOutPatientList_hospital_consultations_list_complaintSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface GetOutPatientList_hospital_consultations_list_systemReviewSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface GetOutPatientList_hospital_consultations_list_physicalExamSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface GetOutPatientList_hospital_consultations_list_provisionalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface GetOutPatientList_hospital_consultations_list_finalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface GetOutPatientList_hospital_consultations_list_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface GetOutPatientList_hospital_consultations_list_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: GetOutPatientList_hospital_consultations_list_preauthorizationDetails_provider | null;
}

export interface GetOutPatientList_hospital_consultations_list_bill {
  __typename: "BillModel";
  id: string;
  createdDate: any;
}

export interface GetOutPatientList_hospital_consultations_list {
  __typename: "ConsultationModel";
  profile: GetOutPatientList_hospital_consultations_list_profile | null;
  id: string;
  consultationDateTime: any | null;
  duration: string | null;
  doctorName: string;
  priority: string | null;
  specialty: string | null;
  class: string | null;
  hmoProviderId: string | null;
  serviceDetails: GetOutPatientList_hospital_consultations_list_serviceDetails[] | null;
  isPackage: boolean;
  clinicName: string | null;
  clinicAddress: string | null;
  complaint: string | null;
  provider: string | null;
  providerServiceName: string | null;
  complaintHistory: string | null;
  healthEducation: string | null;
  systemReview: string | null;
  systemReviewSmartText: string | null;
  complaintGender: string | null;
  complaintSmartSelection: GetOutPatientList_hospital_consultations_list_complaintSmartSelection | null;
  systemReviewSmartSelection: GetOutPatientList_hospital_consultations_list_systemReviewSmartSelection | null;
  category: string | null;
  department: string | null;
  physicalExam: string | null;
  physicalExamSmartText: string | null;
  physicalExamSmartSelection: GetOutPatientList_hospital_consultations_list_physicalExamSmartSelection | null;
  referral: boolean | null;
  referralDate: any | null;
  referralReason: string | null;
  concealReferralReason: boolean | null;
  specialtyReferredTo: string | null;
  profileId: string | null;
  externalReferral: boolean | null;
  provisionalDiagnosis: GetOutPatientList_hospital_consultations_list_provisionalDiagnosis[] | null;
  finalDiagnosis: GetOutPatientList_hospital_consultations_list_finalDiagnosis[] | null;
  consultationStartDate: any | null;
  consultationEndDate: any | null;
  appointmentId: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  concealComplaint: boolean | null;
  concealComplaintHistory: boolean | null;
  concealPhysicalExam: boolean | null;
  concealSystemReview: boolean | null;
  concealHealthEducation: boolean | null;
  billStatus: string | null;
  audiometry: string | null;
  concealAudiometry: boolean | null;
  hospitalId: string | null;
  preauthorizationDetails: GetOutPatientList_hospital_consultations_list_preauthorizationDetails | null;
  bill: GetOutPatientList_hospital_consultations_list_bill | null;
}

export interface GetOutPatientList_hospital_consultations {
  __typename: "ConsultationResponse";
  totalCount: number;
  list: GetOutPatientList_hospital_consultations_list[];
}

export interface GetOutPatientList_hospital {
  __typename: "HospitalModel";
  id: string;
  consultations: GetOutPatientList_hospital_consultations;
}

export interface GetOutPatientList {
  hospital: GetOutPatientList_hospital;
}

export interface GetOutPatientListVariables {
  filterOptions?: ConsultationFilterInput | null;
  hospitalId?: string | null;
}
