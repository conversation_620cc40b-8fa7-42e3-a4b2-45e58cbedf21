/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HandoverNoteItemInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddHandoverNoteItem
// ====================================================

export interface AddHandoverNoteItem_addHandoverNoteItem_patientInformation {
  __typename: "PatientInformation";
  fullName: string;
  clinifyId: string | null;
  phone: string | null;
  email: string | null;
}

export interface AddHandoverNoteItem_addHandoverNoteItem {
  __typename: "HandoverNoteItemModel";
  id: string;
  createdDate: any;
  updatedDate: any | null;
  admissionWard: string | null;
  patientProfileId: string;
  patientInformation: AddHandoverNoteItem_addHandoverNoteItem_patientInformation;
  note: string | null;
  priority: string | null;
  handoverNoteId: string;
  creatorId: string;
}

export interface AddHandoverNoteItem {
  addHandoverNoteItem: AddHandoverNoteItem_addHandoverNoteItem;
}

export interface AddHandoverNoteItemVariables {
  id: string;
  input: HandoverNoteItemInput;
}
