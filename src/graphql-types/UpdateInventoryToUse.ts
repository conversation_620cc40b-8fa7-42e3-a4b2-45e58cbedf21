/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateInventoryToUse
// ====================================================

export interface UpdateInventoryToUse_updateInventoryToUse {
  __typename: "FacilityPreferenceModel";
  id: string;
  useHQFacilityInventory: boolean | null;
}

export interface UpdateInventoryToUse {
  updateInventoryToUse: UpdateInventoryToUse_updateInventoryToUse;
}

export interface UpdateInventoryToUseVariables {
  facilityPreferenceId: string;
  useHQFacilityInventory: boolean;
}
