/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FamilyPlanningAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetImplantsInserted
// ====================================================

export interface GetImplantsInserted_getImplantsInserted {
  __typename: "FamilyPlanningSummary";
  name: number | null;
  totalImplantsInserted: number | null;
  category: string | null;
}

export interface GetImplantsInserted {
  getImplantsInserted: GetImplantsInserted_getImplantsInserted[];
}

export interface GetImplantsInsertedVariables {
  filter?: FamilyPlanningAnalyticsFilter | null;
}
