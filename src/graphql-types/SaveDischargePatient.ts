/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DischargePatientInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: SaveDischargePatient
// ====================================================

export interface SaveDischargePatient_saveDischargePatient_dischargeDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface SaveDischargePatient_saveDischargePatient_admission {
  __typename: "AdmissionModel";
  id: string;
  duration: string | null;
}

export interface SaveDischargePatient_saveDischargePatient {
  __typename: "DischargePatientModel";
  id: string;
  dischargeDate: any | null;
  dischargedStatus: string | null;
  deathDateTime: any | null;
  deathCause: string | null;
  deathLocation: string | null;
  deathCertificateIssued: string | null;
  dischargeSummary: string | null;
  dischargedBy: string | null;
  dischargedBySignature: string | null;
  dischargedBySignatureType: string | null;
  dischargedBySignatureDateTime: any | null;
  dischargeDiagnosis: SaveDischargePatient_saveDischargePatient_dischargeDiagnosis[] | null;
  causeOfDeath: string | null;
  concealDischargeSummary: boolean | null;
  followupDate: any | null;
  dischargeLocation: string | null;
  dischargeAddress: string | null;
  admission: SaveDischargePatient_saveDischargePatient_admission | null;
}

export interface SaveDischargePatient {
  saveDischargePatient: SaveDischargePatient_saveDischargePatient;
}

export interface SaveDischargePatientVariables {
  input: DischargePatientInput;
  id: string;
  clinifyId: string;
  pin?: string | null;
}
