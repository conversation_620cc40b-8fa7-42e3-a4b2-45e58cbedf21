/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PartographUpdatedSubs
// ====================================================

export interface PartographUpdatedSubs_PartographUpdated_fhr {
  __typename: "FhrType";
  readingDate: any | null;
  value: string | null;
  time: string | null;
}

export interface PartographUpdatedSubs_PartographUpdated_amnoiticFluid {
  __typename: "AmnoiticFluidType";
  readingDate: any | null;
  value: string | null;
  moulding: string | null;
  time: string | null;
}

export interface PartographUpdatedSubs_PartographUpdated_cervixDecent {
  __typename: "CervixDecentType";
  readingDate: any | null;
  cervix: string | null;
  decent: string | null;
  period: any | null;
  time: string | null;
}

export interface PartographUpdatedSubs_PartographUpdated_contraction {
  __typename: "ContractionType";
  readingDate: any | null;
  value: string | null;
  duration: string | null;
  time: string | null;
}

export interface PartographUpdatedSubs_PartographUpdated_drugIv {
  __typename: "DrugIvType";
  readingDate: any | null;
  drugName: string[] | null;
  time: string | null;
}

export interface PartographUpdatedSubs_PartographUpdated_oxytocin {
  __typename: "OxytocinType";
  readingDate: any | null;
  value: string | null;
  drops: string | null;
  time: string | null;
}

export interface PartographUpdatedSubs_PartographUpdated_pulseBP {
  __typename: "PulseBPType";
  readingDate: any | null;
  pulse: string | null;
  bpSystolic: string | null;
  bpDiastolic: string | null;
  time: string | null;
}

export interface PartographUpdatedSubs_PartographUpdated_temperature {
  __typename: "TemperatureType";
  readingDate: any | null;
  value: string | null;
  time: string | null;
  unit: string | null;
}

export interface PartographUpdatedSubs_PartographUpdated_urine {
  __typename: "UrineType";
  readingDate: any | null;
  protein: string | null;
  acetone: string | null;
  volume: string | null;
  time: string | null;
}

export interface PartographUpdatedSubs_PartographUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface PartographUpdatedSubs_PartographUpdated {
  __typename: "PartographModel";
  id: string;
  notes: string | null;
  fhr: PartographUpdatedSubs_PartographUpdated_fhr[] | null;
  amnoiticFluid: PartographUpdatedSubs_PartographUpdated_amnoiticFluid[] | null;
  cervixDecent: PartographUpdatedSubs_PartographUpdated_cervixDecent[] | null;
  contraction: PartographUpdatedSubs_PartographUpdated_contraction[] | null;
  drugIv: PartographUpdatedSubs_PartographUpdated_drugIv[] | null;
  oxytocin: PartographUpdatedSubs_PartographUpdated_oxytocin[] | null;
  pulseBP: PartographUpdatedSubs_PartographUpdated_pulseBP[] | null;
  temperature: PartographUpdatedSubs_PartographUpdated_temperature[] | null;
  urine: PartographUpdatedSubs_PartographUpdated_urine[] | null;
  profileId: string | null;
  profile: PartographUpdatedSubs_PartographUpdated_profile | null;
  facilityName: string | null;
  facilityAddress: string | null;
}

export interface PartographUpdatedSubs {
  PartographUpdated: PartographUpdatedSubs_PartographUpdated;
}

export interface PartographUpdatedSubsVariables {
  profileId: string;
}
