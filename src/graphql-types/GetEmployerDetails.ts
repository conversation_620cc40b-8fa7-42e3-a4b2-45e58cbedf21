/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetEmployerDetails
// ====================================================

export interface GetEmployerDetails_employer_selectedMemberPlans {
  __typename: "SelectedMemberPlansFields";
  id: string;
  name: string;
  frequency: string | null;
}

export interface GetEmployerDetails_employer {
  __typename: "EmployerModel";
  id: string;
  employerName: string;
  employerAddress: string | null;
  selectedMemberPlans: GetEmployerDetails_employer_selectedMemberPlans[] | null;
}

export interface GetEmployerDetails {
  employer: GetEmployerDetails_employer | null;
}

export interface GetEmployerDetailsVariables {
  id: string;
}
