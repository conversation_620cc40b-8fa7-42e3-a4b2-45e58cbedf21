/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: ArchiveAdmission
// ====================================================

export interface ArchiveAdmission_archiveAdmissions_admissionDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface ArchiveAdmission_archiveAdmissions_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface ArchiveAdmission_archiveAdmissions_vteAndBleedingRiskAssessment_furtherAssessment {
  __typename: "Assessment";
  bleedingRiskAssessed: boolean | null;
  vteRiskAssessed: boolean | null;
  bleedingRiskAssessedLastModifiedDate: any | null;
  vteRiskAssessedLastModifiedDate: any | null;
  bleedingRiskAssessedLastModifierName: string | null;
  vteRiskAssessedLastModifierName: string | null;
  ref: string | null;
}

export interface ArchiveAdmission_archiveAdmissions_vteAndBleedingRiskAssessment_onAdmission {
  __typename: "Assessment";
  bleedingRiskAssessed: boolean | null;
  vteRiskAssessed: boolean | null;
  bleedingRiskAssessedLastModifiedDate: any | null;
  vteRiskAssessedLastModifiedDate: any | null;
  bleedingRiskAssessedLastModifierName: string | null;
  vteRiskAssessedLastModifierName: string | null;
}

export interface ArchiveAdmission_archiveAdmissions_vteAndBleedingRiskAssessment_within24Hours {
  __typename: "Assessment";
  bleedingRiskAssessed: boolean | null;
  vteRiskAssessed: boolean | null;
  bleedingRiskAssessedLastModifiedDate: any | null;
  vteRiskAssessedLastModifiedDate: any | null;
  bleedingRiskAssessedLastModifierName: string | null;
  vteRiskAssessedLastModifierName: string | null;
}

export interface ArchiveAdmission_archiveAdmissions_vteAndBleedingRiskAssessment {
  __typename: "VTEAndBleedingRisk";
  furtherAssessment: ArchiveAdmission_archiveAdmissions_vteAndBleedingRiskAssessment_furtherAssessment[] | null;
  onAdmission: ArchiveAdmission_archiveAdmissions_vteAndBleedingRiskAssessment_onAdmission | null;
  within24Hours: ArchiveAdmission_archiveAdmissions_vteAndBleedingRiskAssessment_within24Hours | null;
}

export interface ArchiveAdmission_archiveAdmissions_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface ArchiveAdmission_archiveAdmissions_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: ArchiveAdmission_archiveAdmissions_profile_coverageDetails_provider | null;
}

export interface ArchiveAdmission_archiveAdmissions_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: ArchiveAdmission_archiveAdmissions_profile_coverageDetails[] | null;
}

export interface ArchiveAdmission_archiveAdmissions_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface ArchiveAdmission_archiveAdmissions_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: ArchiveAdmission_archiveAdmissions_preauthorizationDetails_provider | null;
}

export interface ArchiveAdmission_archiveAdmissions_allergies {
  __typename: "AllergyModel";
  id: string;
}

export interface ArchiveAdmission_archiveAdmissions_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface ArchiveAdmission_archiveAdmissions_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface ArchiveAdmission_archiveAdmissions_consultations {
  __typename: "ConsultationModel";
  id: string;
}

export interface ArchiveAdmission_archiveAdmissions_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface ArchiveAdmission_archiveAdmissions_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface ArchiveAdmission_archiveAdmissions_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface ArchiveAdmission_archiveAdmissions_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface ArchiveAdmission_archiveAdmissions_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface ArchiveAdmission_archiveAdmissions_bloodTransfusions {
  __typename: "BloodTransfusionModel";
  id: string;
  transfusionDateTime: any | null;
  transfusionOrderGiven: string;
  transfusionDoctor: string | null;
  transfusionNurse: string | null;
  patientBloodGroup: string | null;
  patientGenoType: string | null;
  crossMatchingTime: string | null;
  bloodLabel: string | null;
  bloodProduct: string | null;
  expiryDate: string | null;
  donorBloodType: string | null;
  bloodPint: string | null;
  lengthOfTransfusion: string | null;
  transfusionStartDateTime: any | null;
  transfusionEndDateTime: any | null;
  adverseReaction: string | null;
  reaction: string | null;
  transfusionNote: string | null;
  patientConsent: string | null;
  consentReason: string | null;
  bloodSource: string | null;
  bloodDonorStatus: string | null;
  concealTransfusionNote: boolean | null;
  postTransfusionFBC: string | null;
  concealPostTransfusionFBC: boolean | null;
  diuretic: string | null;
  diureticType: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
}

export interface ArchiveAdmission_archiveAdmissions_transferPatients_transferHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  clinifyId: string | null;
}

export interface ArchiveAdmission_archiveAdmissions_transferPatients {
  __typename: "TransferPatientModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  roomOption: string | null;
  transferSource: string | null;
  roomInventoryId: string | null;
  transferHospital: ArchiveAdmission_archiveAdmissions_transferPatients_transferHospital | null;
}

export interface ArchiveAdmission_archiveAdmissions_dischargePatients_dischargeDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface ArchiveAdmission_archiveAdmissions_dischargePatients {
  __typename: "DischargePatientModel";
  id: string;
  dischargeDate: any | null;
  dischargedStatus: string | null;
  deathDateTime: any | null;
  deathCause: string | null;
  deathLocation: string | null;
  deathCertificateIssued: string | null;
  dischargeSummary: string | null;
  dischargedBy: string | null;
  dischargedBySignature: string | null;
  dischargedBySignatureType: string | null;
  dischargedBySignatureDateTime: any | null;
  dischargeDiagnosis: ArchiveAdmission_archiveAdmissions_dischargePatients_dischargeDiagnosis[] | null;
  causeOfDeath: string | null;
  concealDischargeSummary: boolean | null;
  followupDate: any | null;
  dischargeLocation: string | null;
  dischargeAddress: string | null;
}

export interface ArchiveAdmission_archiveAdmissions_admissionNotes {
  __typename: "AdmissionNoteModel";
  id: string;
  creatorProfileType: string;
  note: string | null;
  conceal: boolean | null;
}

export interface ArchiveAdmission_archiveAdmissions {
  __typename: "AdmissionModel";
  id: string;
  admissionDate: any | null;
  admissionDiagnosis: ArchiveAdmission_archiveAdmissions_admissionDiagnosis[] | null;
  duration: string | null;
  priority: string | null;
  category: string | null;
  severeness: string | null;
  admittedBy: string | null;
  doctorInCharge: string | null;
  ward: string | null;
  provider: string | null;
  providerServiceName: string | null;
  hospitalUnit: string | null;
  roomType: string | null;
  fileNumber: string | null;
  roomNumber: string | null;
  bedNumber: string | null;
  finding: string | null;
  rank: string | null;
  specialty: string | null;
  department: string | null;
  nurseName: string | null;
  hmoProviderId: string | null;
  serviceDetails: ArchiveAdmission_archiveAdmissions_serviceDetails[] | null;
  isPackage: boolean;
  bedAvailable: string | null;
  roomOption: string | null;
  patientConsent: string | null;
  presentMedicalHistory: string | null;
  valuablesOrBelongings: string | null;
  patientEnvironmentOrientation: string | null;
  currentMedications: string | null;
  medicinesDeposition: string | null;
  instructedToSendHome: string | null;
  nonBroughtToHospital: string | null;
  otherPlacement: string | null;
  medicationOrDrug: string | null;
  bloodTransfusion: string | null;
  food: string | null;
  latex: string | null;
  adultAge: string | null;
  adultSedativeMedication: string | null;
  adultAmbulatorySupport: string | null;
  mentalStatus: string | null;
  childAge: string | null;
  dehydration: string | null;
  dizziness: string | null;
  respirationDistress: string | null;
  childAmbulatorySupport: string | null;
  childSedativeMedication: string | null;
  painScore: string | null;
  painDescriptors: string[] | null;
  specifyPainDescriptors: string | null;
  painLocation: string[] | null;
  specifyPainLocation: string | null;
  acuity: string | null;
  modifying: string | null;
  tobaccoUse: string | null;
  tobaccoUseDuration: string | null;
  alcoholUse: string | null;
  alcoholUseDuration: string | null;
  psychologicalStatus: string | null;
  specifyPsychologicalStatus: string | null;
  sleep: string | null;
  specifySleepDifficulty: string | null;
  sleepRoutine: string | null;
  specifySleepRoutine: string | null;
  whatMakesYouSleep: string | null;
  bathing: string | null;
  dressing: string | null;
  eating: string | null;
  mobility: string | null;
  stairClimbing: string | null;
  toiletUse: string | null;
  impairedHearing: string | null;
  impairedVision: string | null;
  canPerformAdl: string | null;
  canRead: string | null;
  canWrite: string | null;
  hearingAid: string | null;
  glasses: string | null;
  contacts: string | null;
  dentures: string | null;
  partial: string | null;
  difficultyInChewing: string | null;
  difficultyInSwallowing: string | null;
  specialDiet: string | null;
  specifySpecialDiet: string | null;
  reAdmission: string | null;
  lastAdmissionDateTime: any | null;
  specialCourtesy: string | null;
  specialArrangement: string | null;
  pallorSunkenEyesDehydrationAnorexia: string | null;
  vomittingDiarrheaEdema: string | null;
  newlyDiagnosedDiabeticOrHypertensive: string | null;
  hairOrSkinChange: string | null;
  nursingNeeds: string | null;
  nursingDiagnosis: string[] | null;
  objectives: string | null;
  nursingOrders: string | null;
  evaluation: string | null;
  dischargeDate: any | null;
  transferDate: any | null;
  roomInventoryId: string | null;
  clinicName: string | null;
  clinicAddress: string | null;
  appointmentId: string | null;
  billStatus: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  hospitalId: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  profileId: string | null;
  vteAndBleedingRiskAssessment: ArchiveAdmission_archiveAdmissions_vteAndBleedingRiskAssessment | null;
  profile: ArchiveAdmission_archiveAdmissions_profile | null;
  preauthorizationDetails: ArchiveAdmission_archiveAdmissions_preauthorizationDetails | null;
  allergies: ArchiveAdmission_archiveAdmissions_allergies[];
  medications: ArchiveAdmission_archiveAdmissions_medications[];
  surgeries: ArchiveAdmission_archiveAdmissions_surgeries[];
  consultations: ArchiveAdmission_archiveAdmissions_consultations[];
  vitals: ArchiveAdmission_archiveAdmissions_vitals[];
  radiology: ArchiveAdmission_archiveAdmissions_radiology[];
  labTests: ArchiveAdmission_archiveAdmissions_labTests[];
  investigations: ArchiveAdmission_archiveAdmissions_investigations[];
  nursingServices: ArchiveAdmission_archiveAdmissions_nursingServices[];
  bloodTransfusions: ArchiveAdmission_archiveAdmissions_bloodTransfusions[] | null;
  transferPatients: ArchiveAdmission_archiveAdmissions_transferPatients[] | null;
  dischargePatients: ArchiveAdmission_archiveAdmissions_dischargePatients[] | null;
  admissionNotes: ArchiveAdmission_archiveAdmissions_admissionNotes[];
}

export interface ArchiveAdmission {
  archiveAdmissions: ArchiveAdmission_archiveAdmissions[];
}

export interface ArchiveAdmissionVariables {
  ids: string[];
  archive?: boolean | null;
  clinifyId: string;
}
