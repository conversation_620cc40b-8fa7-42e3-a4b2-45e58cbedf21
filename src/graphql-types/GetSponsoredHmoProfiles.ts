/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetSponsoredHmoProfiles
// ====================================================

export interface GetSponsoredHmoProfiles_getSponsoredHmoProfiles {
  __typename: "SponsorEnrolleeDetails";
  fullName: string | null;
  isCovered: boolean | null;
  memberNumber: string | null;
  planDueDate: any | null;
  planStartDate: any | null;
  status: string | null;
}

export interface GetSponsoredHmoProfiles {
  getSponsoredHmoProfiles: GetSponsoredHmoProfiles_getSponsoredHmoProfiles[];
}

export interface GetSponsoredHmoProfilesVariables {
  sponsorName: string;
}
