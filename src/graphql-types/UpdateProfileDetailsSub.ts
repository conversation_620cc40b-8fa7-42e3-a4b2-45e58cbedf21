/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PatientProfileType, Gender, HospitalPlan } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: UpdateProfileDetailsSub
// ====================================================

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_enrollmentTpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_enrollmentAgency {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_coverageDetails_questionnaireData {
  __typename: "QuestionnaireData";
  employmentStatus: string | null;
  gender: string | null;
  highestFormalEducationalLevel: string | null;
  maritalStatus: string | null;
  numberOfHouseholdMembers: string | null;
  numberOfMattresses: string | null;
  numberOfMobilePhones: string | null;
  numberOfRooms: string | null;
  numberOfTVs: string | null;
  numberOfVehicles: string | null;
  occupationalGroup: string | null;
  occupation: string | null;
  primaryCookingImplement: string | null;
  relationshipToHouseholdHead: string | null;
  typeOfRoof: string | null;
  typeOfToilet: string | null;
  /**
   * Score in percentage
   */
  questionnaireScore: number | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  coverageType: string | null;
  companyName: string | null;
  companyAddress: string | null;
  familyName: string | null;
  familyAddress: string | null;
  name: string | null;
  contactAddress: string | null;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  capturedDate: any | null;
  capturedBy: string | null;
  enrolledBy: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  paymentFrequency: string | null;
  memberStartDate: any | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  capitatedMember: string | null;
  capitatedAmount: string | null;
  employeeType: string | null;
  employeeDivision: string | null;
  occupation: string | null;
  retired: boolean | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  terminationDate: any | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionRate: string | null;
  commissionPayable: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
  employerCode: string | null;
  activationDatetime: any | null;
  totalPremiumAmountPaid: string | null;
  questionnaireData: UpdateProfileDetailsSub_ProfileDetailsUpdated_coverageDetails_questionnaireData | null;
  provider: UpdateProfileDetailsSub_ProfileDetailsUpdated_coverageDetails_provider | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_fileNumbers {
  __typename: "FileNumberInputType";
  coverageRef: string | null;
  fileNumber: string | null;
  existingFamily: boolean | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_personalInformation_coverageDetails_questionnaireData {
  __typename: "QuestionnaireData";
  employmentStatus: string | null;
  gender: string | null;
  highestFormalEducationalLevel: string | null;
  maritalStatus: string | null;
  numberOfHouseholdMembers: string | null;
  numberOfMattresses: string | null;
  numberOfMobilePhones: string | null;
  numberOfRooms: string | null;
  numberOfTVs: string | null;
  numberOfVehicles: string | null;
  occupationalGroup: string | null;
  occupation: string | null;
  primaryCookingImplement: string | null;
  relationshipToHouseholdHead: string | null;
  typeOfRoof: string | null;
  typeOfToilet: string | null;
  /**
   * Score in percentage
   */
  questionnaireScore: number | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_personalInformation_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_personalInformation_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  coverageType: string | null;
  companyName: string | null;
  companyAddress: string | null;
  familyName: string | null;
  familyAddress: string | null;
  name: string | null;
  contactAddress: string | null;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  capturedDate: any | null;
  capturedBy: string | null;
  enrolledBy: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  paymentFrequency: string | null;
  memberStartDate: any | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  capitatedMember: string | null;
  capitatedAmount: string | null;
  employeeType: string | null;
  employeeDivision: string | null;
  occupation: string | null;
  retired: boolean | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  terminationDate: any | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionRate: string | null;
  commissionPayable: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
  employerCode: string | null;
  activationDatetime: any | null;
  totalPremiumAmountPaid: string | null;
  questionnaireData: UpdateProfileDetailsSub_ProfileDetailsUpdated_personalInformation_coverageDetails_questionnaireData | null;
  provider: UpdateProfileDetailsSub_ProfileDetailsUpdated_personalInformation_coverageDetails_provider | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_personalInformation_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  value: string | null;
  countryCode: string | null;
  countryName: string | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  rank: string | null;
  title: string | null;
  department: string | null;
  speciality: string | null;
  displayPictureUrl: string | null;
  firstName: string | null;
  lastName: string | null;
  middleName: string | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  genoType: string | null;
  weight: string | null;
  weightUnit: string | null;
  height: string | null;
  heightUnit: string | null;
  address: string | null;
  gender: Gender | null;
  secondaryEmail: string | null;
  folioNumber: string | null;
  clinicalTrials: string | null;
  patientFileOrCardNo: string | null;
  coverageDetails: UpdateProfileDetailsSub_ProfileDetailsUpdated_personalInformation_coverageDetails[] | null;
  secondaryPhoneNumber: UpdateProfileDetailsSub_ProfileDetailsUpdated_personalInformation_secondaryPhoneNumber | null;
  nin: string | null;
  votersId: string | null;
  passportNumber: string | null;
  bvn: string | null;
  registrationNote: string | null;
  lga: string | null;
  ward: string | null;
  buildingName: string | null;
  buildingLevel: string | null;
  countryOfResidence: string | null;
  stateOfResidence: string | null;
  patientCategory: string[] | null;
  nationality: string | null;
  state: string | null;
  originLga: string | null;
  city: string | null;
  placeOfBirth: string | null;
  userRole: string | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_backgroundInformation {
  __typename: "backgroundInformation";
  id: string | null;
  maritalStatus: string | null;
  numberOfChildren: number | null;
  education: string | null;
  state: string | null;
  religion: string | null;
  nationality: string | null;
  organDonor: string | null;
  occupation: string | null;
  salaryRange: string | null;
  bloodDonor: string | null;
  preferredLanguage: string | null;
  modeOfCommunication: string | null;
  tissueDonor: string | null;
  boneMarrowDonor: string | null;
  originLga: string | null;
  ethnicity: string | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_hospital_hmoHospitals_planVisibility {
  __typename: "PlanVisibilityDto";
  id: string;
  name: string;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_hospital_hmoHospitals_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_hospital_hmoHospitals {
  __typename: "HmoHospitalModel";
  hmoProviderId: string;
  planVisibility: UpdateProfileDetailsSub_ProfileDetailsUpdated_hospital_hmoHospitals_planVisibility[] | null;
  provider: UpdateProfileDetailsSub_ProfileDetailsUpdated_hospital_hmoHospitals_provider | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated_hospital {
  __typename: "HospitalModel";
  name: string | null;
  id: string;
  address: string | null;
  clinifyId: string | null;
  plan: HospitalPlan;
  website: string | null;
  facilityLogo: string | null;
  specialties: string[] | null;
  hmoHospitals: UpdateProfileDetailsSub_ProfileDetailsUpdated_hospital_hmoHospitals[] | null;
}

export interface UpdateProfileDetailsSub_ProfileDetailsUpdated {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  active: boolean;
  isDefault: boolean;
  createdDate: any | null;
  updatedDate: any | null;
  creatorName: string | null;
  lastModifierName: string | null;
  patientProfileType: PatientProfileType | null;
  enrollmentTpaNonTpa: UpdateProfileDetailsSub_ProfileDetailsUpdated_enrollmentTpaNonTpa | null;
  enrollmentAgency: UpdateProfileDetailsSub_ProfileDetailsUpdated_enrollmentAgency | null;
  coverageDetails: UpdateProfileDetailsSub_ProfileDetailsUpdated_coverageDetails[] | null;
  fileNumbers: UpdateProfileDetailsSub_ProfileDetailsUpdated_fileNumbers[] | null;
  fullName: string;
  type: string;
  typeAlias: string | null;
  title: string | null;
  gender: Gender | null;
  patientStatus: string | null;
  deathDateTime: any | null;
  deathLocation: string | null;
  causeOfDeath: string | null;
  dataAccessType: string | null;
  serviceDetails: UpdateProfileDetailsSub_ProfileDetailsUpdated_serviceDetails[] | null;
  personalInformation: UpdateProfileDetailsSub_ProfileDetailsUpdated_personalInformation | null;
  backgroundInformation: UpdateProfileDetailsSub_ProfileDetailsUpdated_backgroundInformation | null;
  billStatus: string | null;
  createdFromHmo: boolean | null;
  shareData: boolean | null;
  hospital: UpdateProfileDetailsSub_ProfileDetailsUpdated_hospital | null;
}

export interface UpdateProfileDetailsSub {
  ProfileDetailsUpdated: UpdateProfileDetailsSub_ProfileDetailsUpdated;
}

export interface UpdateProfileDetailsSubVariables {
  profileId: string;
  hospitalId: string;
}
