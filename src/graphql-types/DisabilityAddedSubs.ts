/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: DisabilityAddedSubs
// ====================================================

export interface DisabilityAddedSubs_DisabilityAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface DisabilityAddedSubs_DisabilityAdded {
  __typename: "DisabilityModel";
  id: string;
  disability: string | null;
  type: string | null;
  additionalNote: string | null;
  profile: DisabilityAddedSubs_DisabilityAdded_profile | null;
}

export interface DisabilityAddedSubs {
  DisabilityAdded: DisabilityAddedSubs_DisabilityAdded;
}

export interface DisabilityAddedSubsVariables {
  profileId: string;
}
