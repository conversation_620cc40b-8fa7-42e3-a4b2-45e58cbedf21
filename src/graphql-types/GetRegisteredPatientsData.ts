/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VisitationAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetRegisteredPatientsData
// ====================================================

export interface GetRegisteredPatientsData_getRegisteredPatientsData_ageRanges {
  __typename: "CategoryCount";
  count: number | null;
  category: string | null;
}

export interface GetRegisteredPatientsData_getRegisteredPatientsData_categoryByGenders {
  __typename: "CategoryCount";
  count: number | null;
  category: string | null;
  group: string | null;
}

export interface GetRegisteredPatientsData_getRegisteredPatientsData {
  __typename: "VisitationSummary";
  name: number | null;
  totalFemale: number | null;
  totalMale: number | null;
  ageRanges: GetRegisteredPatientsData_getRegisteredPatientsData_ageRanges[] | null;
  categoryByGenders: GetRegisteredPatientsData_getRegisteredPatientsData_categoryByGenders[] | null;
}

export interface GetRegisteredPatientsData {
  getRegisteredPatientsData: GetRegisteredPatientsData_getRegisteredPatientsData;
}

export interface GetRegisteredPatientsDataVariables {
  filter?: VisitationAnalyticsFilter | null;
}
