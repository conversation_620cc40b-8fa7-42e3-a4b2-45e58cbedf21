/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { SurgeryInput, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateSurgeryBill
// ====================================================

export interface UpdateSurgeryBill_updateSurgeryBill_procedureType {
  __typename: "ProcedureTypeInputType";
  ref: string | null;
  type: string;
  priority: string | null;
  provider: string | null;
  itemId: string | null;
  preauthorizationDetailsId: string | null;
}

export interface UpdateSurgeryBill_updateSurgeryBill_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface UpdateSurgeryBill_updateSurgeryBill_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface UpdateSurgeryBill_updateSurgeryBill_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: UpdateSurgeryBill_updateSurgeryBill_profile_coverageDetails_provider | null;
}

export interface UpdateSurgeryBill_updateSurgeryBill_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: UpdateSurgeryBill_updateSurgeryBill_profile_coverageDetails[] | null;
}

export interface UpdateSurgeryBill_updateSurgeryBill_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UpdateSurgeryBill_updateSurgeryBill_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: UpdateSurgeryBill_updateSurgeryBill_preauthorizationDetails_provider | null;
}

export interface UpdateSurgeryBill_updateSurgeryBill_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface UpdateSurgeryBill_updateSurgeryBill_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface UpdateSurgeryBill_updateSurgeryBill_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface UpdateSurgeryBill_updateSurgeryBill_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface UpdateSurgeryBill_updateSurgeryBill_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface UpdateSurgeryBill_updateSurgeryBill_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface UpdateSurgeryBill_updateSurgeryBill_operationNotes {
  __typename: "OperationNoteModel";
  id: string;
  operationNote: string | null;
  postOperationNote: string | null;
  conceal: boolean;
}

export interface UpdateSurgeryBill_updateSurgeryBill {
  __typename: "SurgeryModel";
  id: string;
  surgeryDate: any | null;
  duration: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  procedureType: UpdateSurgeryBill_updateSurgeryBill_procedureType[];
  rank: string | null;
  reason: string | null;
  assistantSurgeon: string | null;
  requestedBy: string | null;
  specialty: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  operatedBy: string | null;
  patientConsent: string | null;
  hmoProviderId: string | null;
  serviceDetails: UpdateSurgeryBill_updateSurgeryBill_serviceDetails[] | null;
  isPackage: boolean;
  operatingRoomNurse: string | null;
  anesthetistName: string | null;
  anesthesia: string | null;
  visitingSpecialistName: string | null;
  visitingFacilityName: string | null;
  documentUrl: string[] | null;
  surgeryStartDate: any | null;
  surgeryEndDate: any | null;
  appointmentId: string | null;
  createdDate: any;
  updatedDate: any;
  department: string | null;
  billStatus: string | null;
  hospitalId: string | null;
  profileId: string | null;
  profile: UpdateSurgeryBill_updateSurgeryBill_profile | null;
  preauthorizationDetails: UpdateSurgeryBill_updateSurgeryBill_preauthorizationDetails[] | null;
  medications: UpdateSurgeryBill_updateSurgeryBill_medications[];
  vitals: UpdateSurgeryBill_updateSurgeryBill_vitals[];
  investigations: UpdateSurgeryBill_updateSurgeryBill_investigations[];
  labTests: UpdateSurgeryBill_updateSurgeryBill_labTests[];
  radiology: UpdateSurgeryBill_updateSurgeryBill_radiology[];
  nursingServices: UpdateSurgeryBill_updateSurgeryBill_nursingServices[];
  operationNotes: UpdateSurgeryBill_updateSurgeryBill_operationNotes[] | null;
}

export interface UpdateSurgeryBill {
  updateSurgeryBill: UpdateSurgeryBill_updateSurgeryBill;
}

export interface UpdateSurgeryBillVariables {
  input: SurgeryInput;
  id: string;
}
