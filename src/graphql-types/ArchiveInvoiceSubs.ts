/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InvoiceStatus, PercentOrAmount, Currency, VirtualAccountProvider } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: ArchiveInvoiceSubs
// ====================================================

export interface ArchiveInvoiceSubs_InvoiceArchived_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface ArchiveInvoiceSubs_InvoiceArchived_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface ArchiveInvoiceSubs_InvoiceArchived_recipient {
  __typename: "InvoiceRecipient";
  address: string | null;
  email: string | null;
  name: string;
  phone: string | null;
  clinifyId: string | null;
}

export interface ArchiveInvoiceSubs_InvoiceArchived_senderHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
  facilityLogo: string | null;
  website: string | null;
}

export interface ArchiveInvoiceSubs_InvoiceArchived_virtualAccount {
  __typename: "VirtualBankAccountModel";
  id: string;
  currency: Currency;
  updatedDate: any;
  createdDate: any;
  accountName: string;
  accountNumber: string;
  bank: VirtualAccountProvider;
  expiryDate: any | null;
}

export interface ArchiveInvoiceSubs_InvoiceArchived_employeesDetails_dependents {
  __typename: "DependentInfo";
  fullName: string;
  enrolleeId: string | null;
  relationship: string | null;
}

export interface ArchiveInvoiceSubs_InvoiceArchived_employeesDetails {
  __typename: "EmployeesInfo";
  enrolleeId: string | null;
  fullName: string;
  planCategory: string | null;
  planType: string | null;
  planAmount: number | null;
  paymentFrequency: string | null;
  planStartDate: any | null;
  planDueDate: any | null;
  planStatus: string | null;
  isCovered: boolean | null;
  dependents: ArchiveInvoiceSubs_InvoiceArchived_employeesDetails_dependents[] | null;
}

export interface ArchiveInvoiceSubs_InvoiceArchived_sponsorDetails {
  __typename: "SponsorEnrolleeDetails";
  status: string | null;
  fullName: string | null;
  isCovered: boolean | null;
  memberNumber: string | null;
  planDueDate: any | null;
  planStartDate: any | null;
}

export interface ArchiveInvoiceSubs_InvoiceArchived_invoiceItems_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
}

export interface ArchiveInvoiceSubs_InvoiceArchived_invoiceItems_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
}

export interface ArchiveInvoiceSubs_InvoiceArchived_invoiceItems {
  __typename: "InvoiceItemModel";
  id: string;
  description: string;
  updatedDate: any;
  createdDate: any;
  invoiceType: string | null;
  discountAmount: number | null;
  discountPercentage: number | null;
  invoiceId: string;
  quantity: number;
  unitPrice: number | null;
  createdBy: ArchiveInvoiceSubs_InvoiceArchived_invoiceItems_createdBy;
  updatedBy: ArchiveInvoiceSubs_InvoiceArchived_invoiceItems_updatedBy | null;
  creatorName: string;
  lastModifierName: string | null;
}

export interface ArchiveInvoiceSubs_InvoiceArchived {
  __typename: "InvoiceModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  discountPercentage: number | null;
  discountAmount: number | null;
  profileId: string | null;
  employerId: string | null;
  creatorId: string;
  description: string | null;
  subTotal: number;
  /**
   * Returns total amount in lowest denomination
   */
  totalAmount: number;
  createdBy: ArchiveInvoiceSubs_InvoiceArchived_createdBy;
  lastModifierId: string | null;
  updatedBy: ArchiveInvoiceSubs_InvoiceArchived_updatedBy | null;
  additionalNote: string | null;
  amountPaid: number;
  bankTransactionIds: string[] | null;
  dueDate: any;
  invoiceReference: string;
  invoiceStatus: InvoiceStatus;
  issueDate: any;
  paymentDate: any | null;
  sponsorName: string | null;
  sponsorRef: string | null;
  nextYearlyPremium: number | null;
  sponsorLivesCovered: number | null;
  agencyLivesCovered: number | null;
  sponsorPremiumPerLives: number | null;
  recipient: ArchiveInvoiceSubs_InvoiceArchived_recipient;
  senderHospitalId: string | null;
  senderHospital: ArchiveInvoiceSubs_InvoiceArchived_senderHospital;
  professionalFeeAmount: number | null;
  professionalFeePercentage: number | null;
  controlledDiscount: PercentOrAmount | null;
  controlledProfessionalFee: PercentOrAmount | null;
  vatPercentage: number | null;
  vatAmount: number | null;
  controlledVat: PercentOrAmount | null;
  virtualAccount: ArchiveInvoiceSubs_InvoiceArchived_virtualAccount | null;
  employeesDetails: ArchiveInvoiceSubs_InvoiceArchived_employeesDetails[] | null;
  sponsorDetails: ArchiveInvoiceSubs_InvoiceArchived_sponsorDetails[] | null;
  paymentFrequency: string | null;
  plasticIdCardCount: number | null;
  plasticIdCardAmount: number | null;
  laminatedIdCardCount: number | null;
  laminatedIdCardAmount: number | null;
  creatorName: string;
  lastModifierName: string | null;
  periodStartDate: any | null;
  periodEndDate: any | null;
  invoiceItems: ArchiveInvoiceSubs_InvoiceArchived_invoiceItems[];
}

export interface ArchiveInvoiceSubs {
  InvoiceArchived: ArchiveInvoiceSubs_InvoiceArchived[];
}

export interface ArchiveInvoiceSubsVariables {
  hospitalId: string;
  profileId: string;
}
