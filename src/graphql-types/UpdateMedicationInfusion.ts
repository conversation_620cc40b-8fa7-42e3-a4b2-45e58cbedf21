/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicationInfusionInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateMedicationInfusion
// ====================================================

export interface UpdateMedicationInfusion_updateMedicationInfusion_details {
  __typename: "MedicationInfusionDetails";
  typeOrStrength: string | null;
  volume: string | null;
  drugName: string | null;
  dosage: string | null;
  route: string | null;
  dripRate: string | null;
  batchNumber: string | null;
  prescribedBy: string | null;
  administrationDateTime: any | null;
  administeredBy: string | null;
  checkedBy: string | null;
}

export interface UpdateMedicationInfusion_updateMedicationInfusion {
  __typename: "MedicationInfusionModel";
  id: string;
  creator: string | null;
  updater: string | null;
  createdDate: any;
  updatedDate: any;
  medicationDetailId: string | null;
  details: UpdateMedicationInfusion_updateMedicationInfusion_details[] | null;
}

export interface UpdateMedicationInfusion {
  updateMedicationInfusion: UpdateMedicationInfusion_updateMedicationInfusion;
}

export interface UpdateMedicationInfusionVariables {
  id: string;
  input: MedicationInfusionInput;
  pin?: string | null;
}
