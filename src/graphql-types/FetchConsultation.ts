/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL query operation: FetchConsultation
// ====================================================

export interface FetchConsultation_consultation_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface FetchConsultation_consultation_complaintSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface FetchConsultation_consultation_systemReviewSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface FetchConsultation_consultation_physicalExamSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface FetchConsultation_consultation_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface FetchConsultation_consultation_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: FetchConsultation_consultation_profile_coverageDetails_provider | null;
}

export interface FetchConsultation_consultation_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: FetchConsultation_consultation_profile_coverageDetails[] | null;
}

export interface FetchConsultation_consultation_provisionalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface FetchConsultation_consultation_finalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface FetchConsultation_consultation_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface FetchConsultation_consultation_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: FetchConsultation_consultation_preauthorizationDetails_provider | null;
}

export interface FetchConsultation_consultation_referredTo_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface FetchConsultation_consultation_referredTo {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  hospital: FetchConsultation_consultation_referredTo_hospital | null;
}

export interface FetchConsultation_consultation_treatmentPlans {
  __typename: "TreatmentPlanModel";
  id: string;
  treatmentPlan: string | null;
  conceal: boolean | null;
  patientAdmitted: string | null;
  concealObservationNote: boolean | null;
  observationNote: string | null;
  admissionConsent: string | null;
  adverseEffectsFollowingTreatment: string | null;
  stateEffects: string | null;
  adverseEffectsInvestigated: string | null;
  outcomeOfInvestigation: string | null;
  treatmentGiven: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  treatmentStatus: string | null;
}

export interface FetchConsultation_consultation_allergies {
  __typename: "AllergyModel";
  id: string;
}

export interface FetchConsultation_consultation_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface FetchConsultation_consultation_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface FetchConsultation_consultation_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface FetchConsultation_consultation_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface FetchConsultation_consultation_labTests {
  __typename: "LabResultModel";
  id: string;
}

export interface FetchConsultation_consultation_radiology {
  __typename: "RadiologyResultModel";
  id: string;
}

export interface FetchConsultation_consultation_investigations {
  __typename: "LabResultModel";
  id: string;
}

export interface FetchConsultation_consultation_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface FetchConsultation_consultation {
  __typename: "ConsultationModel";
  id: string;
  consultationDateTime: any | null;
  duration: string | null;
  doctorName: string;
  priority: string | null;
  specialty: string | null;
  class: string | null;
  hmoProviderId: string | null;
  serviceDetails: FetchConsultation_consultation_serviceDetails[] | null;
  isPackage: boolean;
  clinicName: string | null;
  clinicAddress: string | null;
  complaint: string | null;
  provider: string | null;
  providerServiceName: string | null;
  complaintHistory: string | null;
  healthEducation: string | null;
  systemReview: string | null;
  systemReviewSmartText: string | null;
  complaintGender: string | null;
  complaintSmartSelection: FetchConsultation_consultation_complaintSmartSelection | null;
  systemReviewSmartSelection: FetchConsultation_consultation_systemReviewSmartSelection | null;
  category: string | null;
  department: string | null;
  physicalExam: string | null;
  physicalExamSmartText: string | null;
  physicalExamSmartSelection: FetchConsultation_consultation_physicalExamSmartSelection | null;
  referral: boolean | null;
  referralDate: any | null;
  referralReason: string | null;
  concealReferralReason: boolean | null;
  specialtyReferredTo: string | null;
  profileId: string | null;
  profile: FetchConsultation_consultation_profile | null;
  externalReferral: boolean | null;
  provisionalDiagnosis: FetchConsultation_consultation_provisionalDiagnosis[] | null;
  finalDiagnosis: FetchConsultation_consultation_finalDiagnosis[] | null;
  consultationStartDate: any | null;
  consultationEndDate: any | null;
  appointmentId: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  concealComplaint: boolean | null;
  concealComplaintHistory: boolean | null;
  concealPhysicalExam: boolean | null;
  concealSystemReview: boolean | null;
  concealHealthEducation: boolean | null;
  billStatus: string | null;
  audiometry: string | null;
  concealAudiometry: boolean | null;
  hospitalId: string | null;
  preauthorizationDetails: FetchConsultation_consultation_preauthorizationDetails | null;
  referredTo: FetchConsultation_consultation_referredTo | null;
  treatmentPlans: FetchConsultation_consultation_treatmentPlans[] | null;
  allergies: FetchConsultation_consultation_allergies[];
  medications: FetchConsultation_consultation_medications[];
  surgeries: FetchConsultation_consultation_surgeries[];
  admissions: FetchConsultation_consultation_admissions[];
  vitals: FetchConsultation_consultation_vitals[];
  labTests: FetchConsultation_consultation_labTests[];
  radiology: FetchConsultation_consultation_radiology[];
  investigations: FetchConsultation_consultation_investigations[];
  nursingServices: FetchConsultation_consultation_nursingServices[];
}

export interface FetchConsultation {
  consultation: FetchConsultation_consultation;
}

export interface FetchConsultationVariables {
  id: string;
  clinifyId: string;
}
