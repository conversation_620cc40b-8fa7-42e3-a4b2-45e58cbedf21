/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EnrollmentAgencyInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddEnrollmentAgency
// ====================================================

export interface AddEnrollmentAgency_addEnrollmentAgency_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface AddEnrollmentAgency_addEnrollmentAgency_tpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
}

export interface AddEnrollmentAgency_addEnrollmentAgency_primaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface AddEnrollmentAgency_addEnrollmentAgency_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface AddEnrollmentAgency_addEnrollmentAgency_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface AddEnrollmentAgency_addEnrollmentAgency_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface AddEnrollmentAgency_addEnrollmentAgency {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
  agencyCode: string | null;
  address: string | null;
  country: string | null;
  state: string | null;
  agencyType: string | null;
  profile: AddEnrollmentAgency_addEnrollmentAgency_profile | null;
  tpaNonTpa: AddEnrollmentAgency_addEnrollmentAgency_tpaNonTpa | null;
  localGovernmentArea: string | null;
  primaryPhoneNumber: AddEnrollmentAgency_addEnrollmentAgency_primaryPhoneNumber | null;
  primaryEmailAddress: string | null;
  secondaryPhoneNumber: AddEnrollmentAgency_addEnrollmentAgency_secondaryPhoneNumber | null;
  secondaryEmailAddress: string | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: AddEnrollmentAgency_addEnrollmentAgency_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: AddEnrollmentAgency_addEnrollmentAgency_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  accountName: string | null;
  accountNumber: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface AddEnrollmentAgency {
  addEnrollmentAgency: AddEnrollmentAgency_addEnrollmentAgency;
}

export interface AddEnrollmentAgencyVariables {
  input: EnrollmentAgencyInput;
}
