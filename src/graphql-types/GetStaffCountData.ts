/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FacilityStaffsAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetStaffCountData
// ====================================================

export interface GetStaffCountData_getStaffsByDepartment {
  __typename: "DepartmentsData";
  name: number | null;
  category: string | null;
  count: number | null;
}

export interface GetStaffCountData {
  getStaffsByDepartment: GetStaffCountData_getStaffsByDepartment[];
}

export interface GetStaffCountDataVariables {
  filter?: FacilityStaffsAnalyticsFilter | null;
}
