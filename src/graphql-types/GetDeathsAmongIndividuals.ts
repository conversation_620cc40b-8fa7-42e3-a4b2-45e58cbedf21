/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DeathsAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetDeathsAmongIndividuals
// ====================================================

export interface GetDeathsAmongIndividuals_getDeathsAmongIndividuals_ageRanges {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetDeathsAmongIndividuals_getDeathsAmongIndividuals_byCauseOfDeath {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetDeathsAmongIndividuals_getDeathsAmongIndividuals {
  __typename: "DeathsSummary";
  name: number | null;
  totalFemale: number | null;
  totalMale: number | null;
  ageRanges: GetDeathsAmongIndividuals_getDeathsAmongIndividuals_ageRanges[] | null;
  byCauseOfDeath: GetDeathsAmongIndividuals_getDeathsAmongIndividuals_byCauseOfDeath[] | null;
}

export interface GetDeathsAmongIndividuals {
  getDeathsAmongIndividuals: GetDeathsAmongIndividuals_getDeathsAmongIndividuals;
}

export interface GetDeathsAmongIndividualsVariables {
  filter?: DeathsAnalyticsFilter | null;
}
