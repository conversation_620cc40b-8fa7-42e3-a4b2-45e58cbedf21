/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetPatientMedicalSummary
// ====================================================

export interface GetPatientMedicalSummary_getPatientMedicalSummary_anthropometry {
  __typename: "AnthropometryModel";
  height: string | null;
  heightUnit: string | null;
  weight: string | null;
  weightUnit: string | null;
  bmi: string | null;
  bsa: string | null;
}

export interface GetPatientMedicalSummary_getPatientMedicalSummary_bloodPressure {
  __typename: "BloodPressureModel";
  id: string;
  systolic: string | null;
  diastolic: string | null;
}

export interface GetPatientMedicalSummary_getPatientMedicalSummary_temperature {
  __typename: "TemperatureModel";
  id: string;
  reading: string | null;
  readingUnit: string | null;
}

export interface GetPatientMedicalSummary_getPatientMedicalSummary_pulseRate {
  __typename: "PulseRateModel";
  id: string;
  reading: string | null;
}

export interface GetPatientMedicalSummary_getPatientMedicalSummary_respiratoryRate {
  __typename: "RespiratoryRateModel";
  id: string;
  oxygenSaturation: string | null;
  reading: string | null;
}

export interface GetPatientMedicalSummary_getPatientMedicalSummary_bloodGlucose {
  __typename: "BloodGlucoseModel";
  id: string;
  reading: string | null;
  readingUnit: string | null;
}

export interface GetPatientMedicalSummary_getPatientMedicalSummary {
  __typename: "PatientMedicalSummaryResponse";
  id: string;
  diagnosis: string[] | null;
  anthropometry: GetPatientMedicalSummary_getPatientMedicalSummary_anthropometry | null;
  bloodPressure: GetPatientMedicalSummary_getPatientMedicalSummary_bloodPressure | null;
  temperature: GetPatientMedicalSummary_getPatientMedicalSummary_temperature | null;
  pulseRate: GetPatientMedicalSummary_getPatientMedicalSummary_pulseRate | null;
  respiratoryRate: GetPatientMedicalSummary_getPatientMedicalSummary_respiratoryRate | null;
  bloodGlucose: GetPatientMedicalSummary_getPatientMedicalSummary_bloodGlucose | null;
}

export interface GetPatientMedicalSummary {
  getPatientMedicalSummary: GetPatientMedicalSummary_getPatientMedicalSummary;
}

export interface GetPatientMedicalSummaryVariables {
  profileId: string;
  date?: string | null;
}
