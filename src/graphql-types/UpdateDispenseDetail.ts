/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DispenseDetailsInput, MedicationOptionType } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateDispenseDetail
// ====================================================

export interface UpdateDispenseDetail_updateDispenseDetails_dispenseServiceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
}

export interface UpdateDispenseDetail_updateDispenseDetails_dispenseConsumables {
  __typename: "DispenseConsumables";
  name: string | null;
  drugInventoryId: string | null;
  quantityConsumed: string | null;
  quantityRemaining: string | null;
  inventoryClass: string | null;
}

export interface UpdateDispenseDetail_updateDispenseDetails_hmoClaim_utilizations {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  type: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
}

export interface UpdateDispenseDetail_updateDispenseDetails_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
  claimDate: any | null;
  enrolleeNumber: string | null;
  utilizations: UpdateDispenseDetail_updateDispenseDetails_hmoClaim_utilizations[] | null;
}

export interface UpdateDispenseDetail_updateDispenseDetails {
  __typename: "DispenseDetailsModel";
  id: string;
  dispenseDate: any | null;
  dispensedBy: string | null;
  medicationName: string[] | null;
  medicationDetailId: string | null;
  dispenseNote: string | null;
  concealDispenseNote: boolean;
  hmoProviderId: string | null;
  hospitalId: string;
  dispenseServiceDetails: UpdateDispenseDetail_updateDispenseDetails_dispenseServiceDetails[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
  medicationId: string | null;
  billingId: string | null;
  quantityRemaining: string | null;
  quantityDispensed: string | null;
  dispenseConsumables: UpdateDispenseDetail_updateDispenseDetails_dispenseConsumables[] | null;
  option: MedicationOptionType | null;
  hmoClaimId: string | null;
  inventoryClass: string | null;
  hmoClaim: UpdateDispenseDetail_updateDispenseDetails_hmoClaim | null;
}

export interface UpdateDispenseDetail {
  updateDispenseDetails: UpdateDispenseDetail_updateDispenseDetails;
}

export interface UpdateDispenseDetailVariables {
  id: string;
  input: DispenseDetailsInput;
  pin?: string | null;
}
