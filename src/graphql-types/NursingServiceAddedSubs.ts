/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: NursingServiceAddedSubs
// ====================================================

export interface NursingServiceAddedSubs_NursingServiceAdded_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: NursingServiceAddedSubs_NursingServiceAdded_profile_coverageDetails_provider | null;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: NursingServiceAddedSubs_NursingServiceAdded_profile_coverageDetails[] | null;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: NursingServiceAddedSubs_NursingServiceAdded_preauthorizationDetails_provider | null;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_details_medications {
  __typename: "MedicationDetailType";
  medicationName: string | null;
  prescriptionTime: any | null;
  dosage: string | null;
  dosageUnit: string | null;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_details {
  __typename: "NursingServiceDetailModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  lastModifierId: string | null;
  lastModifierName: string | null;
  creatorId: string | null;
  creatorName: string | null;
  procedureDateTime: any | null;
  procedureType: string | null;
  provider: string | null;
  providerServiceName: string | null;
  priority: string | null;
  category: string | null;
  dateOfBirth: any | null;
  consentGiven: string | null;
  parentGuardianPresent: string | null;
  parentGuardianName: string | null;
  anaesthesiaGiven: string | null;
  anaesthesiaType: string | null;
  vitaminKGiven: string | null;
  castLocation: string | null;
  reasonForCasting: string | null;
  isItARepeatedCasting: string | null;
  hasRadiologicalInvestigationBeenDone: string | null;
  bathing: string | null;
  dressing: string | null;
  eating: string | null;
  mobility: string | null;
  stairClimbing: string | null;
  toiletUse: string | null;
  woundLocation: string | null;
  dressingType: string | null;
  dressingAppearance: string | null;
  dressingIntervention: string | null;
  lastDressingChange: any | null;
  dressingChangeDue: any | null;
  painScore: string | null;
  painDescriptors: string[] | null;
  otherPainDescriptors: string | null;
  signOfInfection: string | null;
  whichEar: string | null;
  observation: string | null;
  informedConsent: string | null;
  method: string | null;
  otherMethods: string | null;
  councelled: string | null;
  typeOfInjectable: string | null;
  typeOfImplant: string | null;
  duration: string | null;
  procedureStartDateTime: any | null;
  procedureEndDateTime: any | null;
  heartRateBefore: string | null;
  heartRateAfter: string | null;
  respiratoryRateBefore: string | null;
  respiratoryRateAfter: string | null;
  oxygenSaturationBefore: string | null;
  oxygenSaturationAfter: string | null;
  medications: NursingServiceAddedSubs_NursingServiceAdded_details_medications[] | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  nursingServiceId: string | null;
  procedureName: string | null;
  location: string | null;
  procedureNote: string | null;
  injectionName: string | null;
  InjectionLocation: string | null;
  injectionPrepared: string | null;
  equipmentsSterilized: string | null;
  medicalHistoryChecked: string | null;
  repeatInjection: string | null;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_bill {
  __typename: "BillModel";
  id: string;
  createdDate: any;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_progressNotes {
  __typename: "NursingServiceProgressNoteModel";
  id: string;
  note: string;
  conceal: boolean | null;
  nursingServiceId: string | null;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_consultations {
  __typename: "ConsultationModel";
  id: string;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface NursingServiceAddedSubs_NursingServiceAdded_immunizations {
  __typename: "AdmissionModel";
  id: string;
}

export interface NursingServiceAddedSubs_NursingServiceAdded {
  __typename: "NursingServiceModel";
  id: string;
  hmoProviderId: string | null;
  serviceDetails: NursingServiceAddedSubs_NursingServiceAdded_serviceDetails[] | null;
  isPackage: boolean;
  specialty: string | null;
  department: string | null;
  nurseName: string | null;
  assistantNurseName: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  billStatus: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  hospitalId: string | null;
  profileId: string | null;
  profile: NursingServiceAddedSubs_NursingServiceAdded_profile | null;
  preauthorizationDetails: NursingServiceAddedSubs_NursingServiceAdded_preauthorizationDetails | null;
  details: NursingServiceAddedSubs_NursingServiceAdded_details[] | null;
  bill: NursingServiceAddedSubs_NursingServiceAdded_bill[] | null;
  progressNotes: NursingServiceAddedSubs_NursingServiceAdded_progressNotes[] | null;
  vitals: NursingServiceAddedSubs_NursingServiceAdded_vitals[];
  admissions: NursingServiceAddedSubs_NursingServiceAdded_admissions[];
  consultations: NursingServiceAddedSubs_NursingServiceAdded_consultations[];
  medications: NursingServiceAddedSubs_NursingServiceAdded_medications[];
  surgeries: NursingServiceAddedSubs_NursingServiceAdded_surgeries[];
  investigations: NursingServiceAddedSubs_NursingServiceAdded_investigations[];
  labTests: NursingServiceAddedSubs_NursingServiceAdded_labTests[];
  radiology: NursingServiceAddedSubs_NursingServiceAdded_radiology[];
  immunizations: NursingServiceAddedSubs_NursingServiceAdded_immunizations[];
}

export interface NursingServiceAddedSubs {
  NursingServiceAdded: NursingServiceAddedSubs_NursingServiceAdded;
}

export interface NursingServiceAddedSubsVariables {
  profileId: string;
}
