/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { TransferFundFilterInput, FundTransactionStatus } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetTransferFunds
// ====================================================

export interface GetTransferFunds_transferFunds_list_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  clinifyId: string;
  title: string | null;
}

export interface GetTransferFunds_transferFunds_list_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface GetTransferFunds_transferFunds_list {
  __typename: "TransferFundModel";
  id: string;
  createdBy: GetTransferFunds_transferFunds_list_createdBy | null;
  amount: number;
  createdDate: any;
  updatedDate: any;
  destinationAccountName: string | null;
  destinationAccountNumber: string | null;
  destinationBankCode: string | null;
  destinationBankName: string | null;
  narration: string | null;
  originatorName: string | null;
  sourceAccountNumber: string | null;
  transferReference: string | null;
  transferStatus: FundTransactionStatus;
  additionalNote: string | null;
  serviceChargeAmount: number | null;
  hospital: GetTransferFunds_transferFunds_list_hospital | null;
}

export interface GetTransferFunds_transferFunds {
  __typename: "TransferFundListResponse";
  totalCount: number;
  list: GetTransferFunds_transferFunds_list[];
}

export interface GetTransferFunds {
  transferFunds: GetTransferFunds_transferFunds;
}

export interface GetTransferFundsVariables {
  filterOptions: TransferFundFilterInput;
  hmoProviderId: string;
}
