/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PricesServiceNameFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: FindPriceServiceName
// ====================================================

export interface FindPriceServiceName_findServiceName_list {
  __typename: "PricesModel";
  id: string;
  name: string | null;
  price: string | null;
  serviceType: string | null;
}

export interface FindPriceServiceName_findServiceName {
  __typename: "PriceResponse";
  list: FindPriceServiceName_findServiceName_list[];
  totalCount: number;
}

export interface FindPriceServiceName {
  findServiceName: FindPriceServiceName_findServiceName;
}

export interface FindPriceServiceNameVariables {
  filterOptions?: PricesServiceNameFilterInput | null;
}
