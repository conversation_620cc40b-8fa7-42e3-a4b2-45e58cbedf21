/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { StocksAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetDrugStatusData
// ====================================================

export interface GetDrugStatusData_getDrugStatusData_summary {
  __typename: "StockStatusData";
  name: number | null;
  totalDrugRemaining: number | null;
  totalDrugDispensed: number | null;
  totalDrugPurchased: number | null;
  totalDrugDamaged: number | null;
  totalDrugExpired: number | null;
  totalRemainingCostAmount: number | null;
  totalRemainingSalesAmount: number | null;
  totalPurchasedCostAmount: number | null;
  totalPurchasedSalesAmount: number | null;
  totalDispensedCostAmount: number | null;
  totalDispensedSalesAmount: number | null;
  totalDamagedCostAmount: number | null;
  totalDamagedSalesAmount: number | null;
  totalExpiredSalesAmount: number | null;
  totalExpiredCostAmount: number | null;
}

export interface GetDrugStatusData_getDrugStatusData_details {
  __typename: "StockStatusDetails";
  dateAdded: string | null;
  itemName: string | null;
  addedBy: string | null;
  totalQuantityPurchased: number | null;
  totalQuantityDispensed: number | null;
  totalQuantityRemaining: number | null;
  totalQuantityExpired: number | null;
  totalQuantityDamaged: number | null;
  supplier: string | null;
  totalCosts: number | null;
  totalSales: number | null;
  totalAverageCost: number | null;
  totalExpiredAmount: number | null;
  totalDamagedAmount: number | null;
  restockLevel: number | null;
}

export interface GetDrugStatusData_getDrugStatusData {
  __typename: "StockStatusResponse";
  summary: GetDrugStatusData_getDrugStatusData_summary | null;
  details: GetDrugStatusData_getDrugStatusData_details[] | null;
}

export interface GetDrugStatusData {
  getDrugStatusData: GetDrugStatusData_getDrugStatusData;
}

export interface GetDrugStatusDataVariables {
  filter?: StocksAnalyticsFilter | null;
}
