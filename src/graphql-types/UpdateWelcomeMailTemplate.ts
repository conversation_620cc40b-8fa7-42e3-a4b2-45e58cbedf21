/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MailTemplateInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateWelcomeMailTemplate
// ====================================================

export interface UpdateWelcomeMailTemplate_updateWelcomeMailTemplate_welcomeMailTemplate {
  __typename: "MailTemplate";
  subject: string | null;
  body: string | null;
}

export interface UpdateWelcomeMailTemplate_updateWelcomeMailTemplate {
  __typename: "FacilityPreferenceModel";
  id: string;
  welcomeMailTemplate: UpdateWelcomeMailTemplate_updateWelcomeMailTemplate_welcomeMailTemplate | null;
  updatedDate: any;
  lastModifierName: string | null;
}

export interface UpdateWelcomeMailTemplate {
  updateWelcomeMailTemplate: UpdateWelcomeMailTemplate_updateWelcomeMailTemplate;
}

export interface UpdateWelcomeMailTemplateVariables {
  hospitalId: string;
  input?: MailTemplateInput | null;
}
