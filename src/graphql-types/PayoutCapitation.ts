/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CapitationPayoutInput, FundTransactionStatus, PayoutCommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: PayoutCapitation
// ====================================================

export interface PayoutCapitation_payoutCapitation_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  clinifyId: string;
  title: string | null;
}

export interface PayoutCapitation_payoutCapitation_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface PayoutCapitation_payoutCapitation_auditApproval {
  __typename: "ClaimsAccountApprovalType";
  createdDate: any;
  creatorName: string;
  approvalGroup: string;
  creatorId: string;
}

export interface PayoutCapitation_payoutCapitation_hmoPlanType {
  __typename: "HmoPlanTypeModel";
  id: string;
  name: string | null;
}

export interface PayoutCapitation_payoutCapitation_detailsByPlanType {
  __typename: "CapitationDetailByPlanType";
  enrolleeCount: number;
  planType: string;
  planTypeName: string;
  totalCapitationAmount: number;
  payoutDecreasePercentage: number | null;
}

export interface PayoutCapitation_payoutCapitation {
  __typename: "TransferFundModel";
  id: string;
  createdBy: PayoutCapitation_payoutCapitation_createdBy | null;
  amount: number;
  createdDate: any;
  updatedDate: any;
  destinationAccountName: string | null;
  destinationAccountNumber: string | null;
  destinationBankCode: string | null;
  destinationBankName: string | null;
  narration: string | null;
  originatorName: string | null;
  sourceAccountNumber: string | null;
  transferReference: string | null;
  transferStatus: FundTransactionStatus;
  additionalNote: string | null;
  serviceChargeAmount: number | null;
  hospitalId: string | null;
  hmoPlanTypeId: string | null;
  hospital: PayoutCapitation_payoutCapitation_hospital | null;
  enrolleeCount: number | null;
  isEnrolleePayout: boolean | null;
  totalCapitationAmount: number | null;
  auditApproval: PayoutCapitation_payoutCapitation_auditApproval[] | null;
  hmoPlanType: PayoutCapitation_payoutCapitation_hmoPlanType | null;
  detailsByPlanType: PayoutCapitation_payoutCapitation_detailsByPlanType[] | null;
  payoutCommissionPayer: PayoutCommissionPayer | null;
  payoutDecreasePercentage: number | null;
  enrolleeCommissionAmount: number | null;
}

export interface PayoutCapitation {
  payoutCapitation: PayoutCapitation_payoutCapitation[];
}

export interface PayoutCapitationVariables {
  inputs: CapitationPayoutInput[];
  origin: string;
}
