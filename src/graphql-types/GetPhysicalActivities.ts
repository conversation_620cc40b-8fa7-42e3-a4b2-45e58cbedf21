/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ProfileInfosFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPhysicalActivities
// ====================================================

export interface GetPhysicalActivities_profile_physicalActivities_list_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface GetPhysicalActivities_profile_physicalActivities_list {
  __typename: "PhysicalActivityModel";
  id: string;
  type: string | null;
  name: string | null;
  additionalNote: string | null;
  profile: GetPhysicalActivities_profile_physicalActivities_list_profile | null;
}

export interface GetPhysicalActivities_profile_physicalActivities {
  __typename: "PhysicalActivitiesResponse";
  totalCount: number;
  list: GetPhysicalActivities_profile_physicalActivities_list[];
}

export interface GetPhysicalActivities_profile {
  __typename: "ProfileModel";
  id: string;
  physicalActivities: GetPhysicalActivities_profile_physicalActivities;
}

export interface GetPhysicalActivities {
  profile: GetPhysicalActivities_profile;
}

export interface GetPhysicalActivitiesVariables {
  filterInput: ProfileInfosFilterInput;
  id: string;
}
