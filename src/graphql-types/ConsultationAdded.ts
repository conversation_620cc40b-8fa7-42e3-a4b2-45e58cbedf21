/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillStatus } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: ConsultationAdded
// ====================================================

export interface ConsultationAdded_ConsultationAdded_bill_details_createdBy_hospital {
  __typename: "HospitalModel";
  id: string;
}

export interface ConsultationAdded_ConsultationAdded_bill_details_createdBy {
  __typename: "ProfileModel";
  clinifyId: string;
  type: string;
  hospital: ConsultationAdded_ConsultationAdded_bill_details_createdBy_hospital | null;
}

export interface ConsultationAdded_ConsultationAdded_bill_details {
  __typename: "BillDetailsModel";
  paymentType: string | null;
  patientType: string | null;
  unitPrice: number | null;
  createdBy: ConsultationAdded_ConsultationAdded_bill_details_createdBy | null;
}

export interface ConsultationAdded_ConsultationAdded_bill {
  __typename: "BillModel";
  id: string;
  amountPaid: number | null;
  totalAmount: number;
  billStatus: BillStatus;
  details: ConsultationAdded_ConsultationAdded_bill_details[] | null;
}

export interface ConsultationAdded_ConsultationAdded {
  __typename: "ConsultationModel";
  id: string;
  bill: ConsultationAdded_ConsultationAdded_bill | null;
}

export interface ConsultationAdded {
  ConsultationAdded: ConsultationAdded_ConsultationAdded;
}

export interface ConsultationAddedVariables {
  profileId: string;
  hospitalId: string;
}
