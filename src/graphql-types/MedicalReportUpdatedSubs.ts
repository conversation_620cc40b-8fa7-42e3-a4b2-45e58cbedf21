/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicalReportStatus } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: MedicalReportUpdatedSubs
// ====================================================

export interface MedicalReportUpdatedSubs_MedicalReportUpdated_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface MedicalReportUpdatedSubs_MedicalReportUpdated_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface MedicalReportUpdatedSubs_MedicalReportUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface MedicalReportUpdatedSubs_MedicalReportUpdated_reportType {
  __typename: "ReportTypeInputType";
  ref: string | null;
  name: string;
  itemId: string | null;
}

export interface MedicalReportUpdatedSubs_MedicalReportUpdated_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface MedicalReportUpdatedSubs_MedicalReportUpdated_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface MedicalReportUpdatedSubs_MedicalReportUpdated {
  __typename: "MedicalReportModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  updatedBy: MedicalReportUpdatedSubs_MedicalReportUpdated_updatedBy | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  createdBy: MedicalReportUpdatedSubs_MedicalReportUpdated_createdBy;
  creatorId: string | null;
  creatorName: string | null;
  profile: MedicalReportUpdatedSubs_MedicalReportUpdated_profile | null;
  reportDate: any | null;
  reportType: MedicalReportUpdatedSubs_MedicalReportUpdated_reportType[] | null;
  patientClinifyId: string | null;
  patientFullname: string | null;
  patientPhone: string | null;
  patientEmail: string | null;
  report: string[] | null;
  rank: string | null;
  department: string | null;
  doctorName: string | null;
  specialty: string | null;
  serviceDetails: MedicalReportUpdatedSubs_MedicalReportUpdated_serviceDetails[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  hospital: MedicalReportUpdatedSubs_MedicalReportUpdated_hospital | null;
  hospitalId: string | null;
  status: MedicalReportStatus | null;
}

export interface MedicalReportUpdatedSubs {
  MedicalReportUpdated: MedicalReportUpdatedSubs_MedicalReportUpdated;
}

export interface MedicalReportUpdatedSubsVariables {
  hospitalId: string;
}
