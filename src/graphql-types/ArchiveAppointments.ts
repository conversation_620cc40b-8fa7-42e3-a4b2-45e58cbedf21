/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NextAppointmentEntities } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: ArchiveAppointments
// ====================================================

export interface ArchiveAppointments_archiveAppointments_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface ArchiveAppointments_archiveAppointments_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface ArchiveAppointments_archiveAppointments_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface ArchiveAppointments_archiveAppointments_specialist {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  type: string;
  fullName: string;
}

export interface ArchiveAppointments_archiveAppointments_serviceDetails {
  __typename: "ServiceDetailInputType";
  type: string | null;
  name: string | null;
}

export interface ArchiveAppointments_archiveAppointments {
  __typename: "OrganisationAppointmentModel";
  id: string;
  hospital: ArchiveAppointments_archiveAppointments_hospital | null;
  profile: ArchiveAppointments_archiveAppointments_profile | null;
  patientInformation: ArchiveAppointments_archiveAppointments_patientInformation | null;
  specialist: ArchiveAppointments_archiveAppointments_specialist | null;
  createdDate: any;
  updatedDate: any;
  status: string | null;
  category: string | null;
  liveSessionUrl: string | null;
  rank: string | null;
  reason: string | null;
  appointmentDateTime: any | null;
  startDateTime: any | null;
  endDateTime: any | null;
  specialty: string | null;
  role: string | null;
  paymentType: string | null;
  serviceDetails: ArchiveAppointments_archiveAppointments_serviceDetails[] | null;
  patientType: string | null;
  duration: string | null;
  confirmedBy: string | null;
  deliveryMethod: string | null;
  urgency: string | null;
  additionalNote: string | null;
  recordType: NextAppointmentEntities | null;
  documentUrl: string[] | null;
}

export interface ArchiveAppointments {
  archiveAppointments: ArchiveAppointments_archiveAppointments[];
}

export interface ArchiveAppointmentsVariables {
  ids: string[];
  archive: boolean;
}
