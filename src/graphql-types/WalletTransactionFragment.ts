/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { TransactionType, TransactionStatus, Currency } from "./globalTypes";

// ====================================================
// GraphQL fragment: WalletTransactionFragment
// ====================================================

export interface WalletTransactionFragment {
  __typename: "WalletTransactionModel";
  id: string;
  createdDate: any;
  amount: number;
  transactionType: TransactionType;
  transactionStatus: TransactionStatus;
  transactionDetails: string;
  currency: Currency;
  amountSent: number;
  description: string;
}
