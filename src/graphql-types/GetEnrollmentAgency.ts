/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetEnrollmentAgency
// ====================================================

export interface GetEnrollmentAgency_enrollmentAgency_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface GetEnrollmentAgency_enrollmentAgency_tpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
}

export interface GetEnrollmentAgency_enrollmentAgency_primaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetEnrollmentAgency_enrollmentAgency_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetEnrollmentAgency_enrollmentAgency_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetEnrollmentAgency_enrollmentAgency_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface GetEnrollmentAgency_enrollmentAgency {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
  agencyCode: string | null;
  address: string | null;
  country: string | null;
  state: string | null;
  agencyType: string | null;
  profile: GetEnrollmentAgency_enrollmentAgency_profile | null;
  tpaNonTpa: GetEnrollmentAgency_enrollmentAgency_tpaNonTpa | null;
  localGovernmentArea: string | null;
  primaryPhoneNumber: GetEnrollmentAgency_enrollmentAgency_primaryPhoneNumber | null;
  primaryEmailAddress: string | null;
  secondaryPhoneNumber: GetEnrollmentAgency_enrollmentAgency_secondaryPhoneNumber | null;
  secondaryEmailAddress: string | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: GetEnrollmentAgency_enrollmentAgency_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: GetEnrollmentAgency_enrollmentAgency_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  accountName: string | null;
  accountNumber: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface GetEnrollmentAgency {
  enrollmentAgency: GetEnrollmentAgency_enrollmentAgency;
}

export interface GetEnrollmentAgencyVariables {
  id: string;
}
