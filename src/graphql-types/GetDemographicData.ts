/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetDemographicData
// ====================================================

export interface GetDemographicData_getDemographicData {
  __typename: "BirthData";
  category: string | null;
  name: number | null;
  totalMale: number | null;
  totalFemale: number | null;
}

export interface GetDemographicData {
  getDemographicData: GetDemographicData_getDemographicData[];
}

export interface GetDemographicDataVariables {
  filter?: ServicesAnalyticsFilter | null;
}
