/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { WalkInFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHospitalTransferList
// ====================================================

export interface GetHospitalTransferList_hospital_walkInTransfers_list_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface GetHospitalTransferList_hospital_walkInTransfers_list {
  __typename: "WalkInTransferModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  transferFacilityName: string | null;
  transferFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: GetHospitalTransferList_hospital_walkInTransfers_list_patientInformation | null;
}

export interface GetHospitalTransferList_hospital_walkInTransfers {
  __typename: "WalkInTransferResponse";
  totalCount: number;
  list: GetHospitalTransferList_hospital_walkInTransfers_list[];
}

export interface GetHospitalTransferList_hospital {
  __typename: "HospitalModel";
  id: string;
  walkInTransfers: GetHospitalTransferList_hospital_walkInTransfers;
}

export interface GetHospitalTransferList {
  hospital: GetHospitalTransferList_hospital;
}

export interface GetHospitalTransferListVariables {
  filterOptions?: WalkInFilterInput | null;
  hospitalId?: string | null;
}
