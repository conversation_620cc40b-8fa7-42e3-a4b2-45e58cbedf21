/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicalReportStatus } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetMedicalReport
// ====================================================

export interface GetMedicalReport_medicalReport_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface GetMedicalReport_medicalReport_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface GetMedicalReport_medicalReport_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface GetMedicalReport_medicalReport_reportType {
  __typename: "ReportTypeInputType";
  ref: string | null;
  name: string;
  itemId: string | null;
}

export interface GetMedicalReport_medicalReport_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface GetMedicalReport_medicalReport_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface GetMedicalReport_medicalReport {
  __typename: "MedicalReportModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  updatedBy: GetMedicalReport_medicalReport_updatedBy | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  createdBy: GetMedicalReport_medicalReport_createdBy;
  creatorId: string | null;
  creatorName: string | null;
  profile: GetMedicalReport_medicalReport_profile | null;
  reportDate: any | null;
  reportType: GetMedicalReport_medicalReport_reportType[] | null;
  patientClinifyId: string | null;
  patientFullname: string | null;
  patientPhone: string | null;
  patientEmail: string | null;
  report: string[] | null;
  rank: string | null;
  department: string | null;
  doctorName: string | null;
  specialty: string | null;
  serviceDetails: GetMedicalReport_medicalReport_serviceDetails[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  hospital: GetMedicalReport_medicalReport_hospital | null;
  hospitalId: string | null;
  status: MedicalReportStatus | null;
}

export interface GetMedicalReport {
  medicalReport: GetMedicalReport_medicalReport;
}

export interface GetMedicalReportVariables {
  id: string;
}
