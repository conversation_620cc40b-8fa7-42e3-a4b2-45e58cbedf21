/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: AdmissionLineUpdatedSubs
// ====================================================

export interface AdmissionLineUpdatedSubs_AdmissionLineUpdated {
  __typename: "AdmissionLinesModel";
  id: string;
  placementDateTime: any | null;
  placedBy: string | null;
  placedPreArrival: string | null;
  ivChangeDue: any | null;
  size: string | null;
  orientation: string | null;
  site: string | null;
  siteAssessment: string | null;
  localAnesthetic: string | null;
  duration: string | null;
  lineStatus: string | null;
  dressingDateTime: any | null;
  dressingType: string | null;
  dressingAppearance: string[] | null;
  dressingIntervention: string[] | null;
  lastDressingChange: string | null;
  dressingChangeDue: string | null;
  urinaryCatheterPlacedInEd: string | null;
  insertedByAnother: string | null;
  insertedBy: string | null;
  insertionAttempts: string | null;
  patientTolerance: string | null;
  preExistingSite: string | null;
  cathetherType: string | null;
  tubeSize: string | null;
  catheterBallonSize: string | null;
  urineReturned: string | null;
  removalDateTime: any | null;
  removedBy: string | null;
  removalReason: string | null;
}

export interface AdmissionLineUpdatedSubs {
  AdmissionLineUpdated: AdmissionLineUpdatedSubs_AdmissionLineUpdated;
}

export interface AdmissionLineUpdatedSubsVariables {
  profileId: string;
}
