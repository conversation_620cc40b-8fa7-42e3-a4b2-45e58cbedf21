/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { RequestPayoutInput, PayoutStatus, Currency, CommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddRequestPayout
// ====================================================

export interface AddRequestPayout_addRequestPayout_receiverAccount {
  __typename: "BankAccountInformation";
  bankName: string;
  accountNumber: string;
  accountName: string | null;
  bankCode: string | null;
  accountType: string | null;
}

export interface AddRequestPayout_addRequestPayout_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface AddRequestPayout_addRequestPayout_virtualServicesPayments_bill_receiverProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface AddRequestPayout_addRequestPayout_virtualServicesPayments_bill {
  __typename: "BillModel";
  id: string;
  billingDateTime: any | null;
  raisedBy: string | null;
  receiverProfile: AddRequestPayout_addRequestPayout_virtualServicesPayments_bill_receiverProfile | null;
}

export interface AddRequestPayout_addRequestPayout_virtualServicesPayments {
  __typename: "VirtualServicesPaymentModel";
  id: string;
  billId: string | null;
  bill: AddRequestPayout_addRequestPayout_virtualServicesPayments_bill | null;
  amountDue: number;
  amountPaid: number;
  paymentStatus: string;
  commissionPayer: CommissionPayer | null;
  commissionFeeAmount: number | null;
  payoutId: string | null;
  payoutStatus: PayoutStatus;
  createdDate: any;
  updatedDate: any | null;
}

export interface AddRequestPayout_addRequestPayout_invoicePayments_invoice_recipient {
  __typename: "InvoiceRecipient";
  phone: string | null;
  address: string | null;
  email: string | null;
  name: string;
  clinifyId: string | null;
}

export interface AddRequestPayout_addRequestPayout_invoicePayments_invoice {
  __typename: "InvoiceModel";
  id: string;
  invoiceReference: string;
  issueDate: any;
  dueDate: any;
  recipient: AddRequestPayout_addRequestPayout_invoicePayments_invoice_recipient;
}

export interface AddRequestPayout_addRequestPayout_invoicePayments {
  __typename: "InvoicePaymentModel";
  id: string;
  paymentStatus: string;
  payoutStatus: PayoutStatus;
  amountDue: number;
  amountPaid: number;
  commissionFeeAmount: number | null;
  commissionFeePercentage: number | null;
  paymentMethod: string;
  invoice: AddRequestPayout_addRequestPayout_invoicePayments_invoice;
}

export interface AddRequestPayout_addRequestPayout_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface AddRequestPayout_addRequestPayout_payout {
  __typename: "PayoutModel";
  id: string;
  amountPaid: number;
  payoutStatus: PayoutStatus;
}

export interface AddRequestPayout_addRequestPayout {
  __typename: "RequestPayoutModel";
  id: string;
  requestPayoutDateTime: any;
  initiatedBy: string | null;
  createdDate: any;
  updatedDate: any;
  payoutStatus: PayoutStatus;
  payoutDescription: string | null;
  receiverAccount: AddRequestPayout_addRequestPayout_receiverAccount;
  currency: Currency;
  hospitalId: string;
  receiverInitialWalletBalanceBeforePayout: number;
  receiverInitialWalletBalanceBeforeRequest: number;
  requestAmount: number;
  totalCommissionFeeAmount: number;
  transactionStartDate: any;
  transactionEndDate: any;
  creatorName: string | null;
  lastModifierName: string | null;
  createdBy: AddRequestPayout_addRequestPayout_createdBy;
  virtualServicesPayments: AddRequestPayout_addRequestPayout_virtualServicesPayments[];
  invoicePayments: AddRequestPayout_addRequestPayout_invoicePayments[];
  hospital: AddRequestPayout_addRequestPayout_hospital;
  payout: AddRequestPayout_addRequestPayout_payout | null;
  additionalNote: string | null;
}

export interface AddRequestPayout {
  addRequestPayout: AddRequestPayout_addRequestPayout;
}

export interface AddRequestPayoutVariables {
  input: RequestPayoutInput;
}
