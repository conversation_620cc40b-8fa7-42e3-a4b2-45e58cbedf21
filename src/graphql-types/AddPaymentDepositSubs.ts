/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Currency } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: AddPaymentDepositSubs
// ====================================================

export interface AddPaymentDepositSubs_PaymentDepositAdded_collectedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface AddPaymentDepositSubs_PaymentDepositAdded_withdrawnBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface AddPaymentDepositSubs_PaymentDepositAdded {
  __typename: "PaymentDepositModel";
  id: string;
  depositDate: any;
  depositMethod: string | null;
  currency: Currency;
  profileId: string;
  hospitalId: string;
  amountDeposited: number;
  amountUsed: number;
  additionalNote: string | null;
  collectedById: string | null;
  creatorId: string;
  lastModifierId: string | null;
  description: string | null;
  createdDate: any;
  updatedDate: any | null;
  autoGenerated: boolean | null;
  amountRefunded: number | null;
  finalDepositBalance: number | null;
  initialDepositBalance: number | null;
  isManualRefund: boolean | null;
  refundDate: any | null;
  refundedBy: string | null;
  collectedBy: AddPaymentDepositSubs_PaymentDepositAdded_collectedBy | null;
  withdrawnBy: AddPaymentDepositSubs_PaymentDepositAdded_withdrawnBy | null;
}

export interface AddPaymentDepositSubs {
  PaymentDepositAdded: AddPaymentDepositSubs_PaymentDepositAdded;
}

export interface AddPaymentDepositSubsVariables {
  hospitalId: string;
  profileId: string;
}
