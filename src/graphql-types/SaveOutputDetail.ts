/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { OutputDetailsInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: SaveOutputDetail
// ====================================================

export interface SaveOutputDetail_saveOutputDetail {
  __typename: "OutputDetailsModel";
  id: string;
  administrationDateTime: any | null;
  administratorName: string | null;
  outputFluidType: string | null;
  outputQuantity: string | null;
  outputQuantityUnit: string | null;
  observations: string | null;
  concealObservations: boolean | null;
}

export interface SaveOutputDetail {
  saveOutputDetail: SaveOutputDetail_saveOutputDetail;
}

export interface SaveOutputDetailVariables {
  input: OutputDetailsInput;
  id: string;
  clinifyId: string;
  pin?: string | null;
}
