/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FeedbackFilterOptions } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetFeedbackSummary
// ====================================================

export interface GetFeedbackSummary_feedbackSummary {
  __typename: "FeedbackSummaryResponse";
  totalCount: number;
  totalResolved: number;
  totalUnresolved: number;
  totalProviderSubmitted: number | null;
}

export interface GetFeedbackSummary {
  feedbackSummary: GetFeedbackSummary_feedbackSummary;
}

export interface GetFeedbackSummaryVariables {
  filterOptions: FeedbackFilterOptions;
}
