/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: ChemoDrugUpdatedSubs
// ====================================================

export interface ChemoDrugUpdatedSubs_ChemoDrugUpdated_investigationDetails {
  __typename: "ChemoInvestigationDetails";
  investigationName: string | null;
  investigationType: string | null;
  investigationPerformed: boolean | null;
  investigationVerified: boolean | null;
}

export interface ChemoDrugUpdatedSubs_ChemoDrugUpdated_administrationRegister {
  __typename: "OncologyDrugAdministrationRegistration";
  administeredBy: string | null;
  administratorId: string | null;
  administrationDateTime: any | null;
  period: string;
}

export interface ChemoDrugUpdatedSubs_ChemoDrugUpdated_oncologyConsultationHistory_chemoComments {
  __typename: "ChemoCommentInputType";
  cycleNumber: number;
  section: string;
  comment: string | null;
  creatorId: string;
  creatorName: string;
  createdDate: any;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
}

export interface ChemoDrugUpdatedSubs_ChemoDrugUpdated_oncologyConsultationHistory {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  chemoComments: ChemoDrugUpdatedSubs_ChemoDrugUpdated_oncologyConsultationHistory_chemoComments[] | null;
}

export interface ChemoDrugUpdatedSubs_ChemoDrugUpdated {
  __typename: "OncologyChemoDrugModel";
  id: string;
  drugName: string;
  route: string | null;
  infusionUsed: string | null;
  dosage: string | null;
  dosagePercentage: string | null;
  totalDose: string | null;
  adjustedDose: string | null;
  quantity: string | null;
  day: string;
  cycleNumber: number;
  drugId: string | null;
  note: string | null;
  section: string;
  frequency: string | null;
  combinationGroupName: string | null;
  combinationName: string | null;
  chemoDiagnosis: string | null;
  inventoryClass: string | null;
  medicationDetailsId: string | null;
  investigationDetails: ChemoDrugUpdatedSubs_ChemoDrugUpdated_investigationDetails[] | null;
  administrationRegister: ChemoDrugUpdatedSubs_ChemoDrugUpdated_administrationRegister[] | null;
  oncologyConsultationHistory: ChemoDrugUpdatedSubs_ChemoDrugUpdated_oncologyConsultationHistory | null;
}

export interface ChemoDrugUpdatedSubs {
  ChemoDrugUpdated: ChemoDrugUpdatedSubs_ChemoDrugUpdated;
}

export interface ChemoDrugUpdatedSubsVariables {
  profileId: string;
}
