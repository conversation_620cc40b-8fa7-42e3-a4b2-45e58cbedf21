/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdatePlanTypeBenefit, BenefitCategory, BenefitCoverage } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateHmoPlanBenefits
// ====================================================

export interface UpdateHmoPlanBenefits_updateHmoPlanBenefits_visitType {
  __typename: "HmoVisitTypeModel";
  id: string;
  name: string | null;
}

export interface UpdateHmoPlanBenefits_updateHmoPlanBenefits_utilisationTypes {
  __typename: "HmoUtilisationType";
  name: string;
  annualLimitPerPerson: string | null;
  benefitCategory: BenefitCategory;
  benefitCoverage: BenefitCoverage;
  benefitLimit: string | null;
  code: string;
  id: string;
  price: string | null;
  bandBPrice: string | null;
  bandCPrice: string | null;
  visitLimit: string | null;
  waitingPeriodDays: number | null;
  quantity: number | null;
}

export interface UpdateHmoPlanBenefits_updateHmoPlanBenefits {
  __typename: "HmoPlanBenefitModel";
  id: string;
  name: string | null;
  code: string | null;
  limit: string | null;
  visitLimit: string | null;
  annualLimitPerPerson: string | null;
  waitingPeriodDays: number | null;
  planTypeId: string | null;
  utilisationCategory: string;
  visitTypeId: string | null;
  visitType: UpdateHmoPlanBenefits_updateHmoPlanBenefits_visitType | null;
  utilisationTypes: UpdateHmoPlanBenefits_updateHmoPlanBenefits_utilisationTypes[] | null;
  createdDate: any;
}

export interface UpdateHmoPlanBenefits {
  updateHmoPlanBenefits: UpdateHmoPlanBenefits_updateHmoPlanBenefits[];
}

export interface UpdateHmoPlanBenefitsVariables {
  providerId: string;
  planTypeId: string;
  input: UpdatePlanTypeBenefit[];
}
