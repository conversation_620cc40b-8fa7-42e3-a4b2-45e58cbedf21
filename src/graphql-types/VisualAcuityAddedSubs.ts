/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: VisualAcuityAddedSubs
// ====================================================

export interface VisualAcuityAddedSubs_VisualAcuityAdded {
  __typename: "VisualAcuityModel";
  id: string;
  readingDateTime: any | null;
  withGlassesLeft: string | null;
  withGlassesRight: string | null;
  withoutGlassesLeft: string | null;
  withoutGlassesRight: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  vitalId: string | null;
}

export interface VisualAcuityAddedSubs {
  VisualAcuityAdded: VisualAcuityAddedSubs_VisualAcuityAdded;
}

export interface VisualAcuityAddedSubsVariables {
  profileId: string;
}
