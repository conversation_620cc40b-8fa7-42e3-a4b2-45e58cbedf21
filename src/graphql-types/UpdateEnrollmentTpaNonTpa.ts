/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EnrollmentTpaNonTpaInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateEnrollmentTpaNonTpa
// ====================================================

export interface UpdateEnrollmentTpaNonTpa_updateEnrollmentTpaNonTpa_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface UpdateEnrollmentTpaNonTpa_updateEnrollmentTpaNonTpa_primaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrollmentTpaNonTpa_updateEnrollmentTpaNonTpa_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrollmentTpaNonTpa_updateEnrollmentTpaNonTpa_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrollmentTpaNonTpa_updateEnrollmentTpaNonTpa_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrollmentTpaNonTpa_updateEnrollmentTpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
  address: string | null;
  isTpa: boolean | null;
  country: string | null;
  state: string | null;
  profile: UpdateEnrollmentTpaNonTpa_updateEnrollmentTpaNonTpa_profile | null;
  localGovernmentArea: string | null;
  primaryPhoneNumber: UpdateEnrollmentTpaNonTpa_updateEnrollmentTpaNonTpa_primaryPhoneNumber | null;
  primaryEmailAddress: string | null;
  secondaryPhoneNumber: UpdateEnrollmentTpaNonTpa_updateEnrollmentTpaNonTpa_secondaryPhoneNumber | null;
  secondaryEmailAddress: string | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: UpdateEnrollmentTpaNonTpa_updateEnrollmentTpaNonTpa_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: UpdateEnrollmentTpaNonTpa_updateEnrollmentTpaNonTpa_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  tpaNumber: string | null;
  tpaCode: string | null;
  accountName: string | null;
  accountNumber: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface UpdateEnrollmentTpaNonTpa {
  updateEnrollmentTpaNonTpa: UpdateEnrollmentTpaNonTpa_updateEnrollmentTpaNonTpa;
}

export interface UpdateEnrollmentTpaNonTpaVariables {
  id: string;
  input: EnrollmentTpaNonTpaInput;
}
