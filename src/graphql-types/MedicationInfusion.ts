/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: MedicationInfusion
// ====================================================

export interface MedicationInfusion_details {
  __typename: "MedicationInfusionDetails";
  typeOrStrength: string | null;
  volume: string | null;
  drugName: string | null;
  dosage: string | null;
  route: string | null;
  dripRate: string | null;
  batchNumber: string | null;
  prescribedBy: string | null;
  administrationDateTime: any | null;
  administeredBy: string | null;
  checkedBy: string | null;
}

export interface MedicationInfusion {
  __typename: "MedicationInfusionModel";
  id: string;
  creator: string | null;
  updater: string | null;
  createdDate: any;
  updatedDate: any;
  medicationDetailId: string | null;
  details: MedicationInfusion_details[] | null;
}
