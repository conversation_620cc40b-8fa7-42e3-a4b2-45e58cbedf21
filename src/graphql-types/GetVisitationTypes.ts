/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetVisitationTypes
// ====================================================

export interface GetVisitationTypes_getVisitationTypes_list {
  __typename: "OptionObject";
  label: string;
  value: string;
}

export interface GetVisitationTypes_getVisitationTypes {
  __typename: "HmoUtilizationTypesResponse";
  list: GetVisitationTypes_getVisitationTypes_list[];
}

export interface GetVisitationTypes {
  getVisitationTypes: GetVisitationTypes_getVisitationTypes;
}

export interface GetVisitationTypesVariables {
  providerId: string;
  clinifyId: string;
  hospitalId?: string | null;
  externalPlanTypeId?: string | null;
}
