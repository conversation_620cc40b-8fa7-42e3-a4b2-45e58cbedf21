/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: ConsultationUpdatedSubs
// ====================================================

export interface ConsultationUpdatedSubs_ConsultationUpdated_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_complaintSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_systemReviewSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_physicalExamSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: ConsultationUpdatedSubs_ConsultationUpdated_profile_coverageDetails_provider | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: ConsultationUpdatedSubs_ConsultationUpdated_profile_coverageDetails[] | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_provisionalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_finalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: ConsultationUpdatedSubs_ConsultationUpdated_preauthorizationDetails_provider | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_referredTo_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_referredTo {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  hospital: ConsultationUpdatedSubs_ConsultationUpdated_referredTo_hospital | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_allergies {
  __typename: "AllergyModel";
  id: string;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_labTests {
  __typename: "LabResultModel";
  id: string;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_radiology {
  __typename: "RadiologyResultModel";
  id: string;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_investigations {
  __typename: "LabResultModel";
  id: string;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface ConsultationUpdatedSubs_ConsultationUpdated {
  __typename: "ConsultationModel";
  id: string;
  consultationDateTime: any | null;
  duration: string | null;
  doctorName: string;
  priority: string | null;
  specialty: string | null;
  class: string | null;
  hmoProviderId: string | null;
  serviceDetails: ConsultationUpdatedSubs_ConsultationUpdated_serviceDetails[] | null;
  isPackage: boolean;
  clinicName: string | null;
  clinicAddress: string | null;
  complaint: string | null;
  provider: string | null;
  providerServiceName: string | null;
  complaintHistory: string | null;
  healthEducation: string | null;
  systemReview: string | null;
  systemReviewSmartText: string | null;
  complaintGender: string | null;
  complaintSmartSelection: ConsultationUpdatedSubs_ConsultationUpdated_complaintSmartSelection | null;
  systemReviewSmartSelection: ConsultationUpdatedSubs_ConsultationUpdated_systemReviewSmartSelection | null;
  category: string | null;
  department: string | null;
  physicalExam: string | null;
  physicalExamSmartText: string | null;
  physicalExamSmartSelection: ConsultationUpdatedSubs_ConsultationUpdated_physicalExamSmartSelection | null;
  referral: boolean | null;
  referralDate: any | null;
  referralReason: string | null;
  concealReferralReason: boolean | null;
  specialtyReferredTo: string | null;
  profileId: string | null;
  profile: ConsultationUpdatedSubs_ConsultationUpdated_profile | null;
  externalReferral: boolean | null;
  provisionalDiagnosis: ConsultationUpdatedSubs_ConsultationUpdated_provisionalDiagnosis[] | null;
  finalDiagnosis: ConsultationUpdatedSubs_ConsultationUpdated_finalDiagnosis[] | null;
  consultationStartDate: any | null;
  consultationEndDate: any | null;
  appointmentId: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  concealComplaint: boolean | null;
  concealComplaintHistory: boolean | null;
  concealPhysicalExam: boolean | null;
  concealSystemReview: boolean | null;
  concealHealthEducation: boolean | null;
  billStatus: string | null;
  audiometry: string | null;
  concealAudiometry: boolean | null;
  hospitalId: string | null;
  preauthorizationDetails: ConsultationUpdatedSubs_ConsultationUpdated_preauthorizationDetails | null;
  referredTo: ConsultationUpdatedSubs_ConsultationUpdated_referredTo | null;
  allergies: ConsultationUpdatedSubs_ConsultationUpdated_allergies[];
  medications: ConsultationUpdatedSubs_ConsultationUpdated_medications[];
  surgeries: ConsultationUpdatedSubs_ConsultationUpdated_surgeries[];
  admissions: ConsultationUpdatedSubs_ConsultationUpdated_admissions[];
  vitals: ConsultationUpdatedSubs_ConsultationUpdated_vitals[];
  labTests: ConsultationUpdatedSubs_ConsultationUpdated_labTests[];
  radiology: ConsultationUpdatedSubs_ConsultationUpdated_radiology[];
  investigations: ConsultationUpdatedSubs_ConsultationUpdated_investigations[];
  nursingServices: ConsultationUpdatedSubs_ConsultationUpdated_nursingServices[];
}

export interface ConsultationUpdatedSubs {
  ConsultationUpdated: ConsultationUpdatedSubs_ConsultationUpdated;
}

export interface ConsultationUpdatedSubsVariables {
  profileId: string;
  hospitalId: string;
}
