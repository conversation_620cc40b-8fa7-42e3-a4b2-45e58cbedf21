/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: FacilityStaffsData
// ====================================================

export interface FacilityStaffsData_staffSummary {
  __typename: "StaffSummaryType";
  hospitalName: string | null;
  hospitalId: string | null;
  totalActiveStaffs: number | null;
  totalFemaleStaffs: number | null;
  totalInActiveStaffs: number | null;
  totalMaleStaffs: number | null;
  totalStaffs: number | null;
}

export interface FacilityStaffsData_rolesSummary {
  __typename: "RoleSummaryType";
  hospitalName: string | null;
  count: number | null;
}

export interface FacilityStaffsData {
  __typename: "FacilityStaffsData";
  name: number | null;
  totalStaffs: number | null;
  totalMaleStaffs: number | null;
  totalFemaleStaffs: number | null;
  totalActiveStaffs: number | null;
  totalInActiveStaffs: number | null;
  totalRoles: number | null;
  staffSummary: FacilityStaffsData_staffSummary[] | null;
  rolesSummary: FacilityStaffsData_rolesSummary[] | null;
}
