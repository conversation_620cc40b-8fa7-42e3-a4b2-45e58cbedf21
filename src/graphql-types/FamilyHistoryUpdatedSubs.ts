/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: FamilyHistoryUpdatedSubs
// ====================================================

export interface FamilyHistoryUpdatedSubs_FamilyHistoryUpdated_conditions {
  __typename: "ConditionInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
  ageOfOnset: string | null;
}

export interface FamilyHistoryUpdatedSubs_FamilyHistoryUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface FamilyHistoryUpdatedSubs_FamilyHistoryUpdated {
  __typename: "FamilyHistoryModel";
  id: string;
  title: string | null;
  firstName: string | null;
  lastName: string | null;
  middleName: string | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  relationship: string | null;
  conditions: FamilyHistoryUpdatedSubs_FamilyHistoryUpdated_conditions[];
  gender: Gender | null;
  additionalNote: string | null;
  profile: FamilyHistoryUpdatedSubs_FamilyHistoryUpdated_profile | null;
}

export interface FamilyHistoryUpdatedSubs {
  FamilyHistoryUpdated: FamilyHistoryUpdatedSubs_FamilyHistoryUpdated;
}

export interface FamilyHistoryUpdatedSubsVariables {
  profileId: string;
}
