/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PaymentDepositRefundInput, Currency } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddDepositRefund
// ====================================================

export interface AddDepositRefund_addPaymentDepositRefund_collectedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface AddDepositRefund_addPaymentDepositRefund_withdrawnBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface AddDepositRefund_addPaymentDepositRefund {
  __typename: "PaymentDepositModel";
  id: string;
  depositDate: any;
  depositMethod: string | null;
  currency: Currency;
  profileId: string;
  hospitalId: string;
  amountDeposited: number;
  amountUsed: number;
  additionalNote: string | null;
  collectedById: string | null;
  creatorId: string;
  lastModifierId: string | null;
  description: string | null;
  createdDate: any;
  updatedDate: any | null;
  autoGenerated: boolean | null;
  amountRefunded: number | null;
  finalDepositBalance: number | null;
  initialDepositBalance: number | null;
  isManualRefund: boolean | null;
  refundDate: any | null;
  refundedBy: string | null;
  collectedBy: AddDepositRefund_addPaymentDepositRefund_collectedBy | null;
  withdrawnBy: AddDepositRefund_addPaymentDepositRefund_withdrawnBy | null;
}

export interface AddDepositRefund {
  addPaymentDepositRefund: AddDepositRefund_addPaymentDepositRefund;
}

export interface AddDepositRefundVariables {
  input: PaymentDepositRefundInput;
}
