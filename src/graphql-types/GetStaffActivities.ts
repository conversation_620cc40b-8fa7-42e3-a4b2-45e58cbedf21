/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { StaffActivitiesFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetStaffActivities
// ====================================================

export interface GetStaffActivities_getStaffActivities_staff {
  __typename: "StaffDetail";
  fullName: string | null;
  clinifyId: string | null;
  type: string | null;
}

export interface GetStaffActivities_getStaffActivities_list_staff {
  __typename: "SA_StaffDetail";
  fullName: string | null;
  clinifyId: string | null;
  type: string | null;
}

export interface GetStaffActivities_getStaffActivities_list_patient {
  __typename: "SA_PatientDetail";
  fullName: string | null;
  clinifyId: string | null;
  weight: string | null;
  height: string | null;
  dob: any | null;
  bloodGroup: string | null;
  genoType: string | null;
  gender: string | null;
  bmi: string | null;
  coverageType: string | null;
  coverageName: string | null;
}

export interface GetStaffActivities_getStaffActivities_list_investigationRadiology_exams {
  __typename: "SA_InvestigationRadiologyExam";
  name: string | null;
  radiographerName: string | null;
  status: string | null;
  date: any | null;
  indication: string | null;
  findings: string[] | null;
  impression: string | null;
}

export interface GetStaffActivities_getStaffActivities_list_investigationRadiology {
  __typename: "SA_InvestigationRadiology";
  procedureType: string | null;
  exams: GetStaffActivities_getStaffActivities_list_investigationRadiology_exams[] | null;
  isProcessed: boolean | null;
  orderedBy: string | null;
  date: any | null;
}

export interface GetStaffActivities_getStaffActivities_list_investigationLaboratory_tests {
  __typename: "SA_LaboratoryTest";
  name: string | null;
  date: any | null;
  results: string[] | null;
  referenceRange: (string | null)[] | null;
  status: string | null;
  verifiedBy: string | null;
}

export interface GetStaffActivities_getStaffActivities_list_investigationLaboratory {
  __typename: "SA_InvestigationLaboratory";
  tests: GetStaffActivities_getStaffActivities_list_investigationLaboratory_tests[] | null;
  isProcessed: boolean | null;
  orderedBy: string | null;
  date: any | null;
}

export interface GetStaffActivities_getStaffActivities_list_vitalSigns {
  __typename: "SA_VitalSigns";
  id: string | null;
  bmi: string | null;
  bsa: string | null;
  takenBy: string | null;
  date: any | null;
  height: string | null;
  weight: string | null;
  pulseRate: string | null;
  heartRate: string | null;
  bloodPressure: string | null;
  bloodGlucose: string | null;
  temperature: string | null;
  respiratoryRate: string | null;
  oxygenSaturation: string | null;
  isHeightCritical: boolean | null;
  isWeightCritical: boolean | null;
  isOxygenSaturationCritical: boolean | null;
  isRespiratoryRateCritical: boolean | null;
  isDiastolicCritical: boolean | null;
  isSystolicCritical: boolean | null;
  isTemperatureCritical: boolean | null;
  isPulseRateCritical: boolean | null;
  isBloodGlucoseCritical: boolean | null;
  isHeartRateCritical: boolean | null;
}

export interface GetStaffActivities_getStaffActivities_list_consultations_chemoDrugs_investigations {
  __typename: "SA_OncologyChemoInvestigation";
  name: string | null;
  type: string | null;
}

export interface GetStaffActivities_getStaffActivities_list_consultations_chemoDrugs {
  __typename: "SA_OncologyChemoDrugs";
  section: string | null;
  combinationName: string | null;
  cycleNumber: number | null;
  day: string | null;
  drugName: string | null;
  dosage: string | null;
  dosagePercentage: string | null;
  totalDose: string | null;
  adjustedDose: string | null;
  route: string | null;
  frequency: string | null;
  chemoDiagnosis: string | null;
  infusionUsed: string | null;
  quantity: string | null;
  administrationDateTime: any | null;
  administeredBy: string | null;
  investigations: GetStaffActivities_getStaffActivities_list_consultations_chemoDrugs_investigations[] | null;
}

export interface GetStaffActivities_getStaffActivities_list_consultations {
  __typename: "SA_Consultations";
  id: string | null;
  initialDiagnosis: string[] | null;
  finalDiagnosis: string[] | null;
  treatmentPlans: string[] | null;
  presentingComplaint: string | null;
  historyOfPresentingComplaint: string | null;
  doctorName: string | null;
  reviewOfSystem: string | null;
  physicalExamination: string | null;
  chemoNote: string | null;
  date: any | null;
  consultationType: string | null;
  chemoDrugs: GetStaffActivities_getStaffActivities_list_consultations_chemoDrugs[] | null;
}

export interface GetStaffActivities_getStaffActivities_list_nursingServices {
  __typename: "SA_NursingServices";
  procedureType: string | null;
  dob: any | null;
  consentGiven: string | null;
  parentGuardianPresent: string | null;
  anaesthesiaGiven: string | null;
  vitaminKGiven: string | null;
  assistantNurseName: string | null;
  castLocation: string | null;
  isItARepeatedCasting: string | null;
  reasonForCasting: string | null;
  hasRadiologicalInvestigationBeenDone: string | null;
  whichEar: string | null;
  observation: string | null;
  councelled: string | null;
  method: string | null;
  informedConsent: string | null;
  woundLocation: string | null;
  dressingType: string | null;
  dressingAppearance: string | null;
  dressingIntervention: string | null;
  lastDressingChange: any | null;
  painScore: string | null;
  painDescriptors: string[] | null;
  dressingChangeDue: any | null;
  signOfInfection: string | null;
  nurseName: string | null;
  date: any | null;
}

export interface GetStaffActivities_getStaffActivities_list_pregnancyCare {
  __typename: "SA_PregnancyCare";
  type: string | null;
  lastMenstrualPeriod: any | null;
  estimatedDateOfDelivery: any | null;
  gravidity: string | null;
  parity: string | null;
  maternalBloodPressure: string | null;
  fetalHeartRate: string | null;
  symphiosioFundalHeight: string | null;
  presentation: string | null;
  position: string | null;
  lie: string | null;
  fetalMovement: string | null;
  oedema: string | null;
  gestationalAge: string | null;
  induction: string | null;
  inductionMethod: string | null;
  epiduralGiven: string | null;
  preterm: string | null;
  fetalMonitoring: string | null;
  methodOfDelivery: string | null;
  babyDeliveryDate: any | null;
  motherStatus: string | null;
  babyStatus: string | null;
  babyGender: string | null;
  birthWeight: string | null;
  apgarScore: string | null;
  estimatedBloodLoss: string | null;
  cervicalTear: string | null;
  perinealLaceration: string | null;
  birthInjury: string | null;
  deliveryDate: any | null;
  hasMenstruationStarted: string | null;
  uterus: string | null;
  breastfeedingChoice: string | null;
  babyComplaints: string | null;
  visitationNote: string | null;
  seenBy: string | null;
  date: any | null;
  deliveredBy: string | null;
}

export interface GetStaffActivities_getStaffActivities_list_admissions_discharges {
  __typename: "SA_Admissions_Discharges";
  dischargeDiagnosis: string[] | null;
  dischargedStatus: string | null;
  dischargedBy: string | null;
  deathDate: any | null;
  dischargeSummary: string | null;
  dischargedDate: any | null;
}

export interface GetStaffActivities_getStaffActivities_list_admissions {
  __typename: "SA_Admissions";
  admissionDiagnosis: string[] | null;
  admissionWard: string | null;
  doctorNotes: string[] | null;
  nurseNotes: string[] | null;
  admissionDate: any | null;
  admittedBy: string | null;
  discharges: GetStaffActivities_getStaffActivities_list_admissions_discharges[] | null;
}

export interface GetStaffActivities_getStaffActivities_list_immunizations {
  __typename: "SA_Immunizations";
  routeOfAdministration: string | null;
  dosage: string | null;
  quantity: string | null;
  vacinationName: string | null;
  givenBy: string | null;
  date: any | null;
}

export interface GetStaffActivities_getStaffActivities_list_medications_prescriptions {
  __typename: "SA_MedicationPrescription";
  name: string | null;
  frequency: string | null;
  routeOfAdministration: string | null;
  date: any | null;
  dosage: string | null;
  quantity: string | null;
  duration: string | null;
  prescribedBy: string | null;
}

export interface GetStaffActivities_getStaffActivities_list_medications_dispenses {
  __typename: "SA_MedicationDispense";
  name: string | null;
  frequency: string | null;
  routeOfAdministration: string | null;
  date: any | null;
  dosage: string | null;
  duration: string | null;
  quantity: string | null;
  dispensedBy: string | null;
}

export interface GetStaffActivities_getStaffActivities_list_medications {
  __typename: "SA_Medications";
  prescriptions: GetStaffActivities_getStaffActivities_list_medications_prescriptions[] | null;
  dispenses: GetStaffActivities_getStaffActivities_list_medications_dispenses[] | null;
  prescribedBy: string | null;
  date: any | null;
}

export interface GetStaffActivities_getStaffActivities_list_consumables_consumables {
  __typename: "SA_ConsumablesPrescription";
  name: string | null;
  quantity: string | null;
  date: string | null;
  prescribedBy: string | null;
}

export interface GetStaffActivities_getStaffActivities_list_consumables_dispenses {
  __typename: "SA_ConsumablesConsumed";
  name: string | null;
  date: string | null;
  dispensedBy: string | null;
  quantity: string | null;
}

export interface GetStaffActivities_getStaffActivities_list_consumables {
  __typename: "SA_Consumables";
  consumables: GetStaffActivities_getStaffActivities_list_consumables_consumables[] | null;
  dispenses: GetStaffActivities_getStaffActivities_list_consumables_dispenses[] | null;
  prescribedBy: string | null;
  date: any | null;
}

export interface GetStaffActivities_getStaffActivities_list_procedures {
  __typename: "SA_Procedures";
  procedureTypes: string[] | null;
  postOperationNotes: string[] | null;
  endDate: any | null;
  startDate: any | null;
  operationNotes: string[] | null;
  indication: string | null;
  patientConsent: string | null;
  surgeonName: string | null;
  requestedBy: string | null;
  date: any | null;
}

export interface GetStaffActivities_getStaffActivities_list_medicalReports {
  __typename: "SA_MedicalReport";
  reportDate: any | null;
  reportedBy: string | null;
  report: string[] | null;
  reportType: string[] | null;
}

export interface GetStaffActivities_getStaffActivities_list {
  __typename: "StaffActivities";
  startDate: any | null;
  endDate: any | null;
  serviceType: string;
  staff: GetStaffActivities_getStaffActivities_list_staff | null;
  patient: GetStaffActivities_getStaffActivities_list_patient;
  investigationRadiology: GetStaffActivities_getStaffActivities_list_investigationRadiology | null;
  investigationLaboratory: GetStaffActivities_getStaffActivities_list_investigationLaboratory | null;
  vitalSigns: GetStaffActivities_getStaffActivities_list_vitalSigns | null;
  consultations: GetStaffActivities_getStaffActivities_list_consultations | null;
  nursingServices: GetStaffActivities_getStaffActivities_list_nursingServices | null;
  pregnancyCare: GetStaffActivities_getStaffActivities_list_pregnancyCare | null;
  admissions: GetStaffActivities_getStaffActivities_list_admissions | null;
  immunizations: GetStaffActivities_getStaffActivities_list_immunizations | null;
  medications: GetStaffActivities_getStaffActivities_list_medications | null;
  consumables: GetStaffActivities_getStaffActivities_list_consumables | null;
  procedures: GetStaffActivities_getStaffActivities_list_procedures | null;
  medicalReports: GetStaffActivities_getStaffActivities_list_medicalReports | null;
}

export interface GetStaffActivities_getStaffActivities {
  __typename: "StaffActivitiesResponse";
  staff: GetStaffActivities_getStaffActivities_staff | null;
  list: GetStaffActivities_getStaffActivities_list[] | null;
}

export interface GetStaffActivities {
  getStaffActivities: GetStaffActivities_getStaffActivities;
}

export interface GetStaffActivitiesVariables {
  filter: StaffActivitiesFilter;
}
