/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UrineDipstickAddedSubs
// ====================================================

export interface UrineDipstickAddedSubs_UrineDipstickAdded {
  __typename: "UrineDipstickModel";
  id: string;
  readingDateTime: any | null;
  blood: string | null;
  glucose: string | null;
  ketones: string | null;
  ph: string | null;
  protein: string | null;
  nitrites: string | null;
  leucocyte: string | null;
  urobilinogen: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isPhCritical: boolean | null;
  vitalId: string | null;
}

export interface UrineDipstickAddedSubs {
  UrineDipstickAdded: UrineDipstickAddedSubs_UrineDipstickAdded;
}

export interface UrineDipstickAddedSubsVariables {
  profileId: string;
}
