/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BloodPressureVitalFields } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateBloodPressureInfo
// ====================================================

export interface UpdateBloodPressureInfo_updateBloodPressureInfo {
  __typename: "BloodPressureModel";
  id: string;
  readingDateTime: any | null;
  diastolic: string | null;
  systolic: string | null;
  meanArterialPressure: string | null;
  heartRate: string | null;
  fetalHeartRate: string | null;
  location: string | null;
  method: string | null;
  additionalNote: string | null;
  position: string | null;
  rhythm: string | null;
  concealAdditionalNote: boolean | null;
  isDiastolicCritical: boolean | null;
  isSystolicCritical: boolean | null;
  isHeartRateCritical: boolean | null;
  isFetalHeartRateCritical: boolean | null;
}

export interface UpdateBloodPressureInfo {
  updateBloodPressureInfo: UpdateBloodPressureInfo_updateBloodPressureInfo;
}

export interface UpdateBloodPressureInfoVariables {
  input: BloodPressureVitalFields;
  id: string;
  clinifyId: string;
}
