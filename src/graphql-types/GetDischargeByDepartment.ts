/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AdmissionAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetDischargeByDepartment
// ====================================================

export interface GetDischargeByDepartment_getDischargeByDepartment {
  __typename: "CategoryDataResponse";
  category: string | null;
  count: number | null;
  name: number | null;
}

export interface GetDischargeByDepartment {
  getDischargeByDepartment: GetDischargeByDepartment_getDischargeByDepartment[];
}

export interface GetDischargeByDepartmentVariables {
  filter?: AdmissionAnalyticsFilter | null;
}
