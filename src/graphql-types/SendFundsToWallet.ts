/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { SendFundsInput, TransactionType, TransactionStatus, Currency } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: SendFundsToWallet
// ====================================================

export interface SendFundsToWallet_sendFundsToWallet_senderWallet_profile {
  __typename: "ProfileModel";
  clinifyId: string;
  fullName: string;
}

export interface SendFundsToWallet_sendFundsToWallet_senderWallet {
  __typename: "WalletModel";
  profile: SendFundsToWallet_sendFundsToWallet_senderWallet_profile | null;
}

export interface SendFundsToWallet_sendFundsToWallet_receiverWallet_profile {
  __typename: "ProfileModel";
  clinifyId: string;
  fullName: string;
}

export interface SendFundsToWallet_sendFundsToWallet_receiverWallet {
  __typename: "WalletModel";
  profile: SendFundsToWallet_sendFundsToWallet_receiverWallet_profile | null;
}

export interface SendFundsToWallet_sendFundsToWallet {
  __typename: "WalletTransactionModel";
  id: string;
  createdDate: any;
  amount: number;
  transactionType: TransactionType;
  transactionStatus: TransactionStatus;
  transactionDetails: string;
  currency: Currency;
  amountSent: number;
  description: string;
  senderWallet: SendFundsToWallet_sendFundsToWallet_senderWallet | null;
  receiverWallet: SendFundsToWallet_sendFundsToWallet_receiverWallet | null;
}

export interface SendFundsToWallet {
  sendFundsToWallet: SendFundsToWallet_sendFundsToWallet;
}

export interface SendFundsToWalletVariables {
  input: SendFundsInput;
}
