/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: InputDetailUpdatedSubs
// ====================================================

export interface InputDetailUpdatedSubs_InputDetailUpdated {
  __typename: "InputDetailsModel";
  id: string;
  administrationDateTime: any | null;
  administratorName: string | null;
  inputFluidType: string | null;
  routeOfAdministration: string | null;
  inputQuantity: string | null;
  inputQuantityUnit: string | null;
  duration: string | null;
}

export interface InputDetailUpdatedSubs {
  InputDetailUpdated: InputDetailUpdatedSubs_InputDetailUpdated;
}

export interface InputDetailUpdatedSubsVariables {
  profileId: string;
}
