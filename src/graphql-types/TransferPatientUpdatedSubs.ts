/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: TransferPatientUpdatedSubs
// ====================================================

export interface TransferPatientUpdatedSubs_TransferPatientUpdated_transferHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  clinifyId: string | null;
}

export interface TransferPatientUpdatedSubs_TransferPatientUpdated {
  __typename: "TransferPatientModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  roomOption: string | null;
  transferSource: string | null;
  roomInventoryId: string | null;
  transferHospital: TransferPatientUpdatedSubs_TransferPatientUpdated_transferHospital | null;
}

export interface TransferPatientUpdatedSubs {
  TransferPatientUpdated: TransferPatientUpdatedSubs_TransferPatientUpdated;
}

export interface TransferPatientUpdatedSubsVariables {
  profileId: string;
}
