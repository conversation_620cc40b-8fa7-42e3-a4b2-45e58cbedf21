/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ArchiveFeedbacks
// ====================================================

export interface ArchiveFeedbacks_archiveFeedbacks_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface ArchiveFeedbacks_archiveFeedbacks_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface ArchiveFeedbacks_archiveFeedbacks {
  __typename: "FeedbackModel";
  id: string;
  feedbackDateTime: any | null;
  title: string;
  message: string;
  comment: string | null;
  category: string;
  isResolved: boolean | null;
  documentUrl: string[] | null;
  createdDate: any;
  creatorName: string | null;
  reviewerName: string | null;
  hospital: ArchiveFeedbacks_archiveFeedbacks_hospital | null;
  createdBy: ArchiveFeedbacks_archiveFeedbacks_createdBy;
}

export interface ArchiveFeedbacks {
  archiveFeedbacks: ArchiveFeedbacks_archiveFeedbacks[];
}

export interface ArchiveFeedbacksVariables {
  ids: string[];
  archive: boolean;
}
