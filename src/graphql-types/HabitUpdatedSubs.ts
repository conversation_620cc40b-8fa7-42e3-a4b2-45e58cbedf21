/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: HabitUpdatedSubs
// ====================================================

export interface HabitUpdatedSubs_HabitUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface HabitUpdatedSubs_HabitUpdated {
  __typename: "HabitModel";
  id: string;
  socialHabit: string | null;
  level: string | null;
  duration: string | null;
  typeSpecified: string | null;
  cigrattesPerDay: string | null;
  unitPerWeek: string | null;
  additionalNote: string | null;
  profile: HabitUpdatedSubs_HabitUpdated_profile | null;
}

export interface HabitUpdatedSubs {
  HabitUpdated: HabitUpdatedSubs_HabitUpdated;
}

export interface HabitUpdatedSubsVariables {
  profileId: string;
}
