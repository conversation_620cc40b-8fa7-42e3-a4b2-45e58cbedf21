/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AllPatientProfilesFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetAllPatientProfiles
// ====================================================

export interface GetAllPatientProfiles_getAllPatientProfiles_user {
  __typename: "UserModel";
  id: string;
  phoneNumber: string | null;
}

export interface GetAllPatientProfiles_getAllPatientProfiles {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  user: GetAllPatientProfiles_getAllPatientProfiles_user;
}

export interface GetAllPatientProfiles {
  getAllPatientProfiles: GetAllPatientProfiles_getAllPatientProfiles[];
}

export interface GetAllPatientProfilesVariables {
  filterOptions: AllPatientProfilesFilterInput;
}
