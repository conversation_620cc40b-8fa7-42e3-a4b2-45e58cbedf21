/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetNursingServiceData
// ====================================================

export interface GetNursingServiceData_getNursingServiceData_byAgeRange {
  __typename: "NursingServiceByAgeRange";
  procedureType: string | null;
  totalMale: number | null;
  totalFemale: number | null;
  ageRange: string | null;
  totalMaleAmount: number | null;
  totalFemaleAmount: number | null;
}

export interface GetNursingServiceData_getNursingServiceData_byNurseName {
  __typename: "NursingServiceByNurseName";
  nurseName: string | null;
  procedureType: string | null;
  procedureDate: string | null;
  amount: string | null;
  patientName: string | null;
  clinifyId: string | null;
  paymentType: string | null;
  quantity: string | null;
}

export interface GetNursingServiceData_getNursingServiceData {
  __typename: "NursingServiceSummaryResponse";
  totalMale: number | null;
  totalFemale: number | null;
  name: number | null;
  totalFemaleAmount: number | null;
  totalMaleAmount: number | null;
  byAgeRange: GetNursingServiceData_getNursingServiceData_byAgeRange[] | null;
  byNurseName: GetNursingServiceData_getNursingServiceData_byNurseName[] | null;
}

export interface GetNursingServiceData {
  getNursingServiceData: GetNursingServiceData_getNursingServiceData;
}

export interface GetNursingServiceDataVariables {
  filter?: ServicesAnalyticsFilter | null;
}
