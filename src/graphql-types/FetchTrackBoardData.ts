/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { TrackboardFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: FetchTrackBoardData
// ====================================================

export interface FetchTrackBoardData_fetchTrackboardData_list_admissionDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_patientDetails {
  __typename: "TrackboardPatientDetails";
  id: string | null;
  fullName: string | null;
  gender: string | null;
  clinifyId: string | null;
  patientStatus: string | null;
  dateOfBirth: string | null;
  registrationDate: string | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_labTests_testResult {
  __typename: "TrackboardlabTestsResult";
  testName: string | null;
  done: boolean | null;
  testDate: any | null;
  results: string[] | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_labTests {
  __typename: "TrackboardlabTests";
  testNames: string[] | null;
  status: string | null;
  testResult: FetchTrackBoardData_fetchTrackboardData_list_labTests_testResult[] | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_radExam_examResult {
  __typename: "TrackboardRadExamResult";
  examType: string | null;
  done: boolean | null;
  examDate: any | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_radExam {
  __typename: "TrackboardRadExam";
  examTypes: string[] | null;
  status: string | null;
  examResult: FetchTrackBoardData_fetchTrackboardData_list_radExam_examResult[] | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_waitingInfo {
  __typename: "TrackboardWaitingInfo";
  status: string | null;
  waitTime: string | null;
  emergencyType: string | null;
  resuscitationActionPlan: string | null;
  createdDate: any | null;
  updatedDate: any | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_dependents {
  __typename: "TrackboardDependent";
  title: string | null;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_bloodTransfusion {
  __typename: "TrackboardBloodTransfusion";
  date: any | null;
  bloodGroup: string | null;
  genoType: string | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_careTeam {
  __typename: "TrackboardCareTeam";
  specialistFullName: string | null;
  specialty: string | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_prescriptions {
  __typename: "TrackboardPrescription";
  name: string | null;
  dispenseCount: number | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_procedures {
  __typename: "TrackboardProcedure";
  date: any | null;
  types: string[] | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_anthropometry {
  __typename: "TrackboardAnthropometry";
  height: string | null;
  heightUnit: string | null;
  weight: string | null;
  weightUnit: string | null;
  bmi: string | null;
  bsa: string | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_bloodPressure {
  __typename: "TrackboardBloodPressure";
  systolic: string | null;
  diastolic: string | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_bloodGlucose {
  __typename: "TrackboardBloodGlucose";
  reading: string | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_pulseRate {
  __typename: "TrackboardPulseRate";
  reading: string | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_respiratoryRate {
  __typename: "TrackboardRespiratoryRate";
  reading: string | null;
  spO2: string | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_temperature {
  __typename: "TrackboardTemperature";
  reading: string | null;
  readingUnit: string | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_pain {
  __typename: "TrackboardPain";
  score: string | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list_vitalDetails {
  __typename: "TrackboardVitalDetails";
  anthropometry: FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_anthropometry[] | null;
  bloodPressure: FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_bloodPressure[] | null;
  bloodGlucose: FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_bloodGlucose[] | null;
  pulseRate: FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_pulseRate[] | null;
  respiratoryRate: FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_respiratoryRate[] | null;
  temperature: FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_temperature[] | null;
  pain: FetchTrackBoardData_fetchTrackboardData_list_vitalDetails_pain[] | null;
}

export interface FetchTrackBoardData_fetchTrackboardData_list {
  __typename: "Trackboard";
  admissionWard: string | null;
  roomNumber: string | null;
  severness: string | null;
  priority: string | null;
  daysAdmitted: string | null;
  admissionStatus: string | null;
  admissionDiagnosis: FetchTrackBoardData_fetchTrackboardData_list_admissionDiagnosis[] | null;
  admittedBy: string | null;
  admittingDoctor: string | null;
  admissionDateTime: any | null;
  dischargeDateTime: any | null;
  dischargedBy: string | null;
  painScore: string | null;
  dischargeStatus: string | null;
  allergies: string[] | null;
  patientDetails: FetchTrackBoardData_fetchTrackboardData_list_patientDetails | null;
  labTests: FetchTrackBoardData_fetchTrackboardData_list_labTests[] | null;
  radExam: FetchTrackBoardData_fetchTrackboardData_list_radExam[] | null;
  waitingInfo: FetchTrackBoardData_fetchTrackboardData_list_waitingInfo[] | null;
  dependents: FetchTrackBoardData_fetchTrackboardData_list_dependents[] | null;
  bloodTransfusion: FetchTrackBoardData_fetchTrackboardData_list_bloodTransfusion[] | null;
  careTeam: FetchTrackBoardData_fetchTrackboardData_list_careTeam[] | null;
  prescriptions: FetchTrackBoardData_fetchTrackboardData_list_prescriptions[] | null;
  procedures: FetchTrackBoardData_fetchTrackboardData_list_procedures[] | null;
  vitalDetails: FetchTrackBoardData_fetchTrackboardData_list_vitalDetails[] | null;
}

export interface FetchTrackBoardData_fetchTrackboardData {
  __typename: "TrackboardResponse";
  totalCount: number | null;
  list: FetchTrackBoardData_fetchTrackboardData_list[] | null;
}

export interface FetchTrackBoardData {
  fetchTrackboardData: FetchTrackBoardData_fetchTrackboardData;
}

export interface FetchTrackBoardDataVariables {
  filterOptions: TrackboardFilter;
}
