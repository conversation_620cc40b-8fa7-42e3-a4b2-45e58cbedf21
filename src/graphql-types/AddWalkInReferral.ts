/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NewWalkInReferralInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddWalkInReferral
// ====================================================

export interface AddWalkInReferral_addWalkInReferral_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface AddWalkInReferral_addWalkInReferral {
  __typename: "WalkInReferralModel";
  id: string;
  referralDateTime: any | null;
  referredBy: string | null;
  referralReason: string | null;
  concealReferralReason: boolean | null;
  referralFacilityName: string | null;
  referralFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: AddWalkInReferral_addWalkInReferral_patientInformation | null;
}

export interface AddWalkInReferral {
  addWalkInReferral: AddWalkInReferral_addWalkInReferral;
}

export interface AddWalkInReferralVariables {
  input: NewWalkInReferralInput;
}
