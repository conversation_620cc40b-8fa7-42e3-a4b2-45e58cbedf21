/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetAnthropometryInfos
// ====================================================

export interface GetAnthropometryInfos_getAnthropometryInfos {
  __typename: "AnthropometryModel";
  id: string;
  bmi: string | null;
  bsa: string | null;
  readingDateTime: any | null;
  height: string | null;
  heightUnit: string | null;
  weight: string | null;
  weightUnit: string | null;
  hipCircumference: string | null;
  hipCircumferenceUnit: string | null;
  waistCircumference: string | null;
  waistCircumferenceUnit: string | null;
  skinfoldThickness: string | null;
  skinfoldThicknessUnit: string | null;
  leftUpperLimbCircumference: string | null;
  rightUpperLimbCircumference: string | null;
  upperLimbCircumferenceUnit: string | null;
  leftLowerLimbCircumference: string | null;
  rightLowerLimbCircumference: string | null;
  lowerLimbCircumferenceUnit: string | null;
  leftThighCircumference: string | null;
  rightThighCircumference: string | null;
  thighCircumferenceUnit: string | null;
  abdominalGirth: string | null;
  abdominalGirthUnit: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isAbdominalGirthCritical: boolean | null;
  isHeightCritical: boolean | null;
  isWeightCritical: boolean | null;
  isSkinfoldThicknessCritical: boolean | null;
  isWaistCircumferenceCritical: boolean | null;
  isHipCircumferenceCritical: boolean | null;
}

export interface GetAnthropometryInfos {
  getAnthropometryInfos: GetAnthropometryInfos_getAnthropometryInfos[];
}

export interface GetAnthropometryInfosVariables {
  parentRecordId: string;
  clinifyId: string;
}
