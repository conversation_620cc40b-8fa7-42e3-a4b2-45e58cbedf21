/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: ArchiveWalkInTransferSubs
// ====================================================

export interface ArchiveWalkInTransferSubs_WalkInTransferArchived_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface ArchiveWalkInTransferSubs_WalkInTransferArchived {
  __typename: "WalkInTransferModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  transferFacilityName: string | null;
  transferFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: ArchiveWalkInTransferSubs_WalkInTransferArchived_patientInformation | null;
}

export interface ArchiveWalkInTransferSubs {
  WalkInTransferArchived: ArchiveWalkInTransferSubs_WalkInTransferArchived[];
}

export interface ArchiveWalkInTransferSubsVariables {
  hospitalId: string;
}
