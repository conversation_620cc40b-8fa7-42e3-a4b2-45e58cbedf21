/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PrivateDoctorsFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: getPrivateDoctors
// ====================================================

export interface getPrivateDoctors_getAllPrivateDoctors_list {
  __typename: "ProfileModel";
  id: string;
  createdDate: any | null;
  fullName: string;
  averageRating: number;
}

export interface getPrivateDoctors_getAllPrivateDoctors {
  __typename: "ProfilesResponse";
  totalCount: number;
  list: getPrivateDoctors_getAllPrivateDoctors_list[];
}

export interface getPrivateDoctors {
  getAllPrivateDoctors: getPrivateDoctors_getAllPrivateDoctors;
}

export interface getPrivateDoctorsVariables {
  filterOptions?: PrivateDoctorsFilterInput | null;
}
