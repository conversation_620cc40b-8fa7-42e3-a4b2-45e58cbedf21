/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL fragment: EmployeeDependentFields
// ====================================================

export interface EmployeeDependentFields_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  memberNumber: string | null;
  primaryProviderId: string | null;
  primaryProviderAddress: string | null;
  primaryProviderName: string | null;
}

export interface EmployeeDependentFields_profile_details {
  __typename: "ProfileDetailsModel";
  id: string;
  dateOfBirth: any | null;
}

export interface EmployeeDependentFields_profile_user {
  __typename: "UserModel";
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
}

export interface EmployeeDependentFields_profile {
  __typename: "ProfileModel";
  id: string;
  gender: Gender | null;
  title: string | null;
  details: EmployeeDependentFields_profile_details | null;
  user: EmployeeDependentFields_profile_user;
}

export interface EmployeeDependentFields {
  __typename: "EmployeeDependantModel";
  id: string;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  relationship: string | null;
  title: string | null;
  employeeId: string;
  lastModifierName: string | null;
  creatorName: string | null;
  createdDate: any;
  updatedDate: any;
  displayPictureUrl: string | null;
  hmoProfile: EmployeeDependentFields_hmoProfile | null;
  profile: EmployeeDependentFields_profile | null;
}
