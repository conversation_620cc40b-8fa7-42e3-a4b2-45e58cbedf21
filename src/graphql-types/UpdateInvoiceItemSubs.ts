/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UpdateInvoiceItemSubs
// ====================================================

export interface UpdateInvoiceItemSubs_InvoiceItemUpdated_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
}

export interface UpdateInvoiceItemSubs_InvoiceItemUpdated_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
}

export interface UpdateInvoiceItemSubs_InvoiceItemUpdated {
  __typename: "InvoiceItemModel";
  id: string;
  description: string;
  updatedDate: any;
  createdDate: any;
  invoiceType: string | null;
  discountAmount: number | null;
  discountPercentage: number | null;
  invoiceId: string;
  quantity: number;
  unitPrice: number | null;
  createdBy: UpdateInvoiceItemSubs_InvoiceItemUpdated_createdBy;
  updatedBy: UpdateInvoiceItemSubs_InvoiceItemUpdated_updatedBy | null;
  creatorName: string;
  lastModifierName: string | null;
}

export interface UpdateInvoiceItemSubs {
  InvoiceItemUpdated: UpdateInvoiceItemSubs_InvoiceItemUpdated;
}

export interface UpdateInvoiceItemSubsVariables {
  hospitalId: string;
  profileId: string;
}
