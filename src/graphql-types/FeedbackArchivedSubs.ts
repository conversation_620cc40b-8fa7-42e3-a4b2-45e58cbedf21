/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: FeedbackArchivedSubs
// ====================================================

export interface FeedbackArchivedSubs_FeedbackArchived_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface FeedbackArchivedSubs_FeedbackArchived_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface FeedbackArchivedSubs_FeedbackArchived {
  __typename: "FeedbackModel";
  id: string;
  feedbackDateTime: any | null;
  title: string;
  message: string;
  comment: string | null;
  category: string;
  isResolved: boolean | null;
  documentUrl: string[] | null;
  createdDate: any;
  creatorName: string | null;
  reviewerName: string | null;
  hospital: FeedbackArchivedSubs_FeedbackArchived_hospital | null;
  createdBy: FeedbackArchivedSubs_FeedbackArchived_createdBy;
}

export interface FeedbackArchivedSubs {
  FeedbackArchived: FeedbackArchivedSubs_FeedbackArchived;
}

export interface FeedbackArchivedSubsVariables {
  hospitalId: string;
  hmoProviderId?: string | null;
}
