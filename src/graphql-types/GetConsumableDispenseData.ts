/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetConsumableDispenseData
// ====================================================

export interface GetConsumableDispenseData_getConsumableDispenseData_servicesSummary {
  __typename: "ServicesSummary";
  totalAmount: number | null;
  totalConsumablesDispensed: number | null;
  name: number | null;
}

export interface GetConsumableDispenseData_getConsumableDispenseData_list_consumables_male {
  __typename: "DetailNameQuantity";
  name: string;
  quantity: number | null;
  totalAmount: number | null;
}

export interface GetConsumableDispenseData_getConsumableDispenseData_list_consumables_female {
  __typename: "DetailNameQuantity";
  name: string;
  quantity: number | null;
  totalAmount: number | null;
}

export interface GetConsumableDispenseData_getConsumableDispenseData_list_consumables {
  __typename: "ListsByGenderWithQuantity";
  male: GetConsumableDispenseData_getConsumableDispenseData_list_consumables_male[] | null;
  female: GetConsumableDispenseData_getConsumableDispenseData_list_consumables_female[] | null;
}

export interface GetConsumableDispenseData_getConsumableDispenseData_list_byConsumableDispensedDateAndDispenserName {
  __typename: "ListByMedicationDispensedDateAndDispenserName";
  dispenseDate: string | null;
  dispensedBy: string | null;
  quantity: number | null;
  medicationNames: string[] | null;
}

export interface GetConsumableDispenseData_getConsumableDispenseData_list_byMost {
  __typename: "ListsByOrderByName";
  name: string | null;
  total: number | null;
}

export interface GetConsumableDispenseData_getConsumableDispenseData_list_byLeast {
  __typename: "ListsByOrderByName";
  name: string | null;
  total: number | null;
}

export interface GetConsumableDispenseData_getConsumableDispenseData_list_byPatientName {
  __typename: "ListByPatientName";
  patientName: string | null;
  quantity: string | null;
  name: string;
  dispenseDate: string | null;
  dispensedBy: string | null;
  clinifyId: string | null;
  paymentType: string | null;
  totalAmount: number | null;
}

export interface GetConsumableDispenseData_getConsumableDispenseData_list {
  __typename: "ServiceSummaryList";
  consumables: GetConsumableDispenseData_getConsumableDispenseData_list_consumables | null;
  byConsumableDispensedDateAndDispenserName: GetConsumableDispenseData_getConsumableDispenseData_list_byConsumableDispensedDateAndDispenserName[] | null;
  byMost: GetConsumableDispenseData_getConsumableDispenseData_list_byMost[] | null;
  byLeast: GetConsumableDispenseData_getConsumableDispenseData_list_byLeast[] | null;
  byPatientName: GetConsumableDispenseData_getConsumableDispenseData_list_byPatientName[] | null;
}

export interface GetConsumableDispenseData_getConsumableDispenseData {
  __typename: "ServiceSummaryWithList";
  servicesSummary: GetConsumableDispenseData_getConsumableDispenseData_servicesSummary[];
  list: GetConsumableDispenseData_getConsumableDispenseData_list;
}

export interface GetConsumableDispenseData {
  getConsumableDispenseData: GetConsumableDispenseData_getConsumableDispenseData;
}

export interface GetConsumableDispenseDataVariables {
  filter?: ServicesAnalyticsFilter | null;
}
