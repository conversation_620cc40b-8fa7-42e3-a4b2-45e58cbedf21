/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmoUtilizationFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetTopHmoUtilizations
// ====================================================

export interface GetTopHmoUtilizations_getTopHmoUtilizations_types {
  __typename: "HmoUtilizationTypeSummary";
  type: string;
  count: number;
}

export interface GetTopHmoUtilizations_getTopHmoUtilizations {
  __typename: "HmoUtilizationSummary";
  category: string | null;
  totalAmount: number | null;
  count: number | null;
  types: GetTopHmoUtilizations_getTopHmoUtilizations_types[] | null;
}

export interface GetTopHmoUtilizations {
  getTopHmoUtilizations: GetTopHmoUtilizations_getTopHmoUtilizations[];
}

export interface GetTopHmoUtilizationsVariables {
  filter: HmoUtilizationFilter;
}
