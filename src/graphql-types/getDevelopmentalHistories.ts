/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ProfileInfosFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: getDevelopmentalHistories
// ====================================================

export interface getDevelopmentalHistories_profile_developmentalHistories_list_milestones {
  __typename: "DevelopmentalHistoryMilestoneModel";
  id: string;
  milestone: string;
  ageRange: string;
  whenMet: string | null;
}

export interface getDevelopmentalHistories_profile_developmentalHistories_list {
  __typename: "DevelopmentalHistoryModel";
  id: string;
  groupName: string;
  milestones: getDevelopmentalHistories_profile_developmentalHistories_list_milestones[] | null;
}

export interface getDevelopmentalHistories_profile_developmentalHistories {
  __typename: "DevelopmentalHistoriesResponse";
  totalCount: number;
  list: getDevelopmentalHistories_profile_developmentalHistories_list[];
}

export interface getDevelopmentalHistories_profile {
  __typename: "ProfileModel";
  id: string;
  developmentalHistories: getDevelopmentalHistories_profile_developmentalHistories;
}

export interface getDevelopmentalHistories {
  profile: getDevelopmentalHistories_profile;
}

export interface getDevelopmentalHistoriesVariables {
  filterInput: ProfileInfosFilterInput;
  id: string;
}
