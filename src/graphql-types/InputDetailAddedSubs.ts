/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: InputDetailAddedSubs
// ====================================================

export interface InputDetailAddedSubs_InputDetailAdded {
  __typename: "InputDetailsModel";
  id: string;
  administrationDateTime: any | null;
  administratorName: string | null;
  inputFluidType: string | null;
  routeOfAdministration: string | null;
  inputQuantity: string | null;
  inputQuantityUnit: string | null;
  duration: string | null;
  admissionId: string | null;
}

export interface InputDetailAddedSubs {
  InputDetailAdded: InputDetailAddedSubs_InputDetailAdded;
}

export interface InputDetailAddedSubsVariables {
  profileId: string;
}
