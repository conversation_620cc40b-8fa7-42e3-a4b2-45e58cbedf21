/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HandoverStaffDeletedSubs
// ====================================================

export interface HandoverStaffDeletedSubs_HandoverStaffRemoved {
  __typename: "HandoverStaffModel";
  id: string;
  handoverNoteId: string;
}

export interface HandoverStaffDeletedSubs {
  HandoverStaffRemoved: HandoverStaffDeletedSubs_HandoverStaffRemoved;
}

export interface HandoverStaffDeletedSubsVariables {
  profileId: string;
  hospitalId?: string | null;
  userType: UserType;
}
