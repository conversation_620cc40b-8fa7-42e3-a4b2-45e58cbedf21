/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ProfileInfosFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPastSurgeries
// ====================================================

export interface GetPastSurgeries_profile_pastSurgeries_list_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface GetPastSurgeries_profile_pastSurgeries_list {
  __typename: "PastSurgeryModel";
  id: string;
  type: string;
  operationDate: any | null;
  additionalNote: string | null;
  profile: GetPastSurgeries_profile_pastSurgeries_list_profile | null;
}

export interface GetPastSurgeries_profile_pastSurgeries {
  __typename: "PastSurgeriesResponse";
  totalCount: number;
  list: GetPastSurgeries_profile_pastSurgeries_list[];
}

export interface GetPastSurgeries_profile {
  __typename: "ProfileModel";
  id: string;
  pastSurgeries: GetPastSurgeries_profile_pastSurgeries;
}

export interface GetPastSurgeries {
  profile: GetPastSurgeries_profile;
}

export interface GetPastSurgeriesVariables {
  filterInput: ProfileInfosFilterInput;
  id: string;
}
