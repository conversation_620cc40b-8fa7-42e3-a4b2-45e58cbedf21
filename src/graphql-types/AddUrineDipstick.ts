/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UrineDipstickVitalFields } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddUrineDipstick
// ====================================================

export interface AddUrineDipstick_addUrineDipstickInfo {
  __typename: "UrineDipstickModel";
  id: string;
  readingDateTime: any | null;
  blood: string | null;
  glucose: string | null;
  ketones: string | null;
  ph: string | null;
  protein: string | null;
  nitrites: string | null;
  leucocyte: string | null;
  urobilinogen: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isPhCritical: boolean | null;
}

export interface AddUrineDipstick {
  addUrineDipstickInfo: AddUrineDipstick_addUrineDipstickInfo;
}

export interface AddUrineDipstickVariables {
  input: UrineDipstickVitalFields;
  parentRecordId: string;
  clinifyId: string;
}
