/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetMyEnrollmentAgency
// ====================================================

export interface GetMyEnrollmentAgency_myEnrollmentAgency_tpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
}

export interface GetMyEnrollmentAgency_myEnrollmentAgency {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
  agencyType: string | null;
  isTpa: boolean | null;
  tpaNonTpa: GetMyEnrollmentAgency_myEnrollmentAgency_tpaNonTpa | null;
}

export interface GetMyEnrollmentAgency {
  myEnrollmentAgency: GetMyEnrollmentAgency_myEnrollmentAgency;
}

export interface GetMyEnrollmentAgencyVariables {
  enrollmentAgencyId?: string | null;
}
