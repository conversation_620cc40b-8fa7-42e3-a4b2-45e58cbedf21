/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetAllUtilizationTypes
// ====================================================

export interface GetAllUtilizationTypes_getAllUtilizationTypes {
  __typename: "UtilizationTypeObject";
  /**
   * Substitute for utilization type name
   */
  label: string | null;
  /**
   * Substitute for utilization type id
   */
  value: string | null;
  code: string | null;
  hmoPlanBenefitId: string | null;
  utilizationCategory: string | null;
}

export interface GetAllUtilizationTypes {
  getAllUtilizationTypes: GetAllUtilizationTypes_getAllUtilizationTypes[];
}

export interface GetAllUtilizationTypesVariables {
  providerId: string;
  visitTypeId: string;
  keyword: string;
}
