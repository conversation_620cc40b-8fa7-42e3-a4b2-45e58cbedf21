/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { TreatmentPlanInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddTreatmentPlan
// ====================================================

export interface AddTreatmentPlan_addTreatmentPlan {
  __typename: "TreatmentPlanModel";
  id: string;
  treatmentPlan: string | null;
  conceal: boolean | null;
  patientAdmitted: string | null;
  concealObservationNote: boolean | null;
  observationNote: string | null;
  admissionConsent: string | null;
  adverseEffectsFollowingTreatment: string | null;
  stateEffects: string | null;
  adverseEffectsInvestigated: string | null;
  outcomeOfInvestigation: string | null;
  treatmentGiven: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  treatmentStatus: string | null;
}

export interface AddTreatmentPlan {
  addTreatmentPlan: AddTreatmentPlan_addTreatmentPlan;
}

export interface AddTreatmentPlanVariables {
  input: TreatmentPlanInput;
  id: string;
  clinifyId: string;
  pin?: string | null;
}
