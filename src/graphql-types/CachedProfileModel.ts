/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL fragment: CachedProfileModel
// ====================================================

export interface CachedProfileModel_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  address: string | null;
  dateOfBirth: any | null;
}

export interface CachedProfileModel_user {
  __typename: "UserModel";
  id: string;
  phoneNumber: string | null;
  corporatePhoneNumber: string | null;
}

export interface CachedProfileModel {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  gender: Gender | null;
  personalInformation: CachedProfileModel_personalInformation | null;
  user: CachedProfileModel_user;
}
