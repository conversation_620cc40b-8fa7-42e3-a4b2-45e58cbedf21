/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: NutritionalHistory
// ====================================================

export interface NutritionalHistory {
  __typename: "NutritionalHistoryModel";
  id: string;
  feedingMethod: string | null;
  feedingDifficulties: string | null;
  counseledOnNutrition: string | null;
  receivedNutritionServices: string | null;
  childIsGrowingWell: string | null;
  receivedMicroNutrientPowder: string | null;
  admittedToTreatSevereMalnutrition: string | null;
  outcomeTreatingSevereMalnutrition: string | null;
  dietaryRecallNote: string | null;
  feedingSinceBirthNote: string | null;
  additionalNote: string | null;
}
