/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetCompanyRevenueSummary
// ====================================================

export interface GetCompanyRevenueSummary_getCompanyRevenueDataSummary {
  __typename: "FinanceDataSummaryResponse";
  totalDiscountAmount: number | null;
  totalAmountPaid: number | null;
  totalAmount: number | null;
  totalAmountOutstanding: number | null;
  name: number | null;
  category: string;
  totalAmountDue: number | null;
  totalRevenue: number | null;
}

export interface GetCompanyRevenueSummary {
  getCompanyRevenueDataSummary: GetCompanyRevenueSummary_getCompanyRevenueDataSummary[];
}

export interface GetCompanyRevenueSummaryVariables {
  filter: FinanceAnalyticsFilter;
}
