/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealTransferReason
// ====================================================

export interface ConcealTransferReason_concealTransferReason {
  __typename: "WalkInTransferModel";
  id: string;
  transferReason: string | null;
  concealTransferReason: boolean | null;
}

export interface ConcealTransferReason {
  concealTransferReason: ConcealTransferReason_concealTransferReason;
}

export interface ConcealTransferReasonVariables {
  id: string;
  concealStatus: boolean;
}
