/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateDashboardColourMode
// ====================================================

export interface UpdateDashboardColourMode_updateDashboardColourMode {
  __typename: "FacilityPreferenceModel";
  id: string;
  dashboardColourMode: boolean | null;
  hospitalId: string;
}

export interface UpdateDashboardColourMode {
  updateDashboardColourMode: UpdateDashboardColourMode_updateDashboardColourMode;
}

export interface UpdateDashboardColourModeVariables {
  mode: boolean;
}
