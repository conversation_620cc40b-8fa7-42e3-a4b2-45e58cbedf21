/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: FetchFindingsTemplates
// ====================================================

export interface FetchFindingsTemplates_fetchFindingsTemplates {
  __typename: "FindingsTemplateModel";
  id: string;
  name: string;
  findings: string | null;
  impression: string | null;
}

export interface FetchFindingsTemplates {
  fetchFindingsTemplates: FetchFindingsTemplates_fetchFindingsTemplates[];
}

export interface FetchFindingsTemplatesVariables {
  facilityPreferenceId?: string | null;
}
