/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { OutputDetailsInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateOutputDetail
// ====================================================

export interface UpdateOutputDetail_updateOutputDetail {
  __typename: "OutputDetailsModel";
  id: string;
  administrationDateTime: any | null;
  administratorName: string | null;
  outputFluidType: string | null;
  outputQuantity: string | null;
  outputQuantityUnit: string | null;
  observations: string | null;
  concealObservations: boolean | null;
}

export interface UpdateOutputDetail {
  updateOutputDetail: UpdateOutputDetail_updateOutputDetail;
}

export interface UpdateOutputDetailVariables {
  input: OutputDetailsInput;
  id: string;
  clinifyId: string;
  pin?: string | null;
}
