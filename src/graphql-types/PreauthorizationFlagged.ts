/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PreauthorizationFlagged
// ====================================================

export interface PreauthorizationFlagged_PreauthorizationFlagged_flags {
  __typename: "FlagDto";
  flag: string | null;
  ruleId: string | null;
}

export interface PreauthorizationFlagged_PreauthorizationFlagged {
  __typename: "PreauthorisationModel";
  id: string;
  flags: PreauthorizationFlagged_PreauthorizationFlagged_flags[] | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
}

export interface PreauthorizationFlagged {
  PreauthorizationFlagged: PreauthorizationFlagged_PreauthorizationFlagged;
}

export interface PreauthorizationFlaggedVariables {
  hospitalId: string;
}
