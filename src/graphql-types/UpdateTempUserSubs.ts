/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillStatus, VirtualAccountProvider } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: UpdateTempUserSubs
// ====================================================

export interface UpdateTempUserSubs_TempUserUpdated_virtualBankAccount {
  __typename: "VirtualBankAccountModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bank: VirtualAccountProvider;
}

export interface UpdateTempUserSubs_TempUserUpdated_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface UpdateTempUserSubs_TempUserUpdated {
  __typename: "TempUserModel";
  id: string;
  amountDue: number | null;
  phoneNumber: string;
  amountPaid: number | null;
  paymentStatus: BillStatus;
  hospitalId: string | null;
  virtualBankAccount: UpdateTempUserSubs_TempUserUpdated_virtualBankAccount | null;
  hospital: UpdateTempUserSubs_TempUserUpdated_hospital | null;
}

export interface UpdateTempUserSubs {
  TempUserUpdated: UpdateTempUserSubs_TempUserUpdated;
}

export interface UpdateTempUserSubsVariables {
  phoneNumber: string;
}
