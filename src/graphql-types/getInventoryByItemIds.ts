/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: getInventoryByItemIds
// ====================================================

export interface getInventoryByItemIds_getInventoriesByIds {
  __typename: "InventoryModel";
  id: string;
  quantityRemaining: string | null;
  unitSellingPrice: string | null;
}

export interface getInventoryByItemIds {
  getInventoriesByIds: getInventoryByItemIds_getInventoriesByIds[];
}

export interface getInventoryByItemIdsVariables {
  clinifyId: string;
  ids: string[];
}
