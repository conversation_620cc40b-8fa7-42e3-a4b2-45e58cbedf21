/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: ArchiveAntenatalSubs
// ====================================================

export interface ArchiveAntenatalSubs_AntenatalArchived_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  paymentType: string | null;
  patientType: string | null;
  reference: string | null;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_details_clinicalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_details_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_details_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: ArchiveAntenatalSubs_AntenatalArchived_details_preauthorizationDetails_provider | null;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_details {
  __typename: "AntenatalDetailsModel";
  createdDate: any;
  updatedDate: any;
  lastModifierId: string | null;
  lastModifierName: string | null;
  creatorId: string | null;
  creatorName: string | null;
  id: string;
  priority: string | null;
  category: string | null;
  gravidity: string | null;
  clinicalDiagnosis: ArchiveAntenatalSubs_AntenatalArchived_details_clinicalDiagnosis[] | null;
  parity: string | null;
  visitationDateTime: any | null;
  position: string | null;
  presentation: string | null;
  specifyBreechPresentation: string | null;
  fetalMovement: string | null;
  gestationAge: string | null;
  fetalHeartRate: string | null;
  bloodPressure: string | null;
  weight: string | null;
  weightUnit: string | null;
  symphysioHeight: string | null;
  heightUnit: string | null;
  visitationNote: string | null;
  lie: string | null;
  oedama: string | null;
  specifyOedama: string | null;
  concealVisitationNote: boolean | null;
  treatmentPlan: string | null;
  concealTreatmentPlan: boolean | null;
  grade: string | null;
  seenBy: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  itemId: string | null;
  provider: string | null;
  estimatedDateOfDelivery: any | null;
  lastMenstrualPeriod: any | null;
  counselledFGM: string | null;
  counselledMaternalNutrition: string | null;
  counselledFamilyPlanning: string | null;
  counselledLabourBirthPreparedness: string | null;
  counselledEarlyInitiationBreastFeeding: string | null;
  counselledExclusiveBreastFeeding: string | null;
  counselledPostpartumFamilyPlanning: string | null;
  givenLlin: string | null;
  outcomeOfVisit: string | null;
  ancHivIAndIiScreening: string | null;
  hiv: string | null;
  ancHepatitisBScreening: string | null;
  hbvAntibody: string | null;
  ancHepatitisCScreening: string | null;
  hcvAntibody: string | null;
  ancGenotype: string | null;
  genotype: string | null;
  ancVdrl: string | null;
  vdrl: string | null;
  ancPcvAndHb: string | null;
  invPcv: string | null;
  invPcvRange: string | null;
  invHb: string | null;
  invHbRange: string | null;
  ancRandomBloodSugar: string | null;
  randomBloodSugar: string | null;
  randomBloodSugarUnit: string | null;
  randomBloodSugarRange: string | null;
  ancFastingBloodSugar: string | null;
  fastingBloodSugar: string | null;
  fastingBloodSugarUnit: string | null;
  fastingBloodSugarRange: string | null;
  ancBloodGrouping: string | null;
  bloodGrouping: string | null;
  bloodGroupingRange: string | null;
  rhesusFactor: string | null;
  rhesusFactorRange: string | null;
  ancUrinalysis: string | null;
  colour: string | null;
  appearanceClarity: string | null;
  ph: string | null;
  specificGravity: string | null;
  glucose: string | null;
  ketones: string | null;
  blood: string | null;
  protein: string | null;
  bilirubin: string | null;
  uribilinogen: string | null;
  nitrites: string | null;
  leukocyteEsterase: string | null;
  rbcs: string | null;
  wbcs: string | null;
  epithelialCells: string | null;
  casts: string | null;
  crystals: string | null;
  bacteria: string | null;
  yeast: string | null;
  ipt1: string | null;
  ipt1Ega: string | null;
  ipt2: string | null;
  ipt2Ega: string | null;
  ipt3: string | null;
  ipt3Ega: string | null;
  ipt4: string | null;
  ipt4Ega: string | null;
  hematinics: string | null;
  hematinicsEga: string | null;
  albendazole: string | null;
  albendazoleEga: string | null;
  tetanusToxoid1: string | null;
  tetanusToid1Ega: string | null;
  tetanusToxoid2: string | null;
  tetanusToid2Ega: string | null;
  tetanusToxoid3: string | null;
  tetanusToid3Ega: string | null;
  tetanusToxoid4: string | null;
  tetanusToid4Ega: string | null;
  tetanusToxoid5: string | null;
  tetanusToid5Ega: string | null;
  preauthorizationDetails: ArchiveAntenatalSubs_AntenatalArchived_details_preauthorizationDetails | null;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: ArchiveAntenatalSubs_AntenatalArchived_profile_coverageDetails_provider | null;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: ArchiveAntenatalSubs_AntenatalArchived_profile_coverageDetails[] | null;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface ArchiveAntenatalSubs_AntenatalArchived_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface ArchiveAntenatalSubs_AntenatalArchived {
  __typename: "AntenatalModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  lastModifierId: string | null;
  lastModifierName: string | null;
  creatorId: string | null;
  creatorName: string | null;
  concealAdditionalNote: boolean | null;
  documentUrl: string[] | null;
  hospitalId: string | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  additionalNote: string | null;
  appointmentId: string | null;
  hmoProviderId: string | null;
  serviceDetails: ArchiveAntenatalSubs_AntenatalArchived_serviceDetails[] | null;
  isPackage: boolean;
  details: ArchiveAntenatalSubs_AntenatalArchived_details[] | null;
  profileId: string | null;
  profile: ArchiveAntenatalSubs_AntenatalArchived_profile | null;
  billStatus: string | null;
  medications: ArchiveAntenatalSubs_AntenatalArchived_medications[];
  vitals: ArchiveAntenatalSubs_AntenatalArchived_vitals[];
  investigations: ArchiveAntenatalSubs_AntenatalArchived_investigations[];
  labTests: ArchiveAntenatalSubs_AntenatalArchived_labTests[];
  radiology: ArchiveAntenatalSubs_AntenatalArchived_radiology[];
  nursingServices: ArchiveAntenatalSubs_AntenatalArchived_nursingServices[];
}

export interface ArchiveAntenatalSubs {
  AntenatalArchived: ArchiveAntenatalSubs_AntenatalArchived[];
}

export interface ArchiveAntenatalSubsVariables {
  profileId: string;
  hospitalId: string;
}
