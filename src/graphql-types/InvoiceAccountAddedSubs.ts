/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InvoiceStatus, Currency, VirtualAccountProvider } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: InvoiceAccountAddedSubs
// ====================================================

export interface InvoiceAccountAddedSubs_InvoiceAccountAdded_virtualAccount {
  __typename: "VirtualBankAccountModel";
  id: string;
  currency: Currency;
  updatedDate: any;
  createdDate: any;
  accountName: string;
  accountNumber: string;
  bank: VirtualAccountProvider;
  expiryDate: any | null;
}

export interface InvoiceAccountAddedSubs_InvoiceAccountAdded {
  __typename: "InvoiceModel";
  id: string;
  issueDate: any;
  invoiceStatus: InvoiceStatus;
  virtualAccountId: string | null;
  virtualAccount: InvoiceAccountAddedSubs_InvoiceAccountAdded_virtualAccount | null;
}

export interface InvoiceAccountAddedSubs {
  InvoiceAccountAdded: InvoiceAccountAddedSubs_InvoiceAccountAdded;
}

export interface InvoiceAccountAddedSubsVariables {
  hospitalId: string;
  profileId: string;
}
