/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: AllergyAddedSubs
// ====================================================

export interface AllergyAddedSubs_AllergyAdded_details_clinicalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface AllergyAddedSubs_AllergyAdded_details {
  __typename: "AllergyDetails";
  type: string;
  trigger: string;
  reactions: string[] | null;
  severeness: string | null;
  clinicalDiagnosis: AllergyAddedSubs_AllergyAdded_details_clinicalDiagnosis[] | null;
}

export interface AllergyAddedSubs_AllergyAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface AllergyAddedSubs_AllergyAdded {
  __typename: "AllergyModel";
  id: string;
  occurenceDate: any | null;
  duration: string | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  doctorName: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  details: AllergyAddedSubs_AllergyAdded_details[] | null;
  profileId: string | null;
  profile: AllergyAddedSubs_AllergyAdded_profile | null;
  documentUrl: string[] | null;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
  createdDate: any;
  updatedDate: any;
}

export interface AllergyAddedSubs {
  AllergyAdded: AllergyAddedSubs_AllergyAdded;
}

export interface AllergyAddedSubsVariables {
  profileId: string;
}
