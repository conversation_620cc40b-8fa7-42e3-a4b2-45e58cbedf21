/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetEmployerHmoPlanTypes
// ====================================================

export interface GetEmployerHmoPlanTypes_employerHmoPlanTypes_premiumDetails_enrollmentCommissionRate {
  __typename: "EnrollmentCommissionRateDetails";
  id: string | null;
  name: string | null;
  commissionRate: number | null;
}

export interface GetEmployerHmoPlanTypes_employerHmoPlanTypes_premiumDetails {
  __typename: "PremiumDetails";
  amount: number | null;
  category: string | null;
  frequency: string | null;
  administrationAgency: string | null;
  commissionRate: number | null;
  enrollmentCommissionRate: GetEmployerHmoPlanTypes_employerHmoPlanTypes_premiumDetails_enrollmentCommissionRate[] | null;
}

export interface GetEmployerHmoPlanTypes_employerHmoPlanTypes {
  __typename: "HmoPlanTypeModel";
  id: string;
  name: string | null;
  premiumDetails: GetEmployerHmoPlanTypes_employerHmoPlanTypes_premiumDetails[] | null;
}

export interface GetEmployerHmoPlanTypes {
  employerHmoPlanTypes: GetEmployerHmoPlanTypes_employerHmoPlanTypes[];
}

export interface GetEmployerHmoPlanTypesVariables {
  employerId: string;
}
