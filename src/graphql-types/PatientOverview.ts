/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL query operation: PatientOverview
// ====================================================

export interface PatientOverview_userOverView_lastRadiology_examinationType {
  __typename: "InvestigationExamTypeInputType";
  examType: string;
}

export interface PatientOverview_userOverView_lastRadiology {
  __typename: "InvestigationModel";
  requestDate: any | null;
  orderedBy: string | null;
  examinationType: PatientOverview_userOverView_lastRadiology_examinationType[] | null;
}

export interface PatientOverview_userOverView_lastLabTest_testInfo {
  __typename: "InvestigationTestInfoInputType";
  testName: string;
}

export interface PatientOverview_userOverView_lastLabTest {
  __typename: "InvestigationModel";
  requestDate: any | null;
  orderedBy: string | null;
  testInfo: PatientOverview_userOverView_lastLabTest_testInfo[] | null;
}

export interface PatientOverview_userOverView_dependent {
  __typename: "Dependent";
  firstName: string | null;
  lastName: string | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  gender: Gender | null;
  relationship: string | null;
}

export interface PatientOverview_userOverView_coverage_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface PatientOverview_userOverView_coverage {
  __typename: "HmoProfileModel";
  provider: PatientOverview_userOverView_coverage_provider;
  memberNumber: string | null;
  memberPlan: string | null;
  memberStatus: string | null;
  companyName: string | null;
  companyAddress: string | null;
}

export interface PatientOverview_userOverView_nextOfKin_phoneNumber {
  __typename: "PhoneNumberFields";
  countryName: string | null;
  countryCode: string | null;
  value: string | null;
}

export interface PatientOverview_userOverView_nextOfKin {
  __typename: "NextOfKinModel";
  firstName: string | null;
  lastName: string | null;
  gender: Gender | null;
  bloodGroup: string | null;
  relationship: string | null;
  occupation: string | null;
  phoneNumber: PatientOverview_userOverView_nextOfKin_phoneNumber | null;
  email: string | null;
  address: string | null;
}

export interface PatientOverview_userOverView_personalInformation {
  __typename: "PersonalInformation";
  title: string | null;
  displayPictureUrl: string | null;
  firstName: string | null;
  lastName: string | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  genoType: string | null;
  weight: string | null;
  weightUnit: string | null;
  height: string | null;
  heightUnit: string | null;
  address: string | null;
}

export interface PatientOverview_userOverView_lastAdmission_admissionDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface PatientOverview_userOverView_lastAdmission {
  __typename: "AdmissionModel";
  admissionDate: any | null;
  admittedBy: string | null;
  finding: string | null;
  hospitalUnit: string | null;
  admissionDiagnosis: PatientOverview_userOverView_lastAdmission_admissionDiagnosis[] | null;
}

export interface PatientOverview_userOverView_allergies_details {
  __typename: "AllergyDetails";
  type: string;
  trigger: string;
  reactions: string[] | null;
  severeness: string | null;
}

export interface PatientOverview_userOverView_allergies {
  __typename: "AllergyModel";
  occurenceDate: any | null;
  duration: string | null;
  details: PatientOverview_userOverView_allergies_details[] | null;
  documentUrl: string[] | null;
  additionalNote: string | null;
}

export interface PatientOverview_userOverView_lastImmunization_details {
  __typename: "ImmunizationDetailModel";
  administeredDate: any | null;
  immunizationName: string;
  administratorName: string | null;
  method: string | null;
}

export interface PatientOverview_userOverView_lastImmunization {
  __typename: "ImmunizationModel";
  createdDate: any;
  details: PatientOverview_userOverView_lastImmunization_details[] | null;
}

export interface PatientOverview_userOverView_lastConsultation_provisionalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface PatientOverview_userOverView_lastConsultation_finalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface PatientOverview_userOverView_lastConsultation {
  __typename: "ConsultationModel";
  consultationDateTime: any | null;
  doctorName: string;
  provisionalDiagnosis: PatientOverview_userOverView_lastConsultation_provisionalDiagnosis[] | null;
  finalDiagnosis: PatientOverview_userOverView_lastConsultation_finalDiagnosis[] | null;
}

export interface PatientOverview_userOverView_lastProcedure_procedureType {
  __typename: "ProcedureTypeInputType";
  type: string;
}

export interface PatientOverview_userOverView_lastProcedure {
  __typename: "SurgeryModel";
  surgeryDate: any | null;
  procedureType: PatientOverview_userOverView_lastProcedure_procedureType[];
  specialty: string | null;
  operatedBy: string | null;
}

export interface PatientOverview_userOverView_lastAppointment {
  __typename: "AppointmentModel";
  facilityName: string | null;
  specialty: string | null;
  appointmentDateTime: any;
  doctorsName: string | null;
}

export interface PatientOverview_userOverView_currentMedication_details {
  __typename: "MedicationDetailsModel";
  medicationName: string | null;
  datePrescribed: any | null;
  purpose: string | null;
}

export interface PatientOverview_userOverView_currentMedication {
  __typename: "MedicationModel";
  prescribedBy: string | null;
  details: PatientOverview_userOverView_currentMedication_details[] | null;
}

export interface PatientOverview_userOverView_lastAntenatal_details {
  __typename: "AntenatalDetailsModel";
  gestationAge: string | null;
  bloodPressure: string | null;
  weight: string | null;
}

export interface PatientOverview_userOverView_lastAntenatal {
  __typename: "AntenatalModel";
  createdDate: any;
  details: PatientOverview_userOverView_lastAntenatal_details[] | null;
}

export interface PatientOverview_userOverView_lastVitalSign_anthropometry {
  __typename: "AnthropometryModel";
  height: string | null;
  heightUnit: string | null;
  weight: string | null;
  weightUnit: string | null;
}

export interface PatientOverview_userOverView_lastVitalSign_bloodGlucose {
  __typename: "BloodGlucoseModel";
  reading: string | null;
  readingUnit: string | null;
}

export interface PatientOverview_userOverView_lastVitalSign_bloodPressure {
  __typename: "BloodPressureModel";
  diastolic: string | null;
  systolic: string | null;
}

export interface PatientOverview_userOverView_lastVitalSign_pulseRate {
  __typename: "PulseRateModel";
  reading: string | null;
}

export interface PatientOverview_userOverView_lastVitalSign_respiratoryRate {
  __typename: "RespiratoryRateModel";
  reading: string | null;
}

export interface PatientOverview_userOverView_lastVitalSign_temperature {
  __typename: "TemperatureModel";
  reading: string | null;
  readingUnit: string | null;
}

export interface PatientOverview_userOverView_lastVitalSign {
  __typename: "VitalModel";
  id: string | null;
  createdDate: any;
  anthropometry: PatientOverview_userOverView_lastVitalSign_anthropometry[] | null;
  bloodGlucose: PatientOverview_userOverView_lastVitalSign_bloodGlucose[] | null;
  bloodPressure: PatientOverview_userOverView_lastVitalSign_bloodPressure[] | null;
  pulseRate: PatientOverview_userOverView_lastVitalSign_pulseRate[] | null;
  respiratoryRate: PatientOverview_userOverView_lastVitalSign_respiratoryRate[] | null;
  temperature: PatientOverview_userOverView_lastVitalSign_temperature[] | null;
}

export interface PatientOverview_userOverView {
  __typename: "overviewResponse";
  completion: string | null;
  primaryPhoneNumber: string | null;
  primaryEmail: string | null;
  gender: string | null;
  lastRadiology: PatientOverview_userOverView_lastRadiology | null;
  lastLabTest: PatientOverview_userOverView_lastLabTest | null;
  dependent: PatientOverview_userOverView_dependent | null;
  coverage: PatientOverview_userOverView_coverage | null;
  nextOfKin: PatientOverview_userOverView_nextOfKin | null;
  personalInformation: PatientOverview_userOverView_personalInformation | null;
  lastAdmission: PatientOverview_userOverView_lastAdmission | null;
  allergies: PatientOverview_userOverView_allergies[] | null;
  lastImmunization: PatientOverview_userOverView_lastImmunization | null;
  lastConsultation: PatientOverview_userOverView_lastConsultation | null;
  lastProcedure: PatientOverview_userOverView_lastProcedure | null;
  lastAppointment: PatientOverview_userOverView_lastAppointment | null;
  currentMedication: PatientOverview_userOverView_currentMedication | null;
  lastAntenatal: PatientOverview_userOverView_lastAntenatal | null;
  lastVitalSign: PatientOverview_userOverView_lastVitalSign | null;
}

export interface PatientOverview {
  userOverView: PatientOverview_userOverView;
}

export interface PatientOverviewVariables {
  id?: string | null;
}
