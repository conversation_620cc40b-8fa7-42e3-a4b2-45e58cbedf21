/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdateOrganisationAppointmentTimeInput, NextAppointmentEntities } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateAppointmentTime
// ====================================================

export interface UpdateAppointmentTime_updateAppointmentTime_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface UpdateAppointmentTime_updateAppointmentTime_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface UpdateAppointmentTime_updateAppointmentTime_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface UpdateAppointmentTime_updateAppointmentTime_specialist {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  type: string;
  fullName: string;
}

export interface UpdateAppointmentTime_updateAppointmentTime_serviceDetails {
  __typename: "ServiceDetailInputType";
  type: string | null;
  name: string | null;
}

export interface UpdateAppointmentTime_updateAppointmentTime {
  __typename: "OrganisationAppointmentModel";
  id: string;
  hospital: UpdateAppointmentTime_updateAppointmentTime_hospital | null;
  profile: UpdateAppointmentTime_updateAppointmentTime_profile | null;
  patientInformation: UpdateAppointmentTime_updateAppointmentTime_patientInformation | null;
  specialist: UpdateAppointmentTime_updateAppointmentTime_specialist | null;
  createdDate: any;
  updatedDate: any;
  status: string | null;
  category: string | null;
  liveSessionUrl: string | null;
  rank: string | null;
  reason: string | null;
  appointmentDateTime: any | null;
  startDateTime: any | null;
  endDateTime: any | null;
  specialty: string | null;
  role: string | null;
  paymentType: string | null;
  serviceDetails: UpdateAppointmentTime_updateAppointmentTime_serviceDetails[] | null;
  patientType: string | null;
  duration: string | null;
  confirmedBy: string | null;
  deliveryMethod: string | null;
  urgency: string | null;
  additionalNote: string | null;
  recordType: NextAppointmentEntities | null;
  documentUrl: string[] | null;
}

export interface UpdateAppointmentTime {
  updateAppointmentTime: UpdateAppointmentTime_updateAppointmentTime;
}

export interface UpdateAppointmentTimeVariables {
  id: string;
  input: UpdateOrganisationAppointmentTimeInput;
}
