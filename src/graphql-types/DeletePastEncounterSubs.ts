/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: DeletePastEncounterSubs
// ====================================================

export interface DeletePastEncounterSubs_PastEncounterRemoved {
  __typename: "PastEncounterModel";
  id: string;
}

export interface DeletePastEncounterSubs {
  PastEncounterRemoved: DeletePastEncounterSubs_PastEncounterRemoved;
}

export interface DeletePastEncounterSubsVariables {
  profileId: string;
}
