/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: OncologyHistoryUpdatedSubs
// ====================================================

export interface OncologyHistoryUpdatedSubs_OncologyUpdated_tumorDetails {
  __typename: "TumorDetailsInputType";
  size: string | null;
  laterality: string | null;
}

export interface OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister_treatmentChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister_treatmentChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister_treatmentChart_periods_cycles[];
}

export interface OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister_treatmentChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister_treatmentChart_periods[];
}

export interface OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister_therapyChart_periods_cycles {
  __typename: "OncologyCycle";
  cycleNo: number;
  value: any | null;
}

export interface OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister_therapyChart_periods {
  __typename: "OncologyPeriod";
  day: number;
  cycles: OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister_therapyChart_periods_cycles[];
}

export interface OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister_therapyChart {
  __typename: "OncologyChart";
  noOfCycles: number;
  periods: OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister_therapyChart_periods[];
}

export interface OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister {
  __typename: "OncologyRegisterModel";
  id: string;
  creatorName: string | null;
  lastModifierName: string | null;
  treatmentChart: OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister_treatmentChart | null;
  therapyChart: OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister_therapyChart | null;
  createdDate: any;
  updatedDate: any;
}

export interface OncologyHistoryUpdatedSubs_OncologyUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface OncologyHistoryUpdatedSubs_OncologyUpdated {
  __typename: "OncologyHistoryModel";
  id: string;
  initialDiagnosisICD10: string | null;
  initialDiagnosisICD11: string | null;
  initialDiagnosisSNOMED: string | null;
  finalDiagnosisICD10: string | null;
  finalDiagnosisICD11: string | null;
  finalDiagnosisSNOMED: string | null;
  diagnosisDateTime: any | null;
  diagnosedBy: string | null;
  additionalNote: string | null;
  stageDiagnosisICD10: string | null;
  stageDiagnosisICD11: string | null;
  stageDiagnosisSNOMED: string | null;
  stageDiagnosisDateTime: any | null;
  classification: string | null;
  stage: string | null;
  stageDate: any | null;
  histopathologicType: string | null;
  stageTiming: string | null;
  primaryTumor: string | null;
  residualTumor: string | null;
  tumorDetails: OncologyHistoryUpdatedSubs_OncologyUpdated_tumorDetails[] | null;
  lymphovascularInvasion: string | null;
  regionalLymphNodes: string | null;
  numberOfNodes: string | null;
  distantMetastasis: string | null;
  grade: string | null;
  stageStatus: string | null;
  cancerType: string | null;
  progression: string | null;
  relapse: string | null;
  remission: string | null;
  stageTreatmentType: string | null;
  stageAdditionalNote: string | null;
  treatmentType: string | null;
  treatmentSite: string | null;
  intentOfTreatment: string | null;
  lineOfTreatment: string | null;
  concurrentTreatment: string | null;
  treatmentPlanProvider: string | null;
  treatmentDepartment: string | null;
  treatmentStatus: string | null;
  treatmentPriority: string | null;
  treatmentInterval: string | null;
  treatmentStartDate: any | null;
  treatmentEndDate: any | null;
  treatmentCycleDays: string | null;
  treatmentCycleNumber: string | null;
  treatmentPatientType: string | null;
  treatmentAdverseReaction: string | null;
  treatmentSpecificReaction: string | null;
  treatmentOutcome: string | null;
  treatmentResponse: string | null;
  treatmentFollowupDate: any | null;
  treatmentAdditionalNote: string | null;
  therapyType: string | null;
  therapySite: string | null;
  intentOfTherapy: string | null;
  lineOfTherapy: string | null;
  concurrentTherapy: string | null;
  therapyPlanProvider: string | null;
  therapyDepartment: string | null;
  therapyStatus: string | null;
  therapyPriority: string | null;
  therapyInterval: string | null;
  therapyStartDate: any | null;
  therapyEndDate: any | null;
  therapyCycleDays: string | null;
  therapyCycleNumber: string | null;
  therapyPatientType: string | null;
  therapyAdverseReaction: string | null;
  therapySpecificReaction: string | null;
  therapyOutcome: string | null;
  therapyResponse: string | null;
  therapyFollowupDate: any | null;
  therapyAdditionalNote: string | null;
  oncologyRegister: OncologyHistoryUpdatedSubs_OncologyUpdated_oncologyRegister | null;
  profile: OncologyHistoryUpdatedSubs_OncologyUpdated_profile | null;
}

export interface OncologyHistoryUpdatedSubs {
  OncologyUpdated: OncologyHistoryUpdatedSubs_OncologyUpdated;
}

export interface OncologyHistoryUpdatedSubsVariables {
  profileId: string;
}
