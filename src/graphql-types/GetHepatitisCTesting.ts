/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CasesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHepatitisCTesting
// ====================================================

export interface GetHepatitisCTesting_getHepatitisCTesting_ageRanges {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetHepatitisCTesting_getHepatitisCTesting {
  __typename: "CasesSummary";
  name: number | null;
  totalFemale: number | null;
  totalMale: number | null;
  totalPositive: number | null;
  totalNegative: number | null;
  ageRanges: GetHepatitisCTesting_getHepatitisCTesting_ageRanges[] | null;
}

export interface GetHepatitisCTesting {
  getHepatitisCTesting: GetHepatitisCTesting_getHepatitisCTesting[];
}

export interface GetHepatitisCTestingVariables {
  filter?: CasesAnalyticsFilter | null;
}
