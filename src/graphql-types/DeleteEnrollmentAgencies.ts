/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: DeleteEnrollmentAgencies
// ====================================================

export interface DeleteEnrollmentAgencies_deleteEnrollmentAgencies_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface DeleteEnrollmentAgencies_deleteEnrollmentAgencies_tpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
}

export interface DeleteEnrollmentAgencies_deleteEnrollmentAgencies_primaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface DeleteEnrollmentAgencies_deleteEnrollmentAgencies_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface DeleteEnrollmentAgencies_deleteEnrollmentAgencies_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface DeleteEnrollmentAgencies_deleteEnrollmentAgencies_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface DeleteEnrollmentAgencies_deleteEnrollmentAgencies {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
  agencyCode: string | null;
  address: string | null;
  country: string | null;
  state: string | null;
  agencyType: string | null;
  profile: DeleteEnrollmentAgencies_deleteEnrollmentAgencies_profile | null;
  tpaNonTpa: DeleteEnrollmentAgencies_deleteEnrollmentAgencies_tpaNonTpa | null;
  localGovernmentArea: string | null;
  primaryPhoneNumber: DeleteEnrollmentAgencies_deleteEnrollmentAgencies_primaryPhoneNumber | null;
  primaryEmailAddress: string | null;
  secondaryPhoneNumber: DeleteEnrollmentAgencies_deleteEnrollmentAgencies_secondaryPhoneNumber | null;
  secondaryEmailAddress: string | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: DeleteEnrollmentAgencies_deleteEnrollmentAgencies_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: DeleteEnrollmentAgencies_deleteEnrollmentAgencies_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  accountName: string | null;
  accountNumber: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface DeleteEnrollmentAgencies {
  deleteEnrollmentAgencies: DeleteEnrollmentAgencies_deleteEnrollmentAgencies[];
}

export interface DeleteEnrollmentAgenciesVariables {
  ids: string[];
}
