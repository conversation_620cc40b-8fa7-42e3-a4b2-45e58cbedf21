/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VisitationAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetVisitationSummary
// ====================================================

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalVisitations {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalAppointmentsBooked {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalAppointmentsCancelled {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalAppointmentsCompleted {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalAppointmentsMissed {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalRegisteredPatients {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalRegisteredPatientsUnderOneYear {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalEmergency {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalNonEmergency {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalInPatient {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalOutPatient {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalReferral {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
  totalReferralIn: number | null;
  totalReferralOut: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData_totalWaitTimeInMinutes {
  __typename: "VisitationSummaryCountType";
  hospitalName: string | null;
  hospitalId: string | null;
  count: number | null;
}

export interface GetVisitationSummary_getVisitationSummary_groupedData {
  __typename: "GroupedVisitationSummary";
  totalVisitations: GetVisitationSummary_getVisitationSummary_groupedData_totalVisitations[] | null;
  totalAppointmentsBooked: GetVisitationSummary_getVisitationSummary_groupedData_totalAppointmentsBooked[] | null;
  totalAppointmentsCancelled: GetVisitationSummary_getVisitationSummary_groupedData_totalAppointmentsCancelled[] | null;
  totalAppointmentsCompleted: GetVisitationSummary_getVisitationSummary_groupedData_totalAppointmentsCompleted[] | null;
  totalAppointmentsMissed: GetVisitationSummary_getVisitationSummary_groupedData_totalAppointmentsMissed[] | null;
  totalRegisteredPatients: GetVisitationSummary_getVisitationSummary_groupedData_totalRegisteredPatients[] | null;
  totalRegisteredPatientsUnderOneYear: GetVisitationSummary_getVisitationSummary_groupedData_totalRegisteredPatientsUnderOneYear[] | null;
  totalEmergency: GetVisitationSummary_getVisitationSummary_groupedData_totalEmergency[] | null;
  totalNonEmergency: GetVisitationSummary_getVisitationSummary_groupedData_totalNonEmergency[] | null;
  totalInPatient: GetVisitationSummary_getVisitationSummary_groupedData_totalInPatient[] | null;
  totalOutPatient: GetVisitationSummary_getVisitationSummary_groupedData_totalOutPatient[] | null;
  totalReferral: GetVisitationSummary_getVisitationSummary_groupedData_totalReferral[] | null;
  totalWaitTimeInMinutes: GetVisitationSummary_getVisitationSummary_groupedData_totalWaitTimeInMinutes[] | null;
}

export interface GetVisitationSummary_getVisitationSummary {
  __typename: "VisitationSummary";
  name: number | null;
  totalRegisteredPatients: number | null;
  totalRegisteredPatientsUnderOneYear: number | null;
  totalAppointmentsBooked: number | null;
  totalAppointmentsCompleted: number | null;
  totalAppointmentsMissed: number | null;
  totalAppointmentsCancelled: number | null;
  totalVisitations: number | null;
  totalEmergency: number | null;
  totalNonEmergency: number | null;
  totalInPatient: number | null;
  totalOutPatient: number | null;
  totalAdmissions: number | null;
  totalReferralOut: number | null;
  totalReferralIn: number | null;
  totalWaitTimeInMinutes: number | null;
  groupedData: GetVisitationSummary_getVisitationSummary_groupedData | null;
}

export interface GetVisitationSummary {
  getVisitationSummary: GetVisitationSummary_getVisitationSummary;
}

export interface GetVisitationSummaryVariables {
  filter?: VisitationAnalyticsFilter | null;
}
