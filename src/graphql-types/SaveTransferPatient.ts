/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { TransferPatientInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: SaveTransferPatient
// ====================================================

export interface SaveTransferPatient_saveTransferPatient_transferHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  clinifyId: string | null;
}

export interface SaveTransferPatient_saveTransferPatient {
  __typename: "TransferPatientModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  roomOption: string | null;
  transferSource: string | null;
  roomInventoryId: string | null;
  transferHospital: SaveTransferPatient_saveTransferPatient_transferHospital | null;
}

export interface SaveTransferPatient {
  saveTransferPatient: SaveTransferPatient_saveTransferPatient;
}

export interface SaveTransferPatientVariables {
  input: TransferPatientInput;
  id: string;
  clinifyId: string;
  pin?: string | null;
}
