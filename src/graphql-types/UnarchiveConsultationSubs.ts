/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: UnarchiveConsultationSubs
// ====================================================

export interface UnarchiveConsultationSubs_ConsultationUnarchived_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_complaintSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_systemReviewSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_physicalExamSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: UnarchiveConsultationSubs_ConsultationUnarchived_profile_coverageDetails_provider | null;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: UnarchiveConsultationSubs_ConsultationUnarchived_profile_coverageDetails[] | null;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_provisionalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_finalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: UnarchiveConsultationSubs_ConsultationUnarchived_preauthorizationDetails_provider | null;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_allergies {
  __typename: "AllergyModel";
  id: string;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_labTests {
  __typename: "LabResultModel";
  id: string;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_radiology {
  __typename: "RadiologyResultModel";
  id: string;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_investigations {
  __typename: "LabResultModel";
  id: string;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface UnarchiveConsultationSubs_ConsultationUnarchived {
  __typename: "ConsultationModel";
  id: string;
  consultationDateTime: any | null;
  duration: string | null;
  doctorName: string;
  priority: string | null;
  specialty: string | null;
  class: string | null;
  hmoProviderId: string | null;
  serviceDetails: UnarchiveConsultationSubs_ConsultationUnarchived_serviceDetails[] | null;
  isPackage: boolean;
  clinicName: string | null;
  clinicAddress: string | null;
  complaint: string | null;
  provider: string | null;
  providerServiceName: string | null;
  complaintHistory: string | null;
  healthEducation: string | null;
  systemReview: string | null;
  systemReviewSmartText: string | null;
  complaintGender: string | null;
  complaintSmartSelection: UnarchiveConsultationSubs_ConsultationUnarchived_complaintSmartSelection | null;
  systemReviewSmartSelection: UnarchiveConsultationSubs_ConsultationUnarchived_systemReviewSmartSelection | null;
  category: string | null;
  department: string | null;
  physicalExam: string | null;
  physicalExamSmartText: string | null;
  physicalExamSmartSelection: UnarchiveConsultationSubs_ConsultationUnarchived_physicalExamSmartSelection | null;
  referral: boolean | null;
  referralDate: any | null;
  referralReason: string | null;
  concealReferralReason: boolean | null;
  specialtyReferredTo: string | null;
  profileId: string | null;
  profile: UnarchiveConsultationSubs_ConsultationUnarchived_profile | null;
  externalReferral: boolean | null;
  provisionalDiagnosis: UnarchiveConsultationSubs_ConsultationUnarchived_provisionalDiagnosis[] | null;
  finalDiagnosis: UnarchiveConsultationSubs_ConsultationUnarchived_finalDiagnosis[] | null;
  consultationStartDate: any | null;
  consultationEndDate: any | null;
  appointmentId: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  concealComplaint: boolean | null;
  concealComplaintHistory: boolean | null;
  concealPhysicalExam: boolean | null;
  concealSystemReview: boolean | null;
  concealHealthEducation: boolean | null;
  billStatus: string | null;
  audiometry: string | null;
  concealAudiometry: boolean | null;
  hospitalId: string | null;
  preauthorizationDetails: UnarchiveConsultationSubs_ConsultationUnarchived_preauthorizationDetails | null;
  allergies: UnarchiveConsultationSubs_ConsultationUnarchived_allergies[];
  medications: UnarchiveConsultationSubs_ConsultationUnarchived_medications[];
  surgeries: UnarchiveConsultationSubs_ConsultationUnarchived_surgeries[];
  admissions: UnarchiveConsultationSubs_ConsultationUnarchived_admissions[];
  vitals: UnarchiveConsultationSubs_ConsultationUnarchived_vitals[];
  labTests: UnarchiveConsultationSubs_ConsultationUnarchived_labTests[];
  radiology: UnarchiveConsultationSubs_ConsultationUnarchived_radiology[];
  investigations: UnarchiveConsultationSubs_ConsultationUnarchived_investigations[];
  nursingServices: UnarchiveConsultationSubs_ConsultationUnarchived_nursingServices[];
}

export interface UnarchiveConsultationSubs {
  ConsultationUnarchived: UnarchiveConsultationSubs_ConsultationUnarchived[];
}

export interface UnarchiveConsultationSubsVariables {
  profileId: string;
  hospitalId: string;
}
