/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: HmoFieldNameOnly
// ====================================================

export interface HmoFieldNameOnly_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface HmoFieldNameOnly {
  __typename: "HmoProfileModel";
  id: string;
  provider: HmoFieldNameOnly_provider;
  memberNumber: string | null;
  memberUniqueId: string | null;
  memberPlan: string | null;
  memberPlanId: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
}
