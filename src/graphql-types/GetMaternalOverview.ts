/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MaternalHealthAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetMaternalOverview
// ====================================================

export interface GetMaternalOverview_getMaternalOverview {
  __typename: "MaternalHealthSummary";
  name: number | null;
  count: number | null;
  category: string | null;
  hospitalName: string | null;
}

export interface GetMaternalOverview {
  getMaternalOverview: GetMaternalOverview_getMaternalOverview[];
}

export interface GetMaternalOverviewVariables {
  filter?: MaternalHealthAnalyticsFilter | null;
}
