/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: AntenatalLinking
// ====================================================

export interface AntenatalLinking_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface AntenatalLinking_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface AntenatalLinking_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface AntenatalLinking_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface AntenatalLinking_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface AntenatalLinking_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface AntenatalLinking {
  __typename: "AntenatalModel";
  medications: AntenatalLinking_medications[];
  vitals: AntenatalLinking_vitals[];
  investigations: AntenatalLinking_investigations[];
  labTests: AntenatalLinking_labTests[];
  radiology: AntenatalLinking_radiology[];
  nursingServices: AntenatalLinking_nursingServices[];
}
