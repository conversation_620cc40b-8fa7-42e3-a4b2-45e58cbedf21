/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PricesFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPriceProviderList
// ====================================================

export interface GetPriceProviderList_getPriceProviders_list {
  __typename: "PricesModel";
  id: string;
  name: string | null;
  price: string | null;
  serviceType: string | null;
}

export interface GetPriceProviderList_getPriceProviders {
  __typename: "PriceResponse";
  totalCount: number;
  list: GetPriceProviderList_getPriceProviders_list[];
}

export interface GetPriceProviderList {
  getPriceProviders: GetPriceProviderList_getPriceProviders;
}

export interface GetPriceProviderListVariables {
  filterOptions?: PricesFilterInput | null;
}
