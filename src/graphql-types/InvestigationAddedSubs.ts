/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { specimenCollection, PatientType, investigationStatus, BillStatus } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: InvestigationAddedSubs
// ====================================================

export interface InvestigationAddedSubs_InvestigationAdded_testInfo {
  __typename: "InvestigationTestInfoInputType";
  testName: string;
  priority: string | null;
  specimen: string[] | null;
  testCategory: string | null;
  clinicalDiagnosisICD10: string | null;
  clinicalDiagnosisICD11: string | null;
  clinicalDiagnosisSNOMED: string | null;
  loinc: string | null;
  ref: string | null;
  provider: string | null;
  itemId: string | null;
  preauthorizationDetailsId: string | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_examinationType {
  __typename: "InvestigationExamTypeInputType";
  ref: string | null;
  examType: string;
  priority: string | null;
  loinc: string | null;
  provider: string | null;
  itemId: string | null;
  preauthorizationDetailsId: string | null;
  indication: string | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_labResult_details_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_labResult_details_testResults {
  __typename: "TestResultsType";
  name: string | null;
  value: string | null;
  unit: string | null;
  dropdown: string[] | null;
  box: boolean | null;
  sensitivityBox: boolean | null;
  units: string[] | null;
  dependsOn: string[] | null;
  range: string | null;
  extraValue: string | null;
  hasExtraValue: boolean | null;
  valueTwo: string | null;
  tabular: string[] | null;
  referenceRange: boolean | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_labResult_details_extraTestResults {
  __typename: "TestResultsType";
  name: string | null;
  value: string | null;
  unit: string | null;
  dropdown: string[] | null;
  box: boolean | null;
  sensitivityBox: boolean | null;
  units: string[] | null;
  dependsOn: string[] | null;
  range: string | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_labResult_details {
  __typename: "LabTestDetailType";
  testDate: any | null;
  duration: string | null;
  serviceDetails: InvestigationAddedSubs_InvestigationAdded_labResult_details_serviceDetails | null;
  specimenCollected: specimenCollection | null;
  specimenTypes: string[] | null;
  specimenNumber: string | null;
  natureSiteOfSpecimen: string | null;
  specimenReceivedDate: any | null;
  collectionDate: any | null;
  collectedBy: string | null;
  performedBy: string | null;
  performedBySignature: string | null;
  performedBySignatureType: string | null;
  performedBySignatureDateTime: any | null;
  verifiedBy: string | null;
  verifiedBySignature: string | null;
  verifiedBySignatureType: string | null;
  verifiedBySignatureDateTime: any | null;
  resultDate: any | null;
  pathologistName: string | null;
  pathologistSignature: string | null;
  pathologistSignatureType: string | null;
  pathologistSignatureDateTime: any | null;
  pathologistReport: string | null;
  conclusion: string | null;
  microscopicExam: string | null;
  grossExam: string | null;
  pertinentHistory: string | null;
  reportDate: any | null;
  finalReportDate: any | null;
  done: boolean | null;
  testName: string | null;
  additionalNote: string | null;
  testResults: InvestigationAddedSubs_InvestigationAdded_labResult_details_testResults[] | null;
  extraTestResults: InvestigationAddedSubs_InvestigationAdded_labResult_details_extraTestResults[] | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_labResult {
  __typename: "LabResultModel";
  id: string;
  documentUrl: string[] | null;
  details: InvestigationAddedSubs_InvestigationAdded_labResult_details[] | null;
  facilityName: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean;
  facilityAddress: string | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_radiologyResult_details_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_radiologyResult_details {
  __typename: "RadiologyExamDetailType";
  examinationDate: any | null;
  duration: string | null;
  patientType: PatientType | null;
  paymentType: string | null;
  serviceDetails: InvestigationAddedSubs_InvestigationAdded_radiologyResult_details_serviceDetails | null;
  radiographerName: string | null;
  radiographerSignature: string | null;
  radiographerSignatureType: string | null;
  radiographerSignatureDateTime: any | null;
  examinationNumber: string | null;
  indication: string | null;
  comparison: string | null;
  technique: string | null;
  radiographerReport: string | null;
  impression: string | null;
  done: boolean | null;
  examType: string | null;
  contrastConfirmed: boolean | null;
  radiologistName: string | null;
  radiologistSignature: string | null;
  radiologistSignatureType: string | null;
  radiologistSignatureDateTime: any | null;
  verifiedBy: string | null;
  verifiedBySignature: string | null;
  verifiedBySignatureType: string | null;
  verifiedBySignatureDateTime: any | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_radiologyResult {
  __typename: "RadiologyResultModel";
  id: string;
  documentUrl: string[] | null;
  details: InvestigationAddedSubs_InvestigationAdded_radiologyResult_details[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_referringHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: InvestigationAddedSubs_InvestigationAdded_profile_coverageDetails_provider | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
  coverageDetails: InvestigationAddedSubs_InvestigationAdded_profile_coverageDetails[] | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface InvestigationAddedSubs_InvestigationAdded_bill {
  __typename: "BillModel";
  id: string;
  billStatus: BillStatus;
  createdDate: any;
}

export interface InvestigationAddedSubs_InvestigationAdded_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  paCode: string | null;
  paStatus: string | null;
}

export interface InvestigationAddedSubs_InvestigationAdded {
  __typename: "InvestigationModel";
  id: string;
  requestType: string;
  requestDate: any | null;
  priority: string | null;
  orderedBy: string | null;
  clinifyId: string | null;
  testInfo: InvestigationAddedSubs_InvestigationAdded_testInfo[] | null;
  specialty: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  examinationType: InvestigationAddedSubs_InvestigationAdded_examinationType[] | null;
  labResult: InvestigationAddedSubs_InvestigationAdded_labResult | null;
  radiologyResult: InvestigationAddedSubs_InvestigationAdded_radiologyResult | null;
  clinicalHistory: string | null;
  status: investigationStatus | null;
  isRequested: boolean | null;
  external: boolean;
  referringHospital: InvestigationAddedSubs_InvestigationAdded_referringHospital | null;
  documentUrl: string[] | null;
  radiologyContrastConfirmation: boolean | null;
  profile: InvestigationAddedSubs_InvestigationAdded_profile | null;
  hospital: InvestigationAddedSubs_InvestigationAdded_hospital | null;
  bill: InvestigationAddedSubs_InvestigationAdded_bill | null;
  preauthorizationDetails: InvestigationAddedSubs_InvestigationAdded_preauthorizationDetails[] | null;
  billStatus: string | null;
}

export interface InvestigationAddedSubs {
  InvestigationAdded: InvestigationAddedSubs_InvestigationAdded;
}

export interface InvestigationAddedSubsVariables {
  profileId: string;
  hospitalId: string;
}
