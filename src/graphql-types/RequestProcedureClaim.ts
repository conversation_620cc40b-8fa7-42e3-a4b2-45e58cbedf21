/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: RequestProcedureClaim
// ====================================================

export interface RequestProcedureClaim_requestProcedure_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
}

export interface RequestProcedureClaim_requestProcedure {
  __typename: "RequestProcedureModel";
  id: string;
  hmoClaim: RequestProcedureClaim_requestProcedure_hmoClaim | null;
}

export interface RequestProcedureClaim {
  requestProcedure: RequestProcedureClaim_requestProcedure;
}

export interface RequestProcedureClaimVariables {
  id: string;
  clinifyId: string;
}
