/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: FamilyHistoryAddedSubs
// ====================================================

export interface FamilyHistoryAddedSubs_FamilyHistoryAdded_conditions {
  __typename: "ConditionInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
  ageOfOnset: string | null;
}

export interface FamilyHistoryAddedSubs_FamilyHistoryAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface FamilyHistoryAddedSubs_FamilyHistoryAdded {
  __typename: "FamilyHistoryModel";
  id: string;
  title: string | null;
  firstName: string | null;
  lastName: string | null;
  middleName: string | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  relationship: string | null;
  conditions: FamilyHistoryAddedSubs_FamilyHistoryAdded_conditions[];
  gender: Gender | null;
  additionalNote: string | null;
  profile: FamilyHistoryAddedSubs_FamilyHistoryAdded_profile | null;
}

export interface FamilyHistoryAddedSubs {
  FamilyHistoryAdded: FamilyHistoryAddedSubs_FamilyHistoryAdded;
}

export interface FamilyHistoryAddedSubsVariables {
  profileId: string;
}
