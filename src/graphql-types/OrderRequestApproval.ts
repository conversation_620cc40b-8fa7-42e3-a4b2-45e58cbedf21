/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: OrderRequestApproval
// ====================================================

export interface OrderRequestApproval_orderRequestApproval_orderFrom {
  __typename: "HospitalModel";
  name: string | null;
}

export interface OrderRequestApproval_orderRequestApproval_orderTo {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface OrderRequestApproval_orderRequestApproval {
  __typename: "InventoryOrderModel";
  id: string;
  sn: number | null;
  quantityOrdered: string | null;
  addedDateTime: string | null;
  deliveredDateTime: any | null;
  section: string | null;
  supplier: string | null;
  invoiceNumber: string | null;
  name: string | null;
  type: string | null;
  size: string | null;
  bedNumber: string | null;
  ward: string | null;
  group: string | null;
  flag: string | null;
  description: string | null;
  strength: string | null;
  category: string | null;
  code: string | null;
  batchNumber: string | null;
  barcode: string | null;
  bedAvailable: string | null;
  expiryDate: string | null;
  expiryStatus: string | null;
  damagedCount: string | null;
  markup: string | null;
  unitCost: string | null;
  unitSellingPrice: string | null;
  totalCost: string | null;
  totalSale: string | null;
  quantityRemaining: string | null;
  quantityPurchased: string | null;
  quantityAvailable: string | null;
  quantityDispensed: string | null;
  quantitySold: string | null;
  manufacturer: string | null;
  recievedDateTime: string | null;
  reorderLevel: string | null;
  receivedBy: string | null;
  addedBy: string | null;
  purchasedBy: string | null;
  vin: string | null;
  plateNumber: string | null;
  year: string | null;
  model: string | null;
  colour: string | null;
  status: string | null;
  comments: string | null;
  images: string | null;
  creatorId: string | null;
  createdDate: any | null;
  creatorName: string | null;
  approved: boolean | null;
  canceled: boolean | null;
  deliveryStatus: string | null;
  quantityDelivered: string | null;
  deliveredBy: string | null;
  orderFrom: OrderRequestApproval_orderRequestApproval_orderFrom | null;
  orderTo: OrderRequestApproval_orderRequestApproval_orderTo | null;
}

export interface OrderRequestApproval {
  orderRequestApproval: OrderRequestApproval_orderRequestApproval[];
}

export interface OrderRequestApprovalVariables {
  ids: string[];
  status: boolean;
}
