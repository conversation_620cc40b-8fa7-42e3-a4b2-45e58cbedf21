/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AntenatalDetailsInput, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateAntenatalDetail
// ====================================================

export interface UpdateAntenatalDetail_updateAntenatalDetail_clinicalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UpdateAntenatalDetail_updateAntenatalDetail_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UpdateAntenatalDetail_updateAntenatalDetail_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: UpdateAntenatalDetail_updateAntenatalDetail_preauthorizationDetails_provider | null;
}

export interface UpdateAntenatalDetail_updateAntenatalDetail {
  __typename: "AntenatalDetailsModel";
  createdDate: any;
  updatedDate: any;
  lastModifierId: string | null;
  lastModifierName: string | null;
  creatorId: string | null;
  creatorName: string | null;
  id: string;
  priority: string | null;
  category: string | null;
  gravidity: string | null;
  clinicalDiagnosis: UpdateAntenatalDetail_updateAntenatalDetail_clinicalDiagnosis[] | null;
  parity: string | null;
  visitationDateTime: any | null;
  position: string | null;
  presentation: string | null;
  specifyBreechPresentation: string | null;
  fetalMovement: string | null;
  gestationAge: string | null;
  fetalHeartRate: string | null;
  bloodPressure: string | null;
  weight: string | null;
  weightUnit: string | null;
  symphysioHeight: string | null;
  heightUnit: string | null;
  visitationNote: string | null;
  lie: string | null;
  oedama: string | null;
  specifyOedama: string | null;
  concealVisitationNote: boolean | null;
  treatmentPlan: string | null;
  concealTreatmentPlan: boolean | null;
  grade: string | null;
  seenBy: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  itemId: string | null;
  provider: string | null;
  estimatedDateOfDelivery: any | null;
  lastMenstrualPeriod: any | null;
  counselledFGM: string | null;
  counselledMaternalNutrition: string | null;
  counselledFamilyPlanning: string | null;
  counselledLabourBirthPreparedness: string | null;
  counselledEarlyInitiationBreastFeeding: string | null;
  counselledExclusiveBreastFeeding: string | null;
  counselledPostpartumFamilyPlanning: string | null;
  givenLlin: string | null;
  outcomeOfVisit: string | null;
  ancHivIAndIiScreening: string | null;
  hiv: string | null;
  ancHepatitisBScreening: string | null;
  hbvAntibody: string | null;
  ancHepatitisCScreening: string | null;
  hcvAntibody: string | null;
  ancGenotype: string | null;
  genotype: string | null;
  ancVdrl: string | null;
  vdrl: string | null;
  ancPcvAndHb: string | null;
  invPcv: string | null;
  invPcvRange: string | null;
  invHb: string | null;
  invHbRange: string | null;
  ancRandomBloodSugar: string | null;
  randomBloodSugar: string | null;
  randomBloodSugarUnit: string | null;
  randomBloodSugarRange: string | null;
  ancFastingBloodSugar: string | null;
  fastingBloodSugar: string | null;
  fastingBloodSugarUnit: string | null;
  fastingBloodSugarRange: string | null;
  ancBloodGrouping: string | null;
  bloodGrouping: string | null;
  bloodGroupingRange: string | null;
  rhesusFactor: string | null;
  rhesusFactorRange: string | null;
  ancUrinalysis: string | null;
  colour: string | null;
  appearanceClarity: string | null;
  ph: string | null;
  specificGravity: string | null;
  glucose: string | null;
  ketones: string | null;
  blood: string | null;
  protein: string | null;
  bilirubin: string | null;
  uribilinogen: string | null;
  nitrites: string | null;
  leukocyteEsterase: string | null;
  rbcs: string | null;
  wbcs: string | null;
  epithelialCells: string | null;
  casts: string | null;
  crystals: string | null;
  bacteria: string | null;
  yeast: string | null;
  ipt1: string | null;
  ipt1Ega: string | null;
  ipt2: string | null;
  ipt2Ega: string | null;
  ipt3: string | null;
  ipt3Ega: string | null;
  ipt4: string | null;
  ipt4Ega: string | null;
  hematinics: string | null;
  hematinicsEga: string | null;
  albendazole: string | null;
  albendazoleEga: string | null;
  tetanusToxoid1: string | null;
  tetanusToid1Ega: string | null;
  tetanusToxoid2: string | null;
  tetanusToid2Ega: string | null;
  tetanusToxoid3: string | null;
  tetanusToid3Ega: string | null;
  tetanusToxoid4: string | null;
  tetanusToid4Ega: string | null;
  tetanusToxoid5: string | null;
  tetanusToid5Ega: string | null;
  preauthorizationDetails: UpdateAntenatalDetail_updateAntenatalDetail_preauthorizationDetails | null;
}

export interface UpdateAntenatalDetail {
  updateAntenatalDetail: UpdateAntenatalDetail_updateAntenatalDetail;
}

export interface UpdateAntenatalDetailVariables {
  clinifyId: string;
  id: string;
  input: AntenatalDetailsInput;
  pin?: string | null;
}
