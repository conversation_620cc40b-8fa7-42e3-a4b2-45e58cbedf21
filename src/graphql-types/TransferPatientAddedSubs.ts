/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: TransferPatientAddedSubs
// ====================================================

export interface TransferPatientAddedSubs_TransferPatientAdded_transferHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  clinifyId: string | null;
}

export interface TransferPatientAddedSubs_TransferPatientAdded {
  __typename: "TransferPatientModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  roomOption: string | null;
  transferSource: string | null;
  roomInventoryId: string | null;
  transferHospital: TransferPatientAddedSubs_TransferPatientAdded_transferHospital | null;
  admissionId: string | null;
}

export interface TransferPatientAddedSubs {
  TransferPatientAdded: TransferPatientAddedSubs_TransferPatientAdded;
}

export interface TransferPatientAddedSubsVariables {
  profileId: string;
}
