/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NewNursingServicesInput, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddPatientNursingService
// ====================================================

export interface AddPatientNursingService_addNursingService_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface AddPatientNursingService_addNursingService_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface AddPatientNursingService_addNursingService_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: AddPatientNursingService_addNursingService_profile_coverageDetails_provider | null;
}

export interface AddPatientNursingService_addNursingService_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: AddPatientNursingService_addNursingService_profile_coverageDetails[] | null;
}

export interface AddPatientNursingService_addNursingService_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface AddPatientNursingService_addNursingService_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: AddPatientNursingService_addNursingService_preauthorizationDetails_provider | null;
}

export interface AddPatientNursingService_addNursingService_details_medications {
  __typename: "MedicationDetailType";
  medicationName: string | null;
  prescriptionTime: any | null;
  dosage: string | null;
  dosageUnit: string | null;
}

export interface AddPatientNursingService_addNursingService_details {
  __typename: "NursingServiceDetailModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  lastModifierId: string | null;
  lastModifierName: string | null;
  creatorId: string | null;
  creatorName: string | null;
  procedureDateTime: any | null;
  procedureType: string | null;
  provider: string | null;
  providerServiceName: string | null;
  priority: string | null;
  category: string | null;
  dateOfBirth: any | null;
  consentGiven: string | null;
  parentGuardianPresent: string | null;
  parentGuardianName: string | null;
  anaesthesiaGiven: string | null;
  anaesthesiaType: string | null;
  vitaminKGiven: string | null;
  castLocation: string | null;
  reasonForCasting: string | null;
  isItARepeatedCasting: string | null;
  hasRadiologicalInvestigationBeenDone: string | null;
  bathing: string | null;
  dressing: string | null;
  eating: string | null;
  mobility: string | null;
  stairClimbing: string | null;
  toiletUse: string | null;
  woundLocation: string | null;
  dressingType: string | null;
  dressingAppearance: string | null;
  dressingIntervention: string | null;
  lastDressingChange: any | null;
  dressingChangeDue: any | null;
  painScore: string | null;
  painDescriptors: string[] | null;
  otherPainDescriptors: string | null;
  signOfInfection: string | null;
  whichEar: string | null;
  observation: string | null;
  informedConsent: string | null;
  method: string | null;
  otherMethods: string | null;
  councelled: string | null;
  typeOfInjectable: string | null;
  typeOfImplant: string | null;
  duration: string | null;
  procedureStartDateTime: any | null;
  procedureEndDateTime: any | null;
  heartRateBefore: string | null;
  heartRateAfter: string | null;
  respiratoryRateBefore: string | null;
  respiratoryRateAfter: string | null;
  oxygenSaturationBefore: string | null;
  oxygenSaturationAfter: string | null;
  medications: AddPatientNursingService_addNursingService_details_medications[] | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  nursingServiceId: string | null;
  procedureName: string | null;
  location: string | null;
  procedureNote: string | null;
  injectionName: string | null;
  InjectionLocation: string | null;
  injectionPrepared: string | null;
  equipmentsSterilized: string | null;
  medicalHistoryChecked: string | null;
  repeatInjection: string | null;
}

export interface AddPatientNursingService_addNursingService_bill {
  __typename: "BillModel";
  id: string;
  createdDate: any;
}

export interface AddPatientNursingService_addNursingService_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
}

export interface AddPatientNursingService_addNursingService_progressNotes {
  __typename: "NursingServiceProgressNoteModel";
  id: string;
  note: string;
  conceal: boolean | null;
  nursingServiceId: string | null;
}

export interface AddPatientNursingService_addNursingService_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface AddPatientNursingService_addNursingService_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface AddPatientNursingService_addNursingService_consultations {
  __typename: "ConsultationModel";
  id: string;
}

export interface AddPatientNursingService_addNursingService_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface AddPatientNursingService_addNursingService_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface AddPatientNursingService_addNursingService_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface AddPatientNursingService_addNursingService_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface AddPatientNursingService_addNursingService_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface AddPatientNursingService_addNursingService_immunizations {
  __typename: "AdmissionModel";
  id: string;
}

export interface AddPatientNursingService_addNursingService {
  __typename: "NursingServiceModel";
  id: string;
  hmoProviderId: string | null;
  serviceDetails: AddPatientNursingService_addNursingService_serviceDetails[] | null;
  isPackage: boolean;
  specialty: string | null;
  department: string | null;
  nurseName: string | null;
  assistantNurseName: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  billStatus: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  hospitalId: string | null;
  profileId: string | null;
  profile: AddPatientNursingService_addNursingService_profile | null;
  preauthorizationDetails: AddPatientNursingService_addNursingService_preauthorizationDetails | null;
  details: AddPatientNursingService_addNursingService_details[] | null;
  bill: AddPatientNursingService_addNursingService_bill[] | null;
  hmoClaim: AddPatientNursingService_addNursingService_hmoClaim | null;
  progressNotes: AddPatientNursingService_addNursingService_progressNotes[] | null;
  vitals: AddPatientNursingService_addNursingService_vitals[];
  admissions: AddPatientNursingService_addNursingService_admissions[];
  consultations: AddPatientNursingService_addNursingService_consultations[];
  medications: AddPatientNursingService_addNursingService_medications[];
  surgeries: AddPatientNursingService_addNursingService_surgeries[];
  investigations: AddPatientNursingService_addNursingService_investigations[];
  labTests: AddPatientNursingService_addNursingService_labTests[];
  radiology: AddPatientNursingService_addNursingService_radiology[];
  immunizations: AddPatientNursingService_addNursingService_immunizations[];
}

export interface AddPatientNursingService {
  addNursingService: AddPatientNursingService_addNursingService;
}

export interface AddPatientNursingServiceVariables {
  input: NewNursingServicesInput;
  id?: string | null;
  pin?: string | null;
}
