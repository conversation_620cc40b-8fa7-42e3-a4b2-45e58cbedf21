/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CasesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHepatitisBTesting
// ====================================================

export interface GetHepatitisBTesting_getHepatitisBTesting_ageRanges {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetHepatitisBTesting_getHepatitisBTesting {
  __typename: "CasesSummary";
  name: number | null;
  totalFemale: number | null;
  totalMale: number | null;
  totalPositive: number | null;
  totalNegative: number | null;
  ageRanges: GetHepatitisBTesting_getHepatitisBTesting_ageRanges[] | null;
}

export interface GetHepatitisBTesting {
  getHepatitisBTesting: GetHepatitisBTesting_getHepatitisBTesting[];
}

export interface GetHepatitisBTestingVariables {
  filter?: CasesAnalyticsFilter | null;
}
