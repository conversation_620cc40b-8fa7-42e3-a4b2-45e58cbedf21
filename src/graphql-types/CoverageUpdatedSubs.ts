/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: CoverageUpdatedSubs
// ====================================================

export interface CoverageUpdatedSubs_CoverageInformationUpdated_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface CoverageUpdatedSubs_CoverageInformationUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface CoverageUpdatedSubs_CoverageInformationUpdated {
  __typename: "HmoProfileModel";
  id: string;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  memberStartDate: any | null;
  memberStatus: string | null;
  companyName: string | null;
  companyAddress: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  memberPlanId: string | null;
  provider: CoverageUpdatedSubs_CoverageInformationUpdated_provider;
  profile: CoverageUpdatedSubs_CoverageInformationUpdated_profile | null;
}

export interface CoverageUpdatedSubs {
  CoverageInformationUpdated: CoverageUpdatedSubs_CoverageInformationUpdated;
}

export interface CoverageUpdatedSubsVariables {
  profileId: string;
}
