/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { StaffsFilterInputs } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHospitialStaffsMin
// ====================================================

export interface GetHospitialStaffsMin_hospital_staffs_list_personalInformation {
  __typename: "PersonalInformation";
  rank: string | null;
  speciality: string | null;
  department: string | null;
}

export interface GetHospitialStaffsMin_hospital_staffs_list_user {
  __typename: "UserModel";
  id: string;
  isPinMandatory: boolean | null;
  hasPin: boolean | null;
}

export interface GetHospitialStaffsMin_hospital_staffs_list {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  title: string | null;
  clinifyId: string;
  personalInformation: GetHospitialStaffsMin_hospital_staffs_list_personalInformation | null;
  user: GetHospitialStaffsMin_hospital_staffs_list_user;
}

export interface GetHospitialStaffsMin_hospital_staffs {
  __typename: "ProfilesResponse";
  totalCount: number;
  list: GetHospitialStaffsMin_hospital_staffs_list[];
}

export interface GetHospitialStaffsMin_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  clinifyId: string | null;
  staffs: GetHospitialStaffsMin_hospital_staffs;
}

export interface GetHospitialStaffsMin {
  hospital: GetHospitialStaffsMin_hospital;
}

export interface GetHospitialStaffsMinVariables {
  filterOptions?: StaffsFilterInputs | null;
  hospitalId?: string | null;
}
