/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ConsultationInput, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateConsultationBill
// ====================================================

export interface UpdateConsultationBill_updateConsultationBill_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface UpdateConsultationBill_updateConsultationBill_complaintSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface UpdateConsultationBill_updateConsultationBill_systemReviewSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface UpdateConsultationBill_updateConsultationBill_physicalExamSmartSelection {
  __typename: "SelectionInputType";
  positives: string[] | null;
  negatives: string[] | null;
  vitalSignRevied: boolean | null;
  nurseNoteReviewed: boolean | null;
  allOtherSystemNegative: boolean | null;
  otherSystemNegative: string[] | null;
  topLevelChecks: string[] | null;
  systemChecks: string[] | null;
  systemPositives: string[] | null;
  systemNegatives: string[] | null;
}

export interface UpdateConsultationBill_updateConsultationBill_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface UpdateConsultationBill_updateConsultationBill_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: UpdateConsultationBill_updateConsultationBill_profile_coverageDetails_provider | null;
}

export interface UpdateConsultationBill_updateConsultationBill_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: UpdateConsultationBill_updateConsultationBill_profile_coverageDetails[] | null;
}

export interface UpdateConsultationBill_updateConsultationBill_provisionalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UpdateConsultationBill_updateConsultationBill_finalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UpdateConsultationBill_updateConsultationBill_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UpdateConsultationBill_updateConsultationBill_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: UpdateConsultationBill_updateConsultationBill_preauthorizationDetails_provider | null;
}

export interface UpdateConsultationBill_updateConsultationBill_treatmentPlans {
  __typename: "TreatmentPlanModel";
  id: string;
  treatmentPlan: string | null;
  conceal: boolean | null;
  patientAdmitted: string | null;
  concealObservationNote: boolean | null;
  observationNote: string | null;
  admissionConsent: string | null;
  adverseEffectsFollowingTreatment: string | null;
  stateEffects: string | null;
  adverseEffectsInvestigated: string | null;
  outcomeOfInvestigation: string | null;
  treatmentGiven: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  treatmentStatus: string | null;
}

export interface UpdateConsultationBill_updateConsultationBill_allergies {
  __typename: "AllergyModel";
  id: string;
}

export interface UpdateConsultationBill_updateConsultationBill_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface UpdateConsultationBill_updateConsultationBill_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface UpdateConsultationBill_updateConsultationBill_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface UpdateConsultationBill_updateConsultationBill_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface UpdateConsultationBill_updateConsultationBill_labTests {
  __typename: "LabResultModel";
  id: string;
}

export interface UpdateConsultationBill_updateConsultationBill_radiology {
  __typename: "RadiologyResultModel";
  id: string;
}

export interface UpdateConsultationBill_updateConsultationBill_investigations {
  __typename: "LabResultModel";
  id: string;
}

export interface UpdateConsultationBill_updateConsultationBill_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface UpdateConsultationBill_updateConsultationBill_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
}

export interface UpdateConsultationBill_updateConsultationBill {
  __typename: "ConsultationModel";
  id: string;
  consultationDateTime: any | null;
  duration: string | null;
  doctorName: string;
  priority: string | null;
  specialty: string | null;
  class: string | null;
  hmoProviderId: string | null;
  serviceDetails: UpdateConsultationBill_updateConsultationBill_serviceDetails[] | null;
  isPackage: boolean;
  clinicName: string | null;
  clinicAddress: string | null;
  complaint: string | null;
  provider: string | null;
  providerServiceName: string | null;
  complaintHistory: string | null;
  healthEducation: string | null;
  systemReview: string | null;
  systemReviewSmartText: string | null;
  complaintGender: string | null;
  complaintSmartSelection: UpdateConsultationBill_updateConsultationBill_complaintSmartSelection | null;
  systemReviewSmartSelection: UpdateConsultationBill_updateConsultationBill_systemReviewSmartSelection | null;
  category: string | null;
  department: string | null;
  physicalExam: string | null;
  physicalExamSmartText: string | null;
  physicalExamSmartSelection: UpdateConsultationBill_updateConsultationBill_physicalExamSmartSelection | null;
  referral: boolean | null;
  referralDate: any | null;
  referralReason: string | null;
  concealReferralReason: boolean | null;
  specialtyReferredTo: string | null;
  profileId: string | null;
  profile: UpdateConsultationBill_updateConsultationBill_profile | null;
  externalReferral: boolean | null;
  provisionalDiagnosis: UpdateConsultationBill_updateConsultationBill_provisionalDiagnosis[] | null;
  finalDiagnosis: UpdateConsultationBill_updateConsultationBill_finalDiagnosis[] | null;
  consultationStartDate: any | null;
  consultationEndDate: any | null;
  appointmentId: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  concealComplaint: boolean | null;
  concealComplaintHistory: boolean | null;
  concealPhysicalExam: boolean | null;
  concealSystemReview: boolean | null;
  concealHealthEducation: boolean | null;
  billStatus: string | null;
  audiometry: string | null;
  concealAudiometry: boolean | null;
  hospitalId: string | null;
  preauthorizationDetails: UpdateConsultationBill_updateConsultationBill_preauthorizationDetails | null;
  treatmentPlans: UpdateConsultationBill_updateConsultationBill_treatmentPlans[] | null;
  allergies: UpdateConsultationBill_updateConsultationBill_allergies[];
  medications: UpdateConsultationBill_updateConsultationBill_medications[];
  surgeries: UpdateConsultationBill_updateConsultationBill_surgeries[];
  admissions: UpdateConsultationBill_updateConsultationBill_admissions[];
  vitals: UpdateConsultationBill_updateConsultationBill_vitals[];
  labTests: UpdateConsultationBill_updateConsultationBill_labTests[];
  radiology: UpdateConsultationBill_updateConsultationBill_radiology[];
  investigations: UpdateConsultationBill_updateConsultationBill_investigations[];
  nursingServices: UpdateConsultationBill_updateConsultationBill_nursingServices[];
  hmoClaim: UpdateConsultationBill_updateConsultationBill_hmoClaim | null;
}

export interface UpdateConsultationBill {
  updateConsultationBill: UpdateConsultationBill_updateConsultationBill;
}

export interface UpdateConsultationBillVariables {
  id: string;
  input: ConsultationInput;
}
