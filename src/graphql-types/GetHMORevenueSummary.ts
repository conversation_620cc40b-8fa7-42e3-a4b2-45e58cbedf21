/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHMORevenueSummary
// ====================================================

export interface GetHMORevenueSummary_getHMORevenueDataSummary {
  __typename: "FinanceDataSummaryResponse";
  totalAmountDue: number | null;
  totalAmount: number | null;
  category: string;
  name: number | null;
  totalAmountOutstanding: number | null;
  totalAmountPaid: number | null;
  totalDiscountAmount: number | null;
  totalRevenue: number | null;
}

export interface GetHMORevenueSummary {
  getHMORevenueDataSummary: GetHMORevenueSummary_getHMORevenueDataSummary[];
}

export interface GetHMORevenueSummaryVariables {
  filter: FinanceAnalyticsFilter;
}
