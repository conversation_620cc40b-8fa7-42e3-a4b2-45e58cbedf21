/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { TransactionType, TransactionStatus, Currency } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: ArchiveWalletTransaction
// ====================================================

export interface ArchiveWalletTransaction_archiveWalletTransaction_senderWallet_profile {
  __typename: "ProfileModel";
  clinifyId: string;
  fullName: string;
}

export interface ArchiveWalletTransaction_archiveWalletTransaction_senderWallet {
  __typename: "WalletModel";
  profile: ArchiveWalletTransaction_archiveWalletTransaction_senderWallet_profile | null;
}

export interface ArchiveWalletTransaction_archiveWalletTransaction_receiverWallet_profile {
  __typename: "ProfileModel";
  clinifyId: string;
  fullName: string;
}

export interface ArchiveWalletTransaction_archiveWalletTransaction_receiverWallet {
  __typename: "WalletModel";
  profile: ArchiveWalletTransaction_archiveWalletTransaction_receiverWallet_profile | null;
}

export interface ArchiveWalletTransaction_archiveWalletTransaction {
  __typename: "WalletTransactionModel";
  id: string;
  createdDate: any;
  amount: number;
  transactionType: TransactionType;
  transactionStatus: TransactionStatus;
  transactionDetails: string;
  currency: Currency;
  amountSent: number;
  description: string;
  senderWallet: ArchiveWalletTransaction_archiveWalletTransaction_senderWallet | null;
  receiverWallet: ArchiveWalletTransaction_archiveWalletTransaction_receiverWallet | null;
}

export interface ArchiveWalletTransaction {
  archiveWalletTransaction: ArchiveWalletTransaction_archiveWalletTransaction[];
}

export interface ArchiveWalletTransactionVariables {
  ids: string[];
  archive?: boolean | null;
}
