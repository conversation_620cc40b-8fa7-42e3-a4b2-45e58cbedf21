/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmosAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHmoMedicationData
// ====================================================

export interface GetHmoMedicationData_getHmoMedicationData {
  __typename: "MedicationReportData";
  prescribingHospital: string | null;
  dispensingPharmacy: string | null;
  enrolleeID: string | null;
  enrolleeName: string | null;
  datePrescribed: string | null;
  medicationName: string | null;
  medicationCategory: string | null;
  medicationQuantity: string | null;
  unitPrice: string | null;
  totalAmount: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  frequency: string | null;
  duration: string | null;
  prescriptionNote: string | null;
  dispenseDate: string | null;
  dispenseTime: string | null;
  dispenseNote: string | null;
  status: string | null;
}

export interface GetHmoMedicationData {
  getHmoMedicationData: GetHmoMedicationData_getHmoMedicationData[];
}

export interface GetHmoMedicationDataVariables {
  filter: HmosAnalyticsFilter;
}
