/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AllergyLinkedRecordType } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: LinkRecordsToAllergy
// ====================================================

export interface LinkRecordsToAllergy_linkRecordsToAllergy_details_clinicalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface LinkRecordsToAllergy_linkRecordsToAllergy_details {
  __typename: "AllergyDetails";
  type: string;
  trigger: string;
  reactions: string[] | null;
  severeness: string | null;
  clinicalDiagnosis: LinkRecordsToAllergy_linkRecordsToAllergy_details_clinicalDiagnosis[] | null;
}

export interface LinkRecordsToAllergy_linkRecordsToAllergy_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface LinkRecordsToAllergy_linkRecordsToAllergy_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface LinkRecordsToAllergy_linkRecordsToAllergy {
  __typename: "AllergyModel";
  id: string;
  occurenceDate: any | null;
  duration: string | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  doctorName: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  details: LinkRecordsToAllergy_linkRecordsToAllergy_details[] | null;
  profileId: string | null;
  profile: LinkRecordsToAllergy_linkRecordsToAllergy_profile | null;
  documentUrl: string[] | null;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
  createdDate: any;
  updatedDate: any;
  medications: LinkRecordsToAllergy_linkRecordsToAllergy_medications[];
}

export interface LinkRecordsToAllergy {
  linkRecordsToAllergy: LinkRecordsToAllergy_linkRecordsToAllergy;
}

export interface LinkRecordsToAllergyVariables {
  parentId: string;
  recordIds: string[];
  recordType: AllergyLinkedRecordType;
}
