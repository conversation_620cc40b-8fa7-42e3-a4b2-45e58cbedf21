/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NotificationFilterInput, DashboardIcon } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPatientNotifications
// ====================================================

export interface GetPatientNotifications_getNotifications_list_metaData {
  __typename: "Notifications";
  id: string | null;
  clinifyId: string | null;
  specialty: string | null;
  provider: string | null;
  department: string | null;
  hospitalId: string | null;
  profileId: string | null;
  /**
   * The icon of the record that triggered the notification (DashboboardIcon)
   */
  recordType: DashboardIcon | null;
  /**
   * For sub records present the parent record ID
   */
  recordId: string | null;
}

export interface GetPatientNotifications_getNotifications_list {
  __typename: "NotificationsModel";
  id: string;
  title: string;
  tag: string;
  description: string;
  profileId: string | null;
  metaData: GetPatientNotifications_getNotifications_list_metaData | null;
  createdDate: any;
  isSeen: boolean | null;
}

export interface GetPatientNotifications_getNotifications {
  __typename: "NotificationsResponse";
  list: GetPatientNotifications_getNotifications_list[];
  unreadCount: number;
}

export interface GetPatientNotifications {
  getNotifications: GetPatientNotifications_getNotifications;
}

export interface GetPatientNotificationsVariables {
  profileId?: string | null;
  options?: NotificationFilterInput | null;
}
