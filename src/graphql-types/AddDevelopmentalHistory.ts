/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DevelopmentalHistoryInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddDevelopmentalHistory
// ====================================================

export interface AddDevelopmentalHistory_addDevelopmentalHistory_milestones {
  __typename: "DevelopmentalHistoryMilestoneModel";
  id: string;
  milestone: string;
  ageRange: string;
  whenMet: string | null;
}

export interface AddDevelopmentalHistory_addDevelopmentalHistory {
  __typename: "DevelopmentalHistoryModel";
  id: string;
  groupName: string;
  milestones: AddDevelopmentalHistory_addDevelopmentalHistory_milestones[] | null;
}

export interface AddDevelopmentalHistory {
  addDevelopmentalHistory: AddDevelopmentalHistory_addDevelopmentalHistory;
}

export interface AddDevelopmentalHistoryVariables {
  input: DevelopmentalHistoryInput;
}
