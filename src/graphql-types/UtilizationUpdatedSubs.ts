/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UtilizationUpdatedSubs
// ====================================================

export interface UtilizationUpdatedSubs_UtilizationUpdated_flags {
  __typename: "FlagDto";
  flag: string | null;
  ruleId: string | null;
}

export interface UtilizationUpdatedSubs_UtilizationUpdated_utilisationStatus {
  __typename: "ClaimsApprovalInputType";
  status: string | null;
  comment: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusDescription: string | null;
  serviceAmount: number | null;
  vettingGroup: string;
  creatorId: string;
  creatorName: string;
  createdDate: any;
  updatedDate: any | null;
}

export interface UtilizationUpdatedSubs_UtilizationUpdated_transferFund_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  title: string | null;
}

export interface UtilizationUpdatedSubs_UtilizationUpdated_transferFund {
  __typename: "TransferFundModel";
  id: string;
  amount: number;
  serviceChargeAmount: number | null;
  transferReference: string | null;
  destinationBankCode: string | null;
  destinationBankName: string | null;
  destinationAccountNumber: string | null;
  destinationAccountName: string | null;
  createdDate: any;
  originatorName: string | null;
  narration: string | null;
  createdBy: UtilizationUpdatedSubs_UtilizationUpdated_transferFund_createdBy | null;
}

export interface UtilizationUpdatedSubs_UtilizationUpdated_aiReason {
  __typename: "AIReason";
  reason: string | null;
  status: string | null;
}

export interface UtilizationUpdatedSubs_UtilizationUpdated {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  category: string | null;
  type: string | null;
  quantity: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
  status: string | null;
  paCode: string | null;
  medicationCategory: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  frequency: string | null;
  duration: string | null;
  birthCount: string | null;
  deliveryDateTime: any | null;
  gestationalAge: string | null;
  specialty: string | null;
  utilizationCode: string | null;
  utilizationId: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusDescription: string | null;
  creatorName: string | null;
  createdDate: any;
  lastModifierName: string | null;
  updatedDate: any;
  /**
   * Service type from which this utilization is created
   */
  serviceName: string | null;
  confirmation: boolean | null;
  percentageCovered: number | null;
  amountCovered: number | null;
  paymentModel: string | null;
  autoApprovalSource: string | null;
  flags: UtilizationUpdatedSubs_UtilizationUpdated_flags[] | null;
  utilisationStatus: UtilizationUpdatedSubs_UtilizationUpdated_utilisationStatus[] | null;
  transferFundId: string | null;
  transferFund: UtilizationUpdatedSubs_UtilizationUpdated_transferFund | null;
  aiReason: UtilizationUpdatedSubs_UtilizationUpdated_aiReason | null;
}

export interface UtilizationUpdatedSubs {
  UtilizationUpdated: UtilizationUpdatedSubs_UtilizationUpdated;
}

export interface UtilizationUpdatedSubsVariables {
  profileId: string;
  hospitalId: string;
  hmoProviderId?: string | null;
}
