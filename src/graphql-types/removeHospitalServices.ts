/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: removeHospitalServices
// ====================================================

export interface removeHospitalServices_removeHospitalServices_services {
  __typename: "HospitalService";
  id: string | null;
  name: string | null;
  createdOn: any | null;
  updatedOn: any | null;
  description: string | null;
  creatorName: string | null;
}

export interface removeHospitalServices_removeHospitalServices {
  __typename: "HospitalModel";
  id: string;
  services: removeHospitalServices_removeHospitalServices_services[] | null;
}

export interface removeHospitalServices {
  removeHospitalServices: removeHospitalServices_removeHospitalServices;
}

export interface removeHospitalServicesVariables {
  ids: string[];
}
