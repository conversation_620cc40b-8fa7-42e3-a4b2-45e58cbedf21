/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetServicesSummary
// ====================================================

export interface GetServicesSummary_getServicesSummary_antenatalsSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_admissionsSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_consultationsSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_proceduresSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_immunizationsSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_medicationsDispensedSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_consumablesDispensedSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_nursingServicesSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_medicalReportsSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_radiologySummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_laboratorySummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_medicationsPrescribedSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_consumablesPrescribedSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_requestedLaboratorySummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_requestedRadiologySummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_processedLaboratorySummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary_processedRadiologySummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetServicesSummary_getServicesSummary {
  __typename: "ServicesSummary";
  name: number | null;
  totalAntenatals: number | null;
  totalAntenatalsAmount: number | null;
  totalAdmissions: number | null;
  totalAdmissionsAmount: number | null;
  totalMedications: number | null;
  totalProcedures: number | null;
  totalProceduresAmount: number | null;
  totalConsultations: number | null;
  totalConsultationsAmount: number | null;
  totalMedicationsDispensed: number | null;
  totalMedicationsDispensedAmount: number | null;
  totalConsumablesDispensed: number | null;
  totalRequestedLaboratoryAmount: number | null;
  totalConsumablesDispensedAmount: number | null;
  totalMedicationsPrescribed: number | null;
  totalMedicationsPrescribedAmount: number | null;
  totalConsumablesPrescribed: number | null;
  totalConsumablesPrescribedAmount: number | null;
  totalImmunizations: number | null;
  totalImmunizationsAmount: number | null;
  totalInvestigations: number | null;
  totalRadiology: number | null;
  totalRadiologyAmount: number | null;
  totalLaboratory: number | null;
  totalLaboratoryAmount: number | null;
  totalRequestedLaboratory: number | null;
  totalRequestedRadiologyAmount: number | null;
  totalProcessedLaboratory: number | null;
  totalProcessedLaboratoryAmount: number | null;
  totalProcessedRadiology: number | null;
  totalProcessedRadiologyAmount: number | null;
  totalRequestedRadiology: number | null;
  totalNursingServices: number | null;
  totalNursingServicesAmount: number | null;
  totalMedicalReports: number | null;
  totalMedicalReportsAmount: number | null;
  antenatalsSummary: GetServicesSummary_getServicesSummary_antenatalsSummary[] | null;
  admissionsSummary: GetServicesSummary_getServicesSummary_admissionsSummary[] | null;
  consultationsSummary: GetServicesSummary_getServicesSummary_consultationsSummary[] | null;
  proceduresSummary: GetServicesSummary_getServicesSummary_proceduresSummary[] | null;
  immunizationsSummary: GetServicesSummary_getServicesSummary_immunizationsSummary[] | null;
  medicationsDispensedSummary: GetServicesSummary_getServicesSummary_medicationsDispensedSummary[] | null;
  consumablesDispensedSummary: GetServicesSummary_getServicesSummary_consumablesDispensedSummary[] | null;
  nursingServicesSummary: GetServicesSummary_getServicesSummary_nursingServicesSummary[] | null;
  medicalReportsSummary: GetServicesSummary_getServicesSummary_medicalReportsSummary[] | null;
  radiologySummary: GetServicesSummary_getServicesSummary_radiologySummary[] | null;
  laboratorySummary: GetServicesSummary_getServicesSummary_laboratorySummary[] | null;
  medicationsPrescribedSummary: GetServicesSummary_getServicesSummary_medicationsPrescribedSummary[] | null;
  consumablesPrescribedSummary: GetServicesSummary_getServicesSummary_consumablesPrescribedSummary[] | null;
  requestedLaboratorySummary: GetServicesSummary_getServicesSummary_requestedLaboratorySummary[] | null;
  requestedRadiologySummary: GetServicesSummary_getServicesSummary_requestedRadiologySummary[] | null;
  processedLaboratorySummary: GetServicesSummary_getServicesSummary_processedLaboratorySummary[] | null;
  processedRadiologySummary: GetServicesSummary_getServicesSummary_processedRadiologySummary[] | null;
}

export interface GetServicesSummary {
  getServicesSummary: GetServicesSummary_getServicesSummary;
}

export interface GetServicesSummaryVariables {
  filter?: ServicesAnalyticsFilter | null;
}
