/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: NursingServiceUpdatedSubs
// ====================================================

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: NursingServiceUpdatedSubs_NursingServiceUpdated_profile_coverageDetails_provider | null;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: NursingServiceUpdatedSubs_NursingServiceUpdated_profile_coverageDetails[] | null;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: NursingServiceUpdatedSubs_NursingServiceUpdated_preauthorizationDetails_provider | null;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_consultations {
  __typename: "ConsultationModel";
  id: string;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated_immunizations {
  __typename: "AdmissionModel";
  id: string;
}

export interface NursingServiceUpdatedSubs_NursingServiceUpdated {
  __typename: "NursingServiceModel";
  id: string;
  hmoProviderId: string | null;
  serviceDetails: NursingServiceUpdatedSubs_NursingServiceUpdated_serviceDetails[] | null;
  isPackage: boolean;
  specialty: string | null;
  department: string | null;
  nurseName: string | null;
  assistantNurseName: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  billStatus: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  hospitalId: string | null;
  profileId: string | null;
  profile: NursingServiceUpdatedSubs_NursingServiceUpdated_profile | null;
  preauthorizationDetails: NursingServiceUpdatedSubs_NursingServiceUpdated_preauthorizationDetails | null;
  vitals: NursingServiceUpdatedSubs_NursingServiceUpdated_vitals[];
  admissions: NursingServiceUpdatedSubs_NursingServiceUpdated_admissions[];
  consultations: NursingServiceUpdatedSubs_NursingServiceUpdated_consultations[];
  medications: NursingServiceUpdatedSubs_NursingServiceUpdated_medications[];
  surgeries: NursingServiceUpdatedSubs_NursingServiceUpdated_surgeries[];
  investigations: NursingServiceUpdatedSubs_NursingServiceUpdated_investigations[];
  labTests: NursingServiceUpdatedSubs_NursingServiceUpdated_labTests[];
  radiology: NursingServiceUpdatedSubs_NursingServiceUpdated_radiology[];
  immunizations: NursingServiceUpdatedSubs_NursingServiceUpdated_immunizations[];
}

export interface NursingServiceUpdatedSubs {
  NursingServiceUpdated: NursingServiceUpdatedSubs_NursingServiceUpdated;
}

export interface NursingServiceUpdatedSubsVariables {
  profileId: string;
}
