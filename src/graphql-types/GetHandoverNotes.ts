/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HandoverNoteFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHandoverNotes
// ====================================================

export interface GetHandoverNotes_handoverNotes_list_staffs_staffProfile {
  __typename: "ProfileModel";
  fullName: string;
}

export interface GetHandoverNotes_handoverNotes_list_staffs {
  __typename: "HandoverStaffModel";
  id: string;
  status: string | null;
  staffProfile: GetHandoverNotes_handoverNotes_list_staffs_staffProfile | null;
}

export interface GetHandoverNotes_handoverNotes_list_handoverBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface GetHandoverNotes_handoverNotes_list_items_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
}

export interface GetHandoverNotes_handoverNotes_list_items {
  __typename: "HandoverNoteItemModel";
  id: string;
  note: string | null;
  patientProfileId: string;
  patientInformation: GetHandoverNotes_handoverNotes_list_items_patientInformation;
}

export interface GetHandoverNotes_handoverNotes_list {
  __typename: "HandoverNoteModel";
  id: string;
  name: string;
  handoverDateTime: any;
  updatedDate: any | null;
  createdDate: any;
  hospitalId: string;
  creatorId: string;
  department: string | null;
  handoverById: string;
  specialty: string | null;
  staffs: GetHandoverNotes_handoverNotes_list_staffs[];
  handoverBy: GetHandoverNotes_handoverNotes_list_handoverBy;
  items: GetHandoverNotes_handoverNotes_list_items[];
}

export interface GetHandoverNotes_handoverNotes {
  __typename: "HandoverNoteListResponse";
  totalCount: number;
  list: GetHandoverNotes_handoverNotes_list[];
}

export interface GetHandoverNotes {
  handoverNotes: GetHandoverNotes_handoverNotes;
}

export interface GetHandoverNotesVariables {
  filterOptions: HandoverNoteFilterInput;
}
