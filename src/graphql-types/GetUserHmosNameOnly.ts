/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetUserHmosNameOnly
// ====================================================

export interface GetUserHmosNameOnly_profile_hmos_list_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface GetUserHmosNameOnly_profile_hmos_list {
  __typename: "HmoProfileModel";
  id: string;
  provider: GetUserHmosNameOnly_profile_hmos_list_provider;
  memberNumber: string | null;
  memberUniqueId: string | null;
  memberPlan: string | null;
  memberPlanId: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
}

export interface GetUserHmosNameOnly_profile_hmos {
  __typename: "HmoProfileResponse";
  list: GetUserHmosNameOnly_profile_hmos_list[];
}

export interface GetUserHmosNameOnly_profile {
  __typename: "ProfileModel";
  id: string;
  hmos: GetUserHmosNameOnly_profile_hmos;
}

export interface GetUserHmosNameOnly {
  profile: GetUserHmosNameOnly_profile;
}

export interface GetUserHmosNameOnlyVariables {
  clinifyId: string;
}
