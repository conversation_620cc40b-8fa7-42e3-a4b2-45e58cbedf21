/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetEmployersByIds
// ====================================================

export interface GetEmployersByIds_employersByIds_employerPrimaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  countryName: string | null;
  value: string | null;
}

export interface GetEmployersByIds_employersByIds_employerSecondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  value: string | null;
  countryCode: string | null;
  countryName: string | null;
}

export interface GetEmployersByIds_employersByIds_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  countryName: string | null;
  value: string | null;
}

export interface GetEmployersByIds_employersByIds_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  countryName: string | null;
  value: string | null;
}

export interface GetEmployersByIds_employersByIds_selectedMemberPlans {
  __typename: "SelectedMemberPlansFields";
  id: string;
  name: string;
  frequency: string | null;
}

export interface GetEmployersByIds_employersByIds_employees_hmoPlanType_premiumDetails {
  __typename: "PremiumDetails";
  frequency: string | null;
  category: string | null;
  amount: number | null;
}

export interface GetEmployersByIds_employersByIds_employees_hmoPlanType {
  __typename: "HmoPlanTypeModel";
  id: string;
  name: string | null;
  planCode: string | null;
  premiumDetails: GetEmployersByIds_employersByIds_employees_hmoPlanType_premiumDetails[] | null;
}

export interface GetEmployersByIds_employersByIds_employees_hmoProfile_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface GetEmployersByIds_employersByIds_employees_hmoProfile_hmoPlanType_premiumDetails {
  __typename: "PremiumDetails";
  frequency: string | null;
  category: string | null;
  amount: number | null;
}

export interface GetEmployersByIds_employersByIds_employees_hmoProfile_hmoPlanType {
  __typename: "HmoPlanTypeModel";
  id: string;
  premiumDetails: GetEmployersByIds_employersByIds_employees_hmoProfile_hmoPlanType_premiumDetails[] | null;
}

export interface GetEmployersByIds_employersByIds_employees_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  provider: GetEmployersByIds_employersByIds_employees_hmoProfile_provider;
  primaryProviderId: string | null;
  primaryProviderAddress: string | null;
  primaryProviderName: string | null;
  memberNumber: string | null;
  memberStatus: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  premiumCollected: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionPayable: string | null;
  commissionRate: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  hmoPlanType: GetEmployersByIds_employersByIds_employees_hmoProfile_hmoPlanType | null;
}

export interface GetEmployersByIds_employersByIds_employees_profile_details {
  __typename: "ProfileDetailsModel";
  id: string;
  dateOfBirth: any | null;
}

export interface GetEmployersByIds_employersByIds_employees_profile_user {
  __typename: "UserModel";
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
}

export interface GetEmployersByIds_employersByIds_employees_profile {
  __typename: "ProfileModel";
  id: string;
  gender: Gender | null;
  title: string | null;
  details: GetEmployersByIds_employersByIds_employees_profile_details | null;
  user: GetEmployersByIds_employersByIds_employees_profile_user;
}

export interface GetEmployersByIds_employersByIds_employees_dependants_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  memberNumber: string | null;
  primaryProviderId: string | null;
  primaryProviderAddress: string | null;
  primaryProviderName: string | null;
}

export interface GetEmployersByIds_employersByIds_employees_dependants_profile_details {
  __typename: "ProfileDetailsModel";
  id: string;
  dateOfBirth: any | null;
}

export interface GetEmployersByIds_employersByIds_employees_dependants_profile_user {
  __typename: "UserModel";
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
}

export interface GetEmployersByIds_employersByIds_employees_dependants_profile {
  __typename: "ProfileModel";
  id: string;
  gender: Gender | null;
  title: string | null;
  details: GetEmployersByIds_employersByIds_employees_dependants_profile_details | null;
  user: GetEmployersByIds_employersByIds_employees_dependants_profile_user;
}

export interface GetEmployersByIds_employersByIds_employees_dependants {
  __typename: "EmployeeDependantModel";
  id: string;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  relationship: string | null;
  title: string | null;
  employeeId: string;
  lastModifierName: string | null;
  creatorName: string | null;
  createdDate: any;
  updatedDate: any;
  displayPictureUrl: string | null;
  hmoProfile: GetEmployersByIds_employersByIds_employees_dependants_hmoProfile | null;
  profile: GetEmployersByIds_employersByIds_employees_dependants_profile | null;
}

export interface GetEmployersByIds_employersByIds_employees {
  __typename: "EmployeeModel";
  id: string;
  title: string | null;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  jobTitle: string | null;
  department: string | null;
  employeeId: string | null;
  planCategory: string | null;
  employeeType: string | null;
  hmoPlanType: GetEmployersByIds_employersByIds_employees_hmoPlanType | null;
  hmoPlanTypeId: string | null;
  paymentFrequency: string | null;
  paymentDate: any | null;
  planStartDate: any | null;
  planDueDate: any | null;
  employerId: string;
  enrolledBy: string | null;
  createdDate: any;
  updatedDate: any;
  creatorName: string | null;
  lastModifierName: string | null;
  creatorId: string | null;
  lastModifierId: string | null;
  displayPictureUrl: string | null;
  hmoProfile: GetEmployersByIds_employersByIds_employees_hmoProfile | null;
  profile: GetEmployersByIds_employersByIds_employees_profile | null;
  dependants: GetEmployersByIds_employersByIds_employees_dependants[] | null;
}

export interface GetEmployersByIds_employersByIds_hmoProvider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface GetEmployersByIds_employersByIds {
  __typename: "EmployerModel";
  id: string;
  employerName: string;
  employerAddress: string | null;
  country: string | null;
  state: string | null;
  localGovernmentArea: string | null;
  ward: string | null;
  displayUrl: string | null;
  employerPrimaryEmailAddress: string | null;
  employerPrimaryPhoneNumber: GetEmployersByIds_employersByIds_employerPrimaryPhoneNumber | null;
  employerSecondaryPhoneNumber: GetEmployersByIds_employersByIds_employerSecondaryPhoneNumber | null;
  employerSecondaryEmailAddress: string | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: GetEmployersByIds_employersByIds_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: GetEmployersByIds_employersByIds_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  numberOfEmployees: number | null;
  planAmount: number | null;
  planAmountFlag: string | null;
  planAgent: string | null;
  paymentFrequency: string | null;
  planGroup: string | null;
  planSubGroup: string | null;
  additionalNote: string | null;
  employerNumber: string | null;
  employerPlanCode: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  commissionRate: string | null;
  commissionPayable: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  selectedMemberPlans: GetEmployersByIds_employersByIds_selectedMemberPlans[] | null;
  createdDate: any;
  updatedDate: any;
  creatorName: string | null;
  lastModifierName: string | null;
  creatorId: string | null;
  lastModifierId: string | null;
  employees: GetEmployersByIds_employersByIds_employees[] | null;
  hmoProvider: GetEmployersByIds_employersByIds_hmoProvider | null;
  hmoProviderId: string | null;
}

export interface GetEmployersByIds {
  employersByIds: GetEmployersByIds_employersByIds[] | null;
}

export interface GetEmployersByIdsVariables {
  ids: string[];
}
