/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicationDetailsInput, MedicationOptionType, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateMedicationDetail
// ====================================================

export interface UpdateMedicationDetail_updateMedicationDetail_medicationConsumables {
  __typename: "MedicationConsumables";
  name: string | null;
  drugInventoryId: string | null;
  unitPrice: number | null;
  quantity: string | null;
  inventoryClass: string | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_priceDetails {
  __typename: "MedPriceDetailInputType";
  type: string | null;
  name: string | null;
  pricePerUnit: string | null;
  patientType: string | null;
  paymentType: string | null;
  quantity: string | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UpdateMedicationDetail_updateMedicationDetail_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: UpdateMedicationDetail_updateMedicationDetail_preauthorizationDetails_provider | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_chemoDrugs_investigationDetails {
  __typename: "ChemoInvestigationDetails";
  investigationName: string | null;
  investigationType: string | null;
  investigationPerformed: boolean | null;
  investigationVerified: boolean | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_chemoDrugs_administrationRegister {
  __typename: "OncologyDrugAdministrationRegistration";
  administeredBy: string | null;
  administratorId: string | null;
  administrationDateTime: any | null;
  period: string;
}

export interface UpdateMedicationDetail_updateMedicationDetail_chemoDrugs_oncologyConsultationHistory_chemoComments {
  __typename: "ChemoCommentInputType";
  cycleNumber: number;
  section: string;
  comment: string | null;
  creatorId: string;
  creatorName: string;
  createdDate: any;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_chemoDrugs_oncologyConsultationHistory {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  chemoComments: UpdateMedicationDetail_updateMedicationDetail_chemoDrugs_oncologyConsultationHistory_chemoComments[] | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_chemoDrugs {
  __typename: "OncologyChemoDrugModel";
  id: string;
  drugName: string;
  route: string | null;
  infusionUsed: string | null;
  dosage: string | null;
  dosagePercentage: string | null;
  totalDose: string | null;
  adjustedDose: string | null;
  quantity: string | null;
  day: string;
  cycleNumber: number;
  drugId: string | null;
  note: string | null;
  section: string;
  frequency: string | null;
  combinationGroupName: string | null;
  combinationName: string | null;
  chemoDiagnosis: string | null;
  inventoryClass: string | null;
  medicationDetailsId: string | null;
  investigationDetails: UpdateMedicationDetail_updateMedicationDetail_chemoDrugs_investigationDetails[] | null;
  administrationRegister: UpdateMedicationDetail_updateMedicationDetail_chemoDrugs_administrationRegister[] | null;
  oncologyConsultationHistory: UpdateMedicationDetail_updateMedicationDetail_chemoDrugs_oncologyConsultationHistory | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_dispenseRegister_details_periods_audits {
  __typename: "dospenseRegisterAudit";
  fullName: string | null;
  dateTime: any | null;
  desc: string | null;
  checkId: number | null;
  profileId: string | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_dispenseRegister_details_periods {
  __typename: "PeriodsDetails";
  no: number | null;
  values: string | null;
  count: number | null;
  audits: UpdateMedicationDetail_updateMedicationDetail_dispenseRegister_details_periods_audits[] | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_dispenseRegister_details {
  __typename: "dispenseRegisterDetails";
  medicationName: string;
  reference: string | null;
  periodName: string;
  periods: UpdateMedicationDetail_updateMedicationDetail_dispenseRegister_details_periods[];
}

export interface UpdateMedicationDetail_updateMedicationDetail_dispenseRegister {
  __typename: "DispenseRegistersModel";
  id: string;
  details: UpdateMedicationDetail_updateMedicationDetail_dispenseRegister_details | null;
  creator: string | null;
  updater: string | null;
  updatedDate: any;
  createdDate: any;
}

export interface UpdateMedicationDetail_updateMedicationDetail_dispenseDetails_dispenseServiceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_dispenseDetails_dispenseConsumables {
  __typename: "DispenseConsumables";
  name: string | null;
  drugInventoryId: string | null;
  quantityConsumed: string | null;
  quantityRemaining: string | null;
  inventoryClass: string | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_dispenseDetails_hmoClaim_utilizations {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  type: string | null;
  /**
   * Value is in Naira
   */
  price: string | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_dispenseDetails_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
  claimDate: any | null;
  enrolleeNumber: string | null;
  utilizations: UpdateMedicationDetail_updateMedicationDetail_dispenseDetails_hmoClaim_utilizations[] | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail_dispenseDetails {
  __typename: "DispenseDetailsModel";
  id: string;
  dispenseDate: any | null;
  dispensedBy: string | null;
  medicationName: string[] | null;
  medicationDetailId: string | null;
  dispenseNote: string | null;
  concealDispenseNote: boolean;
  hmoProviderId: string | null;
  hospitalId: string;
  dispenseServiceDetails: UpdateMedicationDetail_updateMedicationDetail_dispenseDetails_dispenseServiceDetails[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
  medicationId: string | null;
  billingId: string | null;
  quantityRemaining: string | null;
  quantityDispensed: string | null;
  dispenseConsumables: UpdateMedicationDetail_updateMedicationDetail_dispenseDetails_dispenseConsumables[] | null;
  option: MedicationOptionType | null;
  hmoClaimId: string | null;
  inventoryClass: string | null;
  hmoClaim: UpdateMedicationDetail_updateMedicationDetail_dispenseDetails_hmoClaim | null;
}

export interface UpdateMedicationDetail_updateMedicationDetail {
  __typename: "MedicationDetailsModel";
  id: string;
  datePrescribed: any | null;
  duration: string | null;
  medicationName: string | null;
  medicationCategory: string | null;
  purpose: string | null;
  administrationMethod: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  prescriptionNote: string | null;
  concealPrescriptionNote: boolean | null;
  type: string | null;
  quantity: string | null;
  startDate: any | null;
  endDate: any | null;
  bank: string | null;
  drugInventoryId: string | null;
  option: MedicationOptionType | null;
  unitPrice: string | null;
  medicationStatus: string | null;
  provider: string | null;
  fromBundle: string | null;
  medicationConsumables: UpdateMedicationDetail_updateMedicationDetail_medicationConsumables[] | null;
  priceDetails: UpdateMedicationDetail_updateMedicationDetail_priceDetails | null;
  discontinue: string | null;
  discontinueReason: string | null;
  adverseEffectsFollowingMedication: string | null;
  stateEffects: string | null;
  adverseEffectsInvestigated: string | null;
  outcomeOfInvestigation: string | null;
  refillNumber: number | null;
  frequency: string | null;
  isPackage: boolean;
  hospitalId: string | null;
  medicationType: string | null;
  preauthorizationDetails: UpdateMedicationDetail_updateMedicationDetail_preauthorizationDetails | null;
  diagnosis: UpdateMedicationDetail_updateMedicationDetail_diagnosis[] | null;
  chemoDrugs: UpdateMedicationDetail_updateMedicationDetail_chemoDrugs[] | null;
  inventoryClass: string | null;
  dispenseRegister: UpdateMedicationDetail_updateMedicationDetail_dispenseRegister | null;
  dispenseDetails: UpdateMedicationDetail_updateMedicationDetail_dispenseDetails[] | null;
}

export interface UpdateMedicationDetail {
  updateMedicationDetail: UpdateMedicationDetail_updateMedicationDetail;
}

export interface UpdateMedicationDetailVariables {
  clinifyId: string;
  id: string;
  input: MedicationDetailsInput;
  pin?: string | null;
}
