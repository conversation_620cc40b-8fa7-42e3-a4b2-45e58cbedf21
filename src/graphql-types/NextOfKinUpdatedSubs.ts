/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: NextOfKinUpdatedSubs
// ====================================================

export interface NextOfKinUpdatedSubs_NextOfKinUpdated_phoneNumber {
  __typename: "PhoneNumberFields";
  countryName: string | null;
  countryCode: string | null;
  value: string | null;
}

export interface NextOfKinUpdatedSubs_NextOfKinUpdated_phoneNumberAlt {
  __typename: "PhoneNumberFields";
  countryName: string | null;
  countryCode: string | null;
  value: string | null;
}

export interface NextOfKinUpdatedSubs_NextOfKinUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface NextOfKinUpdatedSubs_NextOfKinUpdated {
  __typename: "NextOfKinModel";
  id: string;
  firstName: string | null;
  lastName: string | null;
  gender: Gender | null;
  title: string | null;
  middleName: string | null;
  bloodGroup: string | null;
  genoType: string | null;
  phoneNumber: NextOfKinUpdatedSubs_NextOfKinUpdated_phoneNumber | null;
  phoneNumberAlt: NextOfKinUpdatedSubs_NextOfKinUpdated_phoneNumberAlt | null;
  email: string | null;
  emailAlt: string | null;
  relationship: string | null;
  occupation: string | null;
  address: string | null;
  profile: NextOfKinUpdatedSubs_NextOfKinUpdated_profile | null;
}

export interface NextOfKinUpdatedSubs {
  NextOfKinUpdated: NextOfKinUpdatedSubs_NextOfKinUpdated;
}

export interface NextOfKinUpdatedSubsVariables {
  profileId: string;
}
