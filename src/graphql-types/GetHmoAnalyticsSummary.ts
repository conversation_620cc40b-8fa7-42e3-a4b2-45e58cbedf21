/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmosAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHmoAnalyticsSummary
// ====================================================

export interface GetHmoAnalyticsSummary_getHmosSummary_groupedData {
  __typename: "GroupedHmoClaimSummary";
  hospitalId: string | null;
  hmoProvider: string | null;
  hospitalName: string | null;
  totalClaims: number | null;
  totalApprovedClaims: number | null;
  totalRejectedClaims: number | null;
  totalDraftClaims: number | null;
  totalSubmittedClaims: number | null;
  totalClaimsAmount: number | null;
  totalApprovedClaimsAmount: number | null;
  totalRejectedClaimsAmount: number | null;
  totalDraftClaimsAmount: number | null;
  totalSubmittedClaimsAmount: number | null;
  totalFlaggedClaims: number | null;
  totalPaidClaims: number | null;
  totalEnrollees: number | null;
  totalEnrolleeVisitations: number | null;
  totalActiveEnrollees: number | null;
  totalInactiveEnrollees: number | null;
  totalFlaggedClaimsAmount: number | null;
  totalPaidClaimsAmount: number | null;
  totalPendingClaims: number | null;
  totalPendingClaimsAmount: number | null;
  totalClaimOfficerApprovedClaims: number | null;
  totalClaimOfficerHODApprovedClaims: number | null;
  totalClaimReviewerApprovedClaims: number | null;
  totalClaimReviewerHODApprovedClaims: number | null;
  totalClaimAuditApprovedClaims: number | null;
  totalClaimAuditHODApprovedClaims: number | null;
  totalClaimAdminApprovedClaims: number | null;
  totalClaimFinanceApprovedClaims: number | null;
  totalClaimAccountApprovedClaims: number | null;
  totalClaimOfficerApprovedClaimsAmount: number | null;
  totalClaimOfficerHODApprovedClaimsAmount: number | null;
  totalClaimReviewerApprovedClaimsAmount: number | null;
  totalClaimReviewerHODApprovedClaimsAmount: number | null;
  totalClaimAuditApprovedClaimsAmount: number | null;
  totalClaimAuditHODApprovedClaimsAmount: number | null;
  totalClaimAdminApprovedClaimsAmount: number | null;
  totalClaimFinanceApprovedClaimsAmount: number | null;
  totalClaimAccountApprovedClaimsAmount: number | null;
  totalClaimOfficerRejectedClaims: number | null;
  totalClaimOfficerHODRejectedClaims: number | null;
  totalClaimReviewerRejectedClaims: number | null;
  totalClaimReviewerHODRejectedClaims: number | null;
  totalClaimAuditRejectedClaims: number | null;
  totalClaimAuditHODRejectedClaims: number | null;
  totalClaimAdminRejectedClaims: number | null;
  totalClaimFinanceRejectedClaims: number | null;
  totalClaimAccountRejectedClaims: number | null;
  totalClaimOfficerRejectedClaimsAmount: number | null;
  totalClaimOfficerHODRejectedClaimsAmount: number | null;
  totalClaimReviewerRejectedClaimsAmount: number | null;
  totalClaimReviewerHODRejectedClaimsAmount: number | null;
  totalClaimAuditRejectedClaimsAmount: number | null;
  totalClaimAuditHODRejectedClaimsAmount: number | null;
  totalClaimAdminRejectedClaimsAmount: number | null;
  totalClaimFinanceRejectedClaimsAmount: number | null;
  totalClaimAccountRejectedClaimsAmount: number | null;
  totalClaimOfficerPendingClaims: number | null;
  totalClaimOfficerHODPendingClaims: number | null;
  totalClaimReviewerPendingClaims: number | null;
  totalClaimReviewerHODPendingClaims: number | null;
  totalClaimAuditPendingClaims: number | null;
  totalClaimAuditHODPendingClaims: number | null;
  totalClaimAdminPendingClaims: number | null;
  totalClaimFinancePendingClaims: number | null;
  totalClaimAccountPendingClaims: number | null;
  totalClaimOfficerPendingClaimsAmount: number | null;
  totalClaimOfficerHODPendingClaimsAmount: number | null;
  totalClaimReviewerPendingClaimsAmount: number | null;
  totalClaimReviewerHODPendingClaimsAmount: number | null;
  totalClaimAuditPendingClaimsAmount: number | null;
  totalClaimAuditHODPendingClaimsAmount: number | null;
  totalClaimAdminPendingClaimsAmount: number | null;
  totalClaimFinancePendingClaimsAmount: number | null;
  totalClaimAccountPendingClaimsAmount: number | null;
  totalClaimConfirmationPendingClaims: number | null;
  totalClaimConfirmationPendingClaimsAmount: number | null;
  totalClaimOfficerFlaggedClaims: number | null;
  totalClaimOfficerHODFlaggedClaims: number | null;
  totalClaimReviewerFlaggedClaims: number | null;
  totalClaimReviewerHODFlaggedClaims: number | null;
  totalClaimAuditFlaggedClaims: number | null;
  totalClaimAuditHODFlaggedClaims: number | null;
  totalClaimAdminFlaggedClaims: number | null;
  totalClaimFinanceFlaggedClaims: number | null;
  totalClaimAccountFlaggedClaims: number | null;
  totalClaimOfficerFlaggedClaimsAmount: number | null;
  totalClaimOfficerHODFlaggedClaimsAmount: number | null;
  totalClaimReviewerFlaggedClaimsAmount: number | null;
  totalClaimReviewerHODFlaggedClaimsAmount: number | null;
  totalClaimAuditFlaggedClaimsAmount: number | null;
  totalClaimAuditHODFlaggedClaimsAmount: number | null;
  totalClaimAdminFlaggedClaimsAmount: number | null;
  totalClaimFinanceFlaggedClaimsAmount: number | null;
  totalClaimAccountFlaggedClaimsAmount: number | null;
  totalClaimConfirmationFlaggedClaims: number | null;
  totalClaimConfirmationFlaggedClaimsAmount: number | null;
  totalClaimOfficerStaffCount: number | null;
  totalClaimOfficerHODStaffCount: number | null;
  totalClaimReviewerStaffCount: number | null;
  totalClaimReviewerHODStaffCount: number | null;
  totalClaimAuditStaffCount: number | null;
  totalClaimAuditHODStaffCount: number | null;
  totalClaimAdminStaffCount: number | null;
  totalClaimFinanceStaffCount: number | null;
  totalClaimAccountStaffCount: number | null;
  totalClaimConfirmationStaffCount: number | null;
}

export interface GetHmoAnalyticsSummary_getHmosSummary_automaticAndManualData_byRoles {
  __typename: "SummaryCount";
  name: string | null;
  count: number | null;
  manualCount: number | null;
}

export interface GetHmoAnalyticsSummary_getHmosSummary_automaticAndManualData_byStaffs {
  __typename: "SummaryCount";
  name: string | null;
  count: number | null;
  type: string | null;
  manualCount: number | null;
}

export interface GetHmoAnalyticsSummary_getHmosSummary_automaticAndManualData {
  __typename: "AutomaticAndManualHmoClaimsData";
  totalAutomated: number | null;
  totalManual: number | null;
  byRoles: GetHmoAnalyticsSummary_getHmosSummary_automaticAndManualData_byRoles[] | null;
  byStaffs: GetHmoAnalyticsSummary_getHmosSummary_automaticAndManualData_byStaffs[] | null;
}

export interface GetHmoAnalyticsSummary_getHmosSummary {
  __typename: "HmosSummary";
  groupedData: GetHmoAnalyticsSummary_getHmosSummary_groupedData[] | null;
  automaticAndManualData: GetHmoAnalyticsSummary_getHmosSummary_automaticAndManualData | null;
}

export interface GetHmoAnalyticsSummary {
  getHmosSummary: GetHmoAnalyticsSummary_getHmosSummary;
}

export interface GetHmoAnalyticsSummaryVariables {
  filter?: HmosAnalyticsFilter | null;
}
