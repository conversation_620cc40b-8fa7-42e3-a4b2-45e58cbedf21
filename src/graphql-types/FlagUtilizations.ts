/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: FlagUtilizations
// ====================================================

export interface FlagUtilizations_flagUtilizations_flags {
  __typename: "FlagDto";
  flag: string | null;
}

export interface FlagUtilizations_flagUtilizations {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  flags: FlagUtilizations_flagUtilizations_flags[] | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
}

export interface FlagUtilizations {
  flagUtilizations: FlagUtilizations_flagUtilizations[];
}

export interface FlagUtilizationsVariables {
  utilizationIds: string[];
  flag: string;
  unset: boolean;
}
