/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DefaultPatientAccessType } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdatePatientLookupMode
// ====================================================

export interface UpdatePatientLookupMode_updatePatientAccessType {
  __typename: "FacilityPreferenceModel";
  id: string;
  patientAccessType: DefaultPatientAccessType | null;
  hospitalId: string;
}

export interface UpdatePatientLookupMode {
  updatePatientAccessType: UpdatePatientLookupMode_updatePatientAccessType;
}

export interface UpdatePatientLookupModeVariables {
  mode: DefaultPatientAccessType;
}
