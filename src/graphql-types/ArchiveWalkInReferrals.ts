/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ArchiveWalkInReferrals
// ====================================================

export interface ArchiveWalkInReferrals_archiveWalkInReferrals_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface ArchiveWalkInReferrals_archiveWalkInReferrals {
  __typename: "WalkInReferralModel";
  id: string;
  referralDateTime: any | null;
  referredBy: string | null;
  referralReason: string | null;
  concealReferralReason: boolean | null;
  referralFacilityName: string | null;
  referralFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: ArchiveWalkInReferrals_archiveWalkInReferrals_patientInformation | null;
}

export interface ArchiveWalkInReferrals {
  archiveWalkInReferrals: ArchiveWalkInReferrals_archiveWalkInReferrals[];
}

export interface ArchiveWalkInReferralsVariables {
  ids: string[];
  archive?: boolean | null;
}
