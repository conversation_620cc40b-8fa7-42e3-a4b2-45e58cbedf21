/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AdmissionAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetTransferByFacilityData
// ====================================================

export interface GetTransferByFacilityData_getTransferByFacilityData {
  __typename: "CategoryDataResponse";
  category: string | null;
  count: number | null;
  name: number | null;
}

export interface GetTransferByFacilityData {
  getTransferByFacilityData: GetTransferByFacilityData_getTransferByFacilityData[];
}

export interface GetTransferByFacilityDataVariables {
  filter?: AdmissionAnalyticsFilter | null;
}
