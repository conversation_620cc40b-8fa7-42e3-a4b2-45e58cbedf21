/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetFinanceDataSummary
// ====================================================

export interface GetFinanceDataSummary_getFinanceDataSymmary {
  __typename: "FinanceSymmaryResponse";
  name: number | null;
  totalAmountDue: number | null;
  totalAmount: number | null;
  totalAmountOutstanding: number | null;
  totalAmountPaid: number | null;
  totalDiscountAmount: number | null;
  totalRevenue: number | null;
}

export interface GetFinanceDataSummary {
  getFinanceDataSymmary: GetFinanceDataSummary_getFinanceDataSymmary;
}

export interface GetFinanceDataSummaryVariables {
  filter: FinanceAnalyticsFilter;
}
