/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DefaultPatientAccessType, CommissionPayer, PayoutCommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL fragment: FacilityPreference
// ====================================================

export interface FacilityPreference_welcomeMailTemplate {
  __typename: "MailTemplate";
  body: string | null;
  subject: string | null;
}

export interface FacilityPreference_mandatoryFields {
  __typename: "MandatoryFields";
  admission: string[] | null;
  allergy: string[] | null;
  consultation: string[] | null;
  antenatal: string[] | null;
  immunization: string[] | null;
  medication: string[] | null;
  medicationDetails: string[] | null;
  procedure: string[] | null;
  bloodTransfusion: string[] | null;
  dischargePatient: string[] | null;
  transferPatient: string[] | null;
  admissionInput: string[] | null;
  admissionOutput: string[] | null;
  admissionLine: string[] | null;
  treatmentPlan: string[] | null;
  vitals: string[] | null;
  anthropometry: string[] | null;
  bloodGlucose: string[] | null;
  bloodPressure: string[] | null;
  pain: string[] | null;
  respiratoryRate: string[] | null;
  temperature: string[] | null;
  urineDipstick: string[] | null;
  visualAcuity: string[] | null;
  investigation: string[] | null;
  radiologyExam: string[] | null;
  postnatal: string[] | null;
  labourAndDelivery: string[] | null;
  nextOfKin: string[] | null;
  dependents: string[] | null;
  medicalReport: string[] | null;
  admissionNotes: string[] | null;
  nursingServices: string[] | null;
  oncology: string[] | null;
  requestProcedure: string[] | null;
  laboratory: string[] | null;
  radiology: string[] | null;
  postOperationChecklist: string[] | null;
  preChemoEducation: string[] | null;
  cancerScreening: string[] | null;
}

export interface FacilityPreference_enrollmentAgencyPreferences {
  __typename: "EnrollmentAgencyPreferencesDto";
  administrationAgency: string | null;
  agencies: string[] | null;
}

export interface FacilityPreference_fieldOfficers {
  __typename: "FieldOfficerResponseDto";
  profileId: string | null;
  administrationAgency: string | null;
  enrollmentAgency: string | null;
  accountNumber: string | null;
  accountName: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface FacilityPreference_enrolleeReferrals_phoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface FacilityPreference_enrolleeReferrals {
  __typename: "EnrolleeReferralResponseDto";
  name: string | null;
  referrerCode: string | null;
  accountNumber: string | null;
  accountName: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  email: string | null;
  status: string | null;
  phoneNumber: FacilityPreference_enrolleeReferrals_phoneNumber | null;
}

export interface FacilityPreference_enrolleeSponsorAssigments_sponsorPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface FacilityPreference_enrolleeSponsorAssigments {
  __typename: "SponsorResponseDto";
  ref: string | null;
  sponsorName: string | null;
  sponsorType: string | null;
  sponsorLives: string | null;
  agencyLives: string | null;
  amountDue: string | null;
  paymentFrequency: string | null;
  nextRenewalDate: any | null;
  renewalCount: string | null;
  paymentStatus: string | null;
  paymentDateTime: any | null;
  sponsoredPremiumPerLife: string | null;
  totalSponsoredPremium: string | null;
  status: string | null;
  percentageCovered: string | null;
  sponsorEmailAddress: string | null;
  sponsorPhoneNumber: FacilityPreference_enrolleeSponsorAssigments_sponsorPhoneNumber | null;
}

export interface FacilityPreference_enrolleeCapitionAmountByPlanType {
  __typename: "CapitionAmountByPlanType";
  planTypeId: string | null;
  amount: number | null;
}

export interface FacilityPreference {
  __typename: "FacilityPreferenceModel";
  id: string;
  welcomeMailTemplate: FacilityPreference_welcomeMailTemplate | null;
  mandatoryFields: FacilityPreference_mandatoryFields | null;
  hospitalId: string;
  lastModifierName: string | null;
  updatedDate: any;
  patientAccessType: DefaultPatientAccessType | null;
  radiologyExamSource: string | null;
  procedureTypeSource: string | null;
  radiologyContrastConfirmation: boolean | null;
  dashboardColourMode: boolean | null;
  generateFileNumber: boolean | null;
  hospitalShortName: string | null;
  laboratoryTestSource: string | null;
  retainershipSource: string | null;
  rolesServiceDetailsIsHidden: string[] | null;
  showServiceDetails: boolean | null;
  commissionPayer: CommissionPayer | null;
  outPatientLink: string | null;
  inPatientLink: string | null;
  receiptSize: string | null;
  useHQFacilityInventory: boolean | null;
  useHQFacilityTariffs: boolean | null;
  inventoryClass: string | null;
  hmoSingleVisitPACode: boolean | null;
  enableBusinessRulePreventSubmit: boolean | null;
  customPaFormatType: boolean | null;
  registrationFee: number | null;
  enrolleeCapitationAmount: number | null;
  autoProcessClaims: boolean | null;
  autoProcessPreauthorizations: boolean | null;
  enrollmentAgencyPreferences: FacilityPreference_enrollmentAgencyPreferences[] | null;
  enrolleeSponsors: string[] | null;
  fieldOfficers: FacilityPreference_fieldOfficers[] | null;
  enrolleeReferrals: FacilityPreference_enrolleeReferrals[] | null;
  enrolleeSponsorAssigments: FacilityPreference_enrolleeSponsorAssigments[] | null;
  enrolleeCapitationAmountPerPlan: boolean | null;
  payoutCommissionPayer: PayoutCommissionPayer | null;
  enrolleeCapitionAmountByPlanType: FacilityPreference_enrolleeCapitionAmountByPlanType[] | null;
}
