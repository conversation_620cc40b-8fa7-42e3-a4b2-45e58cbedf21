/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { StocksAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetConsumableStatusData
// ====================================================

export interface GetConsumableStatusData_getConsumableStatusData_summary {
  __typename: "StockStatusData";
  name: number | null;
  totalConsumableConsumed: number | null;
  totalConsumableRemaining: number | null;
  totalConsumablePurchased: number | null;
  totalConsumableDamaged: number | null;
  totalConsumableExpired: number | null;
  totalRemainingCostAmount: number | null;
  totalRemainingSalesAmount: number | null;
  totalPurchasedCostAmount: number | null;
  totalPurchasedSalesAmount: number | null;
  totalDispensedCostAmount: number | null;
  totalDispensedSalesAmount: number | null;
  totalDamagedCostAmount: number | null;
  totalDamagedSalesAmount: number | null;
  totalExpiredSalesAmount: number | null;
  totalExpiredCostAmount: number | null;
}

export interface GetConsumableStatusData_getConsumableStatusData_details {
  __typename: "StockStatusDetails";
  dateAdded: string | null;
  itemName: string | null;
  addedBy: string | null;
  totalQuantityPurchased: number | null;
  totalQuantityDispensed: number | null;
  totalQuantityRemaining: number | null;
  totalQuantityExpired: number | null;
  totalQuantityDamaged: number | null;
  supplier: string | null;
  totalCosts: number | null;
  totalSales: number | null;
  totalAverageCost: number | null;
  totalExpiredAmount: number | null;
  totalDamagedAmount: number | null;
  restockLevel: number | null;
}

export interface GetConsumableStatusData_getConsumableStatusData {
  __typename: "StockStatusResponse";
  summary: GetConsumableStatusData_getConsumableStatusData_summary | null;
  details: GetConsumableStatusData_getConsumableStatusData_details[] | null;
}

export interface GetConsumableStatusData {
  getConsumableStatusData: GetConsumableStatusData_getConsumableStatusData;
}

export interface GetConsumableStatusDataVariables {
  filter?: StocksAnalyticsFilter | null;
}
