/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NewPricesInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddHospitalPrice
// ====================================================

export interface AddHospitalPrice_addPrice {
  __typename: "PricesModel";
  id: string;
  name: string | null;
  code: string | null;
  category: string | null;
  price: string | null;
  aliasCode: string | null;
  serviceType: string | null;
  providerCode: string | null;
  aliasName: string | null;
}

export interface AddHospitalPrice {
  addPrice: AddHospitalPrice_addPrice;
}

export interface AddHospitalPriceVariables {
  input: NewPricesInput;
}
