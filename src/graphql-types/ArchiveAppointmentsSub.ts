/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NextAppointmentEntities } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: ArchiveAppointmentsSub
// ====================================================

export interface ArchiveAppointmentsSub_AppointmentsArchived_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface ArchiveAppointmentsSub_AppointmentsArchived_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface ArchiveAppointmentsSub_AppointmentsArchived_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface ArchiveAppointmentsSub_AppointmentsArchived_specialist {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  type: string;
  fullName: string;
}

export interface ArchiveAppointmentsSub_AppointmentsArchived_serviceDetails {
  __typename: "ServiceDetailInputType";
  type: string | null;
  name: string | null;
}

export interface ArchiveAppointmentsSub_AppointmentsArchived {
  __typename: "OrganisationAppointmentModel";
  id: string;
  hospital: ArchiveAppointmentsSub_AppointmentsArchived_hospital | null;
  profile: ArchiveAppointmentsSub_AppointmentsArchived_profile | null;
  patientInformation: ArchiveAppointmentsSub_AppointmentsArchived_patientInformation | null;
  specialist: ArchiveAppointmentsSub_AppointmentsArchived_specialist | null;
  createdDate: any;
  updatedDate: any;
  status: string | null;
  category: string | null;
  liveSessionUrl: string | null;
  rank: string | null;
  reason: string | null;
  appointmentDateTime: any | null;
  startDateTime: any | null;
  endDateTime: any | null;
  specialty: string | null;
  role: string | null;
  paymentType: string | null;
  serviceDetails: ArchiveAppointmentsSub_AppointmentsArchived_serviceDetails[] | null;
  patientType: string | null;
  duration: string | null;
  confirmedBy: string | null;
  deliveryMethod: string | null;
  urgency: string | null;
  additionalNote: string | null;
  recordType: NextAppointmentEntities | null;
  documentUrl: string[] | null;
}

export interface ArchiveAppointmentsSub {
  AppointmentsArchived: ArchiveAppointmentsSub_AppointmentsArchived[];
}

export interface ArchiveAppointmentsSubVariables {
  profileId: string;
  hospitalId: string;
}
