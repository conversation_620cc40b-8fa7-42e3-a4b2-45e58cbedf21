/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Currency } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPaymentDeposit
// ====================================================

export interface GetPaymentDeposit_paymentDeposit_collectedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface GetPaymentDeposit_paymentDeposit_withdrawnBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface GetPaymentDeposit_paymentDeposit_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface GetPaymentDeposit_paymentDeposit {
  __typename: "PaymentDepositModel";
  id: string;
  depositDate: any;
  depositMethod: string | null;
  currency: Currency;
  profileId: string;
  hospitalId: string;
  amountDeposited: number;
  amountUsed: number;
  additionalNote: string | null;
  collectedById: string | null;
  creatorId: string;
  lastModifierId: string | null;
  description: string | null;
  createdDate: any;
  updatedDate: any | null;
  autoGenerated: boolean | null;
  amountRefunded: number | null;
  finalDepositBalance: number | null;
  initialDepositBalance: number | null;
  isManualRefund: boolean | null;
  refundDate: any | null;
  refundedBy: string | null;
  creatorName: string;
  lastModifierName: string | null;
  collectedBy: GetPaymentDeposit_paymentDeposit_collectedBy | null;
  withdrawnBy: GetPaymentDeposit_paymentDeposit_withdrawnBy | null;
  hospital: GetPaymentDeposit_paymentDeposit_hospital;
}

export interface GetPaymentDeposit {
  paymentDeposit: GetPaymentDeposit_paymentDeposit;
}

export interface GetPaymentDepositVariables {
  id: string;
  clinifyId: string;
}
