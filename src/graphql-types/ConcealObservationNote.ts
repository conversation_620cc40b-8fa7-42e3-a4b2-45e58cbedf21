/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealObservationNote
// ====================================================

export interface ConcealObservationNote_concealObservationNote {
  __typename: "TreatmentPlanModel";
  id: string;
  treatmentPlan: string | null;
  conceal: boolean | null;
  patientAdmitted: string | null;
  concealObservationNote: boolean | null;
  observationNote: string | null;
  admissionConsent: string | null;
  adverseEffectsFollowingTreatment: string | null;
  stateEffects: string | null;
  adverseEffectsInvestigated: string | null;
  outcomeOfInvestigation: string | null;
  treatmentGiven: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  treatmentStatus: string | null;
}

export interface ConcealObservationNote {
  concealObservationNote: ConcealObservationNote_concealObservationNote;
}

export interface ConcealObservationNoteVariables {
  id: string;
  concealStatus: boolean;
}
