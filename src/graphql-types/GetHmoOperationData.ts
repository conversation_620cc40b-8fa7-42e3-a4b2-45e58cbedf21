/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmosAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHmoOperationData
// ====================================================

export interface GetHmoOperationData_getHmoOperationData_providerReports {
  __typename: "ProviderReportData";
  providerName: string | null;
  numberOfClaims: number | null;
  totalSubmittedAmount: number | null;
  totalPaidAmount: number | null;
}

export interface GetHmoOperationData_getHmoOperationData_topProceduresByCost {
  __typename: "TopProceduresByCostReportData";
  procedureName: string | null;
  numberOfClaims: number | null;
  totalSubmittedAmount: number | null;
}

export interface GetHmoOperationData_getHmoOperationData_topDiagnosis {
  __typename: "TopDiagnosisReportData";
  diagnosis: string | null;
  numberOfClaims: number | null;
  totalSubmittedAmount: number | null;
}

export interface GetHmoOperationData_getHmoOperationData_topFeeForServiceProviders {
  __typename: "ProviderReportData";
  providerName: string | null;
  numberOfClaims: number | null;
  totalSubmittedAmount: number | null;
  totalPaidAmount: number | null;
}

export interface GetHmoOperationData_getHmoOperationData_claimVettedData {
  __typename: "ProviderReportData";
  providerName: string | null;
  numberOfClaims: number | null;
  totalSubmittedAmount: number | null;
  totalPaidAmount: number | null;
}

export interface GetHmoOperationData_getHmoOperationData {
  __typename: "OperationReportData";
  totalProvidersSubmittedClaims: number | null;
  totalNumberOfClaims: number | null;
  totalSubmittedClaimsAmount: number | null;
  totalPaidClaimsAmount: number | null;
  providerReports: GetHmoOperationData_getHmoOperationData_providerReports[] | null;
  topProceduresByCost: GetHmoOperationData_getHmoOperationData_topProceduresByCost[] | null;
  topDiagnosis: GetHmoOperationData_getHmoOperationData_topDiagnosis[] | null;
  topFeeForServiceProviders: GetHmoOperationData_getHmoOperationData_topFeeForServiceProviders[] | null;
  claimVettedData: GetHmoOperationData_getHmoOperationData_claimVettedData[] | null;
}

export interface GetHmoOperationData {
  getHmoOperationData: GetHmoOperationData_getHmoOperationData;
}

export interface GetHmoOperationDataVariables {
  filter: HmosAnalyticsFilter;
}
