/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: DeleteEnrollmentAgents
// ====================================================

export interface DeleteEnrollmentAgents_deleteEnrollmentAgents_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface DeleteEnrollmentAgents_deleteEnrollmentAgents_agency {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
  agencyType: string | null;
  isTpa: boolean | null;
}

export interface DeleteEnrollmentAgents_deleteEnrollmentAgents_tpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
  isTpa: boolean | null;
}

export interface DeleteEnrollmentAgents_deleteEnrollmentAgents {
  __typename: "EnrollmentAgentModel";
  id: string;
  profile: DeleteEnrollmentAgents_deleteEnrollmentAgents_profile | null;
  agency: DeleteEnrollmentAgents_deleteEnrollmentAgents_agency | null;
  tpaNonTpa: DeleteEnrollmentAgents_deleteEnrollmentAgents_tpaNonTpa | null;
  accountNumber: string | null;
  accountName: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface DeleteEnrollmentAgents {
  deleteEnrollmentAgents: DeleteEnrollmentAgents_deleteEnrollmentAgents[];
}

export interface DeleteEnrollmentAgentsVariables {
  ids: string[];
}
