/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: EnrollmentAgentUpdated
// ====================================================

export interface EnrollmentAgentUpdated_EnrollmentAgentUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface EnrollmentAgentUpdated_EnrollmentAgentUpdated_agency {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
  agencyType: string | null;
  isTpa: boolean | null;
}

export interface EnrollmentAgentUpdated_EnrollmentAgentUpdated_tpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
  isTpa: boolean | null;
}

export interface EnrollmentAgentUpdated_EnrollmentAgentUpdated {
  __typename: "EnrollmentAgentModel";
  id: string;
  profile: EnrollmentAgentUpdated_EnrollmentAgentUpdated_profile | null;
  agency: EnrollmentAgentUpdated_EnrollmentAgentUpdated_agency | null;
  tpaNonTpa: EnrollmentAgentUpdated_EnrollmentAgentUpdated_tpaNonTpa | null;
  accountNumber: string | null;
  accountName: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface EnrollmentAgentUpdated {
  EnrollmentAgentUpdated: EnrollmentAgentUpdated_EnrollmentAgentUpdated;
}

export interface EnrollmentAgentUpdatedVariables {
  hospitalId: string;
}
