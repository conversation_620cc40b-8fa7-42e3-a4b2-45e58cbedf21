/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetUrineDipstickInfos
// ====================================================

export interface GetUrineDipstickInfos_getUrineDipstickInfos {
  __typename: "UrineDipstickModel";
  id: string;
  readingDateTime: any | null;
  blood: string | null;
  glucose: string | null;
  ketones: string | null;
  ph: string | null;
  protein: string | null;
  nitrites: string | null;
  leucocyte: string | null;
  urobilinogen: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isPhCritical: boolean | null;
}

export interface GetUrineDipstickInfos {
  getUrineDipstickInfos: GetUrineDipstickInfos_getUrineDipstickInfos[];
}

export interface GetUrineDipstickInfosVariables {
  parentRecordId: string;
  clinifyId: string;
}
