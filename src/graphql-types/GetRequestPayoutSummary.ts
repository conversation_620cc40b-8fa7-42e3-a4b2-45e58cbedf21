/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PayoutFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetRequestPayoutSummary
// ====================================================

export interface GetRequestPayoutSummary_getRequestPayoutSummary {
  __typename: "RequestPayoutSummaryResponse";
  totalRequestedPayouts: number;
  totalPendingRequestPayouts: number;
  totalApprovedRequestPayouts: number;
  totalRejectedRequestPayouts: number;
  totalRequestedPayoutAmount: number | null;
  totalPendingRequestedPayoutAmount: number | null;
  totalApprovedRequestedPayoutAmount: number | null;
  totalRejectedRequestedPayoutAmount: number | null;
}

export interface GetRequestPayoutSummary {
  getRequestPayoutSummary: GetRequestPayoutSummary_getRequestPayoutSummary;
}

export interface GetRequestPayoutSummaryVariables {
  options: PayoutFilterInput;
}
