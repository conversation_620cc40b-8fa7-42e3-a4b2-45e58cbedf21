/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicationOptionType } from "./globalTypes";

// ====================================================
// GraphQL query operation: FetchMedicationBundle
// ====================================================

export interface FetchMedicationBundle_medicationBundle_medicationBundleItems_medicationConsumables {
  __typename: "MedicationConsumables";
  drugInventoryId: string | null;
  name: string | null;
  quantity: string | null;
  unitPrice: number | null;
  inventoryClass: string | null;
}

export interface FetchMedicationBundle_medicationBundle_medicationBundleItems_priceDetails {
  __typename: "MedPriceDetailInputType";
  type: string | null;
  name: string | null;
  pricePerUnit: string | null;
  patientType: string | null;
  paymentType: string | null;
  quantity: string | null;
}

export interface FetchMedicationBundle_medicationBundle_medicationBundleItems_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface FetchMedicationBundle_medicationBundle_medicationBundleItems {
  __typename: "MedicationBundleItemModel";
  id: string;
  administrationMethod: string | null;
  bank: string | null;
  bundleDate: any;
  createdDate: any;
  creatorId: string | null;
  creatorName: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  duration: string | null;
  frequency: string | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  medicationCategory: string | null;
  drugInventoryId: string | null;
  inventoryClass: string | null;
  medicationConsumables: FetchMedicationBundle_medicationBundle_medicationBundleItems_medicationConsumables[] | null;
  priceDetails: FetchMedicationBundle_medicationBundle_medicationBundleItems_priceDetails | null;
  provider: string | null;
  medicationName: string | null;
  option: MedicationOptionType | null;
  purpose: string | null;
  quantity: string | null;
  unitPrice: string | null;
  updatedDate: any;
  prescriptionNote: string | null;
  medicationType: string | null;
  diagnosis: FetchMedicationBundle_medicationBundle_medicationBundleItems_diagnosis[] | null;
}

export interface FetchMedicationBundle_medicationBundle {
  __typename: "MedicationBundleModel";
  id: string;
  bundleName: string;
  clinifyId: string;
  createdDate: any;
  updatedDate: any;
  creatorId: string | null;
  creatorName: string | null;
  documentUrl: string[] | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  additionalNote: string | null;
  medicationBundleItems: FetchMedicationBundle_medicationBundle_medicationBundleItems[] | null;
}

export interface FetchMedicationBundle {
  medicationBundle: FetchMedicationBundle_medicationBundle;
}

export interface FetchMedicationBundleVariables {
  id: string;
}
