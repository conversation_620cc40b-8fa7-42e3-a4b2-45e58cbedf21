/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetFamilyRevenueSummary
// ====================================================

export interface GetFamilyRevenueSummary_getFamilyRevenueDataSummary {
  __typename: "FinanceDataSummaryResponse";
  totalRevenue: number | null;
  totalAmountDue: number | null;
  totalAmount: number | null;
  category: string;
  name: number | null;
  totalAmountOutstanding: number | null;
  totalAmountPaid: number | null;
  totalDiscountAmount: number | null;
}

export interface GetFamilyRevenueSummary {
  getFamilyRevenueDataSummary: GetFamilyRevenueSummary_getFamilyRevenueDataSummary[];
}

export interface GetFamilyRevenueSummaryVariables {
  filter: FinanceAnalyticsFilter;
}
