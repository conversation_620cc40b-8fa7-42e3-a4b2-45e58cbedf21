/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: NextOfKinAddedSubs
// ====================================================

export interface NextOfKinAddedSubs_NextOfKinAdded_phoneNumber {
  __typename: "PhoneNumberFields";
  countryName: string | null;
  countryCode: string | null;
  value: string | null;
}

export interface NextOfKinAddedSubs_NextOfKinAdded_phoneNumberAlt {
  __typename: "PhoneNumberFields";
  countryName: string | null;
  countryCode: string | null;
  value: string | null;
}

export interface NextOfKinAddedSubs_NextOfKinAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface NextOfKinAddedSubs_NextOfKinAdded {
  __typename: "NextOfKinModel";
  id: string;
  firstName: string | null;
  lastName: string | null;
  gender: Gender | null;
  title: string | null;
  middleName: string | null;
  bloodGroup: string | null;
  genoType: string | null;
  phoneNumber: NextOfKinAddedSubs_NextOfKinAdded_phoneNumber | null;
  phoneNumberAlt: NextOfKinAddedSubs_NextOfKinAdded_phoneNumberAlt | null;
  email: string | null;
  emailAlt: string | null;
  relationship: string | null;
  occupation: string | null;
  address: string | null;
  profile: NextOfKinAddedSubs_NextOfKinAdded_profile | null;
}

export interface NextOfKinAddedSubs {
  NextOfKinAdded: NextOfKinAddedSubs_NextOfKinAdded;
}

export interface NextOfKinAddedSubsVariables {
  profileId: string;
}
