/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AdmissionAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetAdmissionSummary
// ====================================================

export interface GetAdmissionSummary_getAdmissionSummary_groupedData_admissions {
  __typename: "AdmissionSummaryCount";
  hospitalId: string | null;
  hospitalName: string | null;
  totalAdmissions: number | null;
  averageLengthOfStay: number | null;
}

export interface GetAdmissionSummary_getAdmissionSummary_groupedData_beds {
  __typename: "AdmissionSummaryCount";
  hospitalId: string | null;
  hospitalName: string | null;
  totalBedAvailable: number | null;
  totalBedOccupied: number | null;
  totalBeds: number | null;
}

export interface GetAdmissionSummary_getAdmissionSummary_groupedData_totalDischarge {
  __typename: "AdmissionSummaryCount";
  hospitalId: string | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetAdmissionSummary_getAdmissionSummary_groupedData_totalTransfers {
  __typename: "AdmissionSummaryCount";
  hospitalId: string | null;
  hospitalName: string | null;
  count: number | null;
}

export interface GetAdmissionSummary_getAdmissionSummary_groupedData {
  __typename: "GroupedAdmissionSummaryType";
  admissions: GetAdmissionSummary_getAdmissionSummary_groupedData_admissions[] | null;
  beds: GetAdmissionSummary_getAdmissionSummary_groupedData_beds[] | null;
  totalDischarge: GetAdmissionSummary_getAdmissionSummary_groupedData_totalDischarge[] | null;
  totalTransfers: GetAdmissionSummary_getAdmissionSummary_groupedData_totalTransfers[] | null;
}

export interface GetAdmissionSummary_getAdmissionSummary {
  __typename: "AdmissionSummary";
  totalAdmissions: number | null;
  totalDischarge: number | null;
  averageLengthOfStay: number | null;
  totalTransfers: number | null;
  totalBeds: number | null;
  totalBedOccupied: number | null;
  totalBedAvailable: number | null;
  groupedData: GetAdmissionSummary_getAdmissionSummary_groupedData | null;
}

export interface GetAdmissionSummary {
  getAdmissionSummary: GetAdmissionSummary_getAdmissionSummary;
}

export interface GetAdmissionSummaryVariables {
  filter?: AdmissionAnalyticsFilter | null;
}
