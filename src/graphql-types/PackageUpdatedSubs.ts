/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PackageUpdatedSubs
// ====================================================

export interface PackageUpdatedSubs_PackageUpdated_serviceDetails {
  __typename: "ServiceDetailsFields";
  serviceType: string | null;
  serviceName: string | null;
  /**
   * Extra information about the service in json format
   */
  extraInformation: string | null;
}

export interface PackageUpdatedSubs_PackageUpdated {
  __typename: "PackageModel";
  id: string;
  packageDate: any;
  name: string;
  price: string | null;
  gender: string;
  serviceDetails: PackageUpdatedSubs_PackageUpdated_serviceDetails[] | null;
  createdDate: any;
  updatedDate: any;
}

export interface PackageUpdatedSubs {
  PackageUpdated: PackageUpdatedSubs_PackageUpdated;
}

export interface PackageUpdatedSubsVariables {
  hospitalId: string;
}
