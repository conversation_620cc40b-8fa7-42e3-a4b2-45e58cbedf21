/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: DischargePatientAddedSubs
// ====================================================

export interface DischargePatientAddedSubs_DischargePatientAdded_dischargeDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface DischargePatientAddedSubs_DischargePatientAdded_admission {
  __typename: "AdmissionModel";
  id: string;
  duration: string | null;
}

export interface DischargePatientAddedSubs_DischargePatientAdded {
  __typename: "DischargePatientModel";
  id: string;
  dischargeDate: any | null;
  dischargedStatus: string | null;
  deathDateTime: any | null;
  deathCause: string | null;
  deathLocation: string | null;
  deathCertificateIssued: string | null;
  dischargeSummary: string | null;
  dischargedBy: string | null;
  dischargedBySignature: string | null;
  dischargedBySignatureType: string | null;
  dischargedBySignatureDateTime: any | null;
  dischargeDiagnosis: DischargePatientAddedSubs_DischargePatientAdded_dischargeDiagnosis[] | null;
  causeOfDeath: string | null;
  concealDischargeSummary: boolean | null;
  followupDate: any | null;
  dischargeLocation: string | null;
  dischargeAddress: string | null;
  admissionId: string | null;
  admission: DischargePatientAddedSubs_DischargePatientAdded_admission | null;
}

export interface DischargePatientAddedSubs {
  DischargePatientAdded: DischargePatientAddedSubs_DischargePatientAdded;
}

export interface DischargePatientAddedSubsVariables {
  profileId: string;
}
