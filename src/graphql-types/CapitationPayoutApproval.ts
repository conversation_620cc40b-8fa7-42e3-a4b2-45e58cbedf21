/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FundTransactionStatus, PayoutCommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: CapitationPayoutApproval
// ====================================================

export interface CapitationPayoutApproval_capitationAuditApproval_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  clinifyId: string;
  title: string | null;
}

export interface CapitationPayoutApproval_capitationAuditApproval_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface CapitationPayoutApproval_capitationAuditApproval_auditApproval {
  __typename: "ClaimsAccountApprovalType";
  createdDate: any;
  creatorName: string;
  approvalGroup: string;
  creatorId: string;
}

export interface CapitationPayoutApproval_capitationAuditApproval_hmoPlanType {
  __typename: "HmoPlanTypeModel";
  id: string;
  name: string | null;
}

export interface CapitationPayoutApproval_capitationAuditApproval_detailsByPlanType {
  __typename: "CapitationDetailByPlanType";
  enrolleeCount: number;
  planType: string;
  planTypeName: string;
  totalCapitationAmount: number;
  payoutDecreasePercentage: number | null;
}

export interface CapitationPayoutApproval_capitationAuditApproval {
  __typename: "TransferFundModel";
  id: string;
  createdBy: CapitationPayoutApproval_capitationAuditApproval_createdBy | null;
  amount: number;
  createdDate: any;
  updatedDate: any;
  destinationAccountName: string | null;
  destinationAccountNumber: string | null;
  destinationBankCode: string | null;
  destinationBankName: string | null;
  narration: string | null;
  originatorName: string | null;
  sourceAccountNumber: string | null;
  transferReference: string | null;
  transferStatus: FundTransactionStatus;
  additionalNote: string | null;
  serviceChargeAmount: number | null;
  hospitalId: string | null;
  hmoPlanTypeId: string | null;
  hospital: CapitationPayoutApproval_capitationAuditApproval_hospital | null;
  enrolleeCount: number | null;
  isEnrolleePayout: boolean | null;
  totalCapitationAmount: number | null;
  auditApproval: CapitationPayoutApproval_capitationAuditApproval_auditApproval[] | null;
  hmoPlanType: CapitationPayoutApproval_capitationAuditApproval_hmoPlanType | null;
  detailsByPlanType: CapitationPayoutApproval_capitationAuditApproval_detailsByPlanType[] | null;
  payoutCommissionPayer: PayoutCommissionPayer | null;
  payoutDecreasePercentage: number | null;
  enrolleeCommissionAmount: number | null;
}

export interface CapitationPayoutApproval {
  capitationAuditApproval: CapitationPayoutApproval_capitationAuditApproval[];
}

export interface CapitationPayoutApprovalVariables {
  hospitalIds: string[];
  startDate: string;
  endDate: string;
  status: boolean;
  hmoPlanTypeId?: string | null;
}
