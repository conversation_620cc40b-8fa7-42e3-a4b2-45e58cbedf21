/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { RespiratoryRateVitalFields } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddRespiratoryRate
// ====================================================

export interface AddRespiratoryRate_addRespiratoryRateInfo {
  __typename: "RespiratoryRateModel";
  id: string;
  readingDateTime: any | null;
  reading: string | null;
  oxygenSaturation: string | null;
  spO2Site: string | null;
  O2FlowRate: string | null;
  fIO2: string | null;
  O2Therapy: string | null;
  etco2: string | null;
  etco2Unit: string | null;
  cardiacRythm: string | null;
  rhythm: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
  isO2FlowRateCritical: boolean | null;
  isOxygenSaturationCritical: boolean | null;
  isEtco2Critical: boolean | null;
  isFIO2Critical: boolean | null;
}

export interface AddRespiratoryRate {
  addRespiratoryRateInfo: AddRespiratoryRate_addRespiratoryRateInfo;
}

export interface AddRespiratoryRateVariables {
  input: RespiratoryRateVitalFields;
  parentRecordId: string;
  clinifyId: string;
}
