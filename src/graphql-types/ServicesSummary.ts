/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: ServicesSummary
// ====================================================

export interface ServicesSummary_antenatalsSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_admissionsSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_consultationsSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_proceduresSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_immunizationsSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_medicationsDispensedSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_consumablesDispensedSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_nursingServicesSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_medicalReportsSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_radiologySummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_laboratorySummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_medicationsPrescribedSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_consumablesPrescribedSummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_requestedLaboratorySummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_requestedRadiologySummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_processedLaboratorySummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary_processedRadiologySummary {
  __typename: "ServiceSummaryCount";
  totalAmount: number | null;
  hospitalName: string | null;
  count: number | null;
}

export interface ServicesSummary {
  __typename: "ServicesSummary";
  name: number | null;
  totalAntenatals: number | null;
  totalAntenatalsAmount: number | null;
  totalAdmissions: number | null;
  totalAdmissionsAmount: number | null;
  totalMedications: number | null;
  totalProcedures: number | null;
  totalProceduresAmount: number | null;
  totalConsultations: number | null;
  totalConsultationsAmount: number | null;
  totalMedicationsDispensed: number | null;
  totalMedicationsDispensedAmount: number | null;
  totalConsumablesDispensed: number | null;
  totalRequestedLaboratoryAmount: number | null;
  totalConsumablesDispensedAmount: number | null;
  totalMedicationsPrescribed: number | null;
  totalMedicationsPrescribedAmount: number | null;
  totalConsumablesPrescribed: number | null;
  totalConsumablesPrescribedAmount: number | null;
  totalImmunizations: number | null;
  totalImmunizationsAmount: number | null;
  totalInvestigations: number | null;
  totalRadiology: number | null;
  totalRadiologyAmount: number | null;
  totalLaboratory: number | null;
  totalLaboratoryAmount: number | null;
  totalRequestedLaboratory: number | null;
  totalRequestedRadiologyAmount: number | null;
  totalProcessedLaboratory: number | null;
  totalProcessedLaboratoryAmount: number | null;
  totalProcessedRadiology: number | null;
  totalProcessedRadiologyAmount: number | null;
  totalRequestedRadiology: number | null;
  totalNursingServices: number | null;
  totalNursingServicesAmount: number | null;
  totalMedicalReports: number | null;
  totalMedicalReportsAmount: number | null;
  antenatalsSummary: ServicesSummary_antenatalsSummary[] | null;
  admissionsSummary: ServicesSummary_admissionsSummary[] | null;
  consultationsSummary: ServicesSummary_consultationsSummary[] | null;
  proceduresSummary: ServicesSummary_proceduresSummary[] | null;
  immunizationsSummary: ServicesSummary_immunizationsSummary[] | null;
  medicationsDispensedSummary: ServicesSummary_medicationsDispensedSummary[] | null;
  consumablesDispensedSummary: ServicesSummary_consumablesDispensedSummary[] | null;
  nursingServicesSummary: ServicesSummary_nursingServicesSummary[] | null;
  medicalReportsSummary: ServicesSummary_medicalReportsSummary[] | null;
  radiologySummary: ServicesSummary_radiologySummary[] | null;
  laboratorySummary: ServicesSummary_laboratorySummary[] | null;
  medicationsPrescribedSummary: ServicesSummary_medicationsPrescribedSummary[] | null;
  consumablesPrescribedSummary: ServicesSummary_consumablesPrescribedSummary[] | null;
  requestedLaboratorySummary: ServicesSummary_requestedLaboratorySummary[] | null;
  requestedRadiologySummary: ServicesSummary_requestedRadiologySummary[] | null;
  processedLaboratorySummary: ServicesSummary_processedLaboratorySummary[] | null;
  processedRadiologySummary: ServicesSummary_processedRadiologySummary[] | null;
}
