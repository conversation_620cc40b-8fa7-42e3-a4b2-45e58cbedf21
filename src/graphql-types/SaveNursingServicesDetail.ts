/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NursingServicesDetailsInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: SaveNursingServicesDetail
// ====================================================

export interface SaveNursingServicesDetail_saveNursingServicesDetails_medications {
  __typename: "MedicationDetailType";
  medicationName: string | null;
  prescriptionTime: any | null;
  dosage: string | null;
  dosageUnit: string | null;
}

export interface SaveNursingServicesDetail_saveNursingServicesDetails {
  __typename: "NursingServiceDetailModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  lastModifierId: string | null;
  lastModifierName: string | null;
  creatorId: string | null;
  creatorName: string | null;
  procedureDateTime: any | null;
  procedureType: string | null;
  provider: string | null;
  providerServiceName: string | null;
  priority: string | null;
  category: string | null;
  dateOfBirth: any | null;
  consentGiven: string | null;
  parentGuardianPresent: string | null;
  parentGuardianName: string | null;
  anaesthesiaGiven: string | null;
  anaesthesiaType: string | null;
  vitaminKGiven: string | null;
  castLocation: string | null;
  reasonForCasting: string | null;
  isItARepeatedCasting: string | null;
  hasRadiologicalInvestigationBeenDone: string | null;
  bathing: string | null;
  dressing: string | null;
  eating: string | null;
  mobility: string | null;
  stairClimbing: string | null;
  toiletUse: string | null;
  woundLocation: string | null;
  dressingType: string | null;
  dressingAppearance: string | null;
  dressingIntervention: string | null;
  lastDressingChange: any | null;
  dressingChangeDue: any | null;
  painScore: string | null;
  painDescriptors: string[] | null;
  otherPainDescriptors: string | null;
  signOfInfection: string | null;
  whichEar: string | null;
  observation: string | null;
  informedConsent: string | null;
  method: string | null;
  otherMethods: string | null;
  councelled: string | null;
  typeOfInjectable: string | null;
  typeOfImplant: string | null;
  duration: string | null;
  procedureStartDateTime: any | null;
  procedureEndDateTime: any | null;
  heartRateBefore: string | null;
  heartRateAfter: string | null;
  respiratoryRateBefore: string | null;
  respiratoryRateAfter: string | null;
  oxygenSaturationBefore: string | null;
  oxygenSaturationAfter: string | null;
  medications: SaveNursingServicesDetail_saveNursingServicesDetails_medications[] | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  nursingServiceId: string | null;
  procedureName: string | null;
  location: string | null;
  procedureNote: string | null;
  injectionName: string | null;
  InjectionLocation: string | null;
  injectionPrepared: string | null;
  equipmentsSterilized: string | null;
  medicalHistoryChecked: string | null;
  repeatInjection: string | null;
}

export interface SaveNursingServicesDetail {
  saveNursingServicesDetails: SaveNursingServicesDetail_saveNursingServicesDetails;
}

export interface SaveNursingServicesDetailVariables {
  input: NursingServicesDetailsInput;
  id: string;
  clinifyId: string;
  pin?: string | null;
}
