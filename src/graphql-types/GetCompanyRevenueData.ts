/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetCompanyRevenueData
// ====================================================

export interface GetCompanyRevenueData_getCompanyRevenueData_data {
  __typename: "categoryData";
  name: number | null;
  category: string | null;
  serviceName: string | null;
  totalAmount: number | null;
  totalAmountPaid: number | null;
  totalAmountOutstanding: number | null;
  totalAmountDue: number | null;
  patientFullName: string | null;
  enrolleeId: string | null;
  visitDate: string | null;
  coverageInformationId: string | null;
  companyName: string | null;
  patientPhoneNumber: string | null;
  patientEmailAddress: string | null;
  billId: string | null;
  quantity: string | null;
  raisedBy: string | null;
  bankName: string | null;
  accountNumber: string | null;
  splitPaymentTypes: string | null;
  splitAmountPaid: number[] | null;
  splitBankNames: string | null;
  splitAccountNumbers: string | null;
}

export interface GetCompanyRevenueData_getCompanyRevenueData {
  __typename: "PaymentTypeData";
  data: GetCompanyRevenueData_getCompanyRevenueData_data[] | null;
}

export interface GetCompanyRevenueData {
  getCompanyRevenueData: GetCompanyRevenueData_getCompanyRevenueData;
}

export interface GetCompanyRevenueDataVariables {
  filter?: FinanceAnalyticsFilter | null;
}
