/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPaymentTypeData
// ====================================================

export interface GetPaymentTypeData_getPaymentTypeData_cardSummary {
  __typename: "PaymentSummary";
  paymentType: string | null;
  count: number | null;
}

export interface GetPaymentTypeData_getPaymentTypeData_data {
  __typename: "FinanceData";
  name: number | null;
  category: string | null;
  totalAmount: number | null;
  totalAmountDue: number | null;
  totalAmountOutstanding: number | null;
  totalDiscount: number | null;
  totalAmountPaid: number | null;
  totalRevenue: number | null;
  visitationDate: any | null;
  serviceType: string | null;
  serviceName: string | null;
  quantity: string | null;
  raisedBy: string | null;
  patientName: string | null;
  patientClinifyId: string | null;
  patientPhoneNumber: string | null;
  patientEmailAddress: string | null;
  coverageType: string | null;
  coverageName: string | null;
  enrolleeId: string | null;
}

export interface GetPaymentTypeData_getPaymentTypeData {
  __typename: "PaymentTypeDataResponse";
  cardSummary: GetPaymentTypeData_getPaymentTypeData_cardSummary[] | null;
  data: GetPaymentTypeData_getPaymentTypeData_data[] | null;
}

export interface GetPaymentTypeData {
  getPaymentTypeData: GetPaymentTypeData_getPaymentTypeData;
}

export interface GetPaymentTypeDataVariables {
  filter?: FinanceAnalyticsFilter | null;
}
