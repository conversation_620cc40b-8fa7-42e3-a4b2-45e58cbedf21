/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EnrollmentTpaNonTpaInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddEnrollmentTpaNonTpa
// ====================================================

export interface AddEnrollmentTpaNonTpa_addEnrollmentTpaNonTpa_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface AddEnrollmentTpaNonTpa_addEnrollmentTpaNonTpa_primaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface AddEnrollmentTpaNonTpa_addEnrollmentTpaNonTpa_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface AddEnrollmentTpaNonTpa_addEnrollmentTpaNonTpa_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface AddEnrollmentTpaNonTpa_addEnrollmentTpaNonTpa_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface AddEnrollmentTpaNonTpa_addEnrollmentTpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
  address: string | null;
  isTpa: boolean | null;
  country: string | null;
  state: string | null;
  profile: AddEnrollmentTpaNonTpa_addEnrollmentTpaNonTpa_profile | null;
  localGovernmentArea: string | null;
  primaryPhoneNumber: AddEnrollmentTpaNonTpa_addEnrollmentTpaNonTpa_primaryPhoneNumber | null;
  primaryEmailAddress: string | null;
  secondaryPhoneNumber: AddEnrollmentTpaNonTpa_addEnrollmentTpaNonTpa_secondaryPhoneNumber | null;
  secondaryEmailAddress: string | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: AddEnrollmentTpaNonTpa_addEnrollmentTpaNonTpa_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: AddEnrollmentTpaNonTpa_addEnrollmentTpaNonTpa_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  tpaNumber: string | null;
  tpaCode: string | null;
  accountName: string | null;
  accountNumber: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface AddEnrollmentTpaNonTpa {
  addEnrollmentTpaNonTpa: AddEnrollmentTpaNonTpa_addEnrollmentTpaNonTpa;
}

export interface AddEnrollmentTpaNonTpaVariables {
  input: EnrollmentTpaNonTpaInput;
}
