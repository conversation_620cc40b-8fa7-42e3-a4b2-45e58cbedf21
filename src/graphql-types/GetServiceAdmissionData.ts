/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetServiceAdmissionData
// ====================================================

export interface GetServiceAdmissionData_getServiceAdmissionData_servicesSummary {
  __typename: "ServicesSummary";
  totalAdmissions: number | null;
  name: number | null;
}

export interface GetServiceAdmissionData_getServiceAdmissionData_list_admissionDiagnosis_diagnosisICD10 {
  __typename: "ListsByGender";
  male: string[] | null;
  female: string[] | null;
}

export interface GetServiceAdmissionData_getServiceAdmissionData_list_admissionDiagnosis_diagnosisICD11 {
  __typename: "ListsByGender";
  male: string[] | null;
  female: string[] | null;
}

export interface GetServiceAdmissionData_getServiceAdmissionData_list_admissionDiagnosis_diagnosisSNOMED {
  __typename: "ListsByGender";
  male: string[] | null;
  female: string[] | null;
}

export interface GetServiceAdmissionData_getServiceAdmissionData_list_admissionDiagnosis_byAdmittedDateAndAdmittedBy {
  __typename: "ListsByAdmittedDateAndAdmittedBy";
  admittedBy: string | null;
  admissionDate: string | null;
  diagnosis: string[] | null;
  clinifyId: string | null;
  patientName: string | null;
  paymentType: string | null;
  status: string | null;
  priority: string | null;
  totalLengthOfStay: string | null;
}

export interface GetServiceAdmissionData_getServiceAdmissionData_list_admissionDiagnosis {
  __typename: "AdmissionDiagnosisData";
  diagnosisICD10: GetServiceAdmissionData_getServiceAdmissionData_list_admissionDiagnosis_diagnosisICD10 | null;
  diagnosisICD11: GetServiceAdmissionData_getServiceAdmissionData_list_admissionDiagnosis_diagnosisICD11 | null;
  diagnosisSNOMED: GetServiceAdmissionData_getServiceAdmissionData_list_admissionDiagnosis_diagnosisSNOMED | null;
  byAdmittedDateAndAdmittedBy: GetServiceAdmissionData_getServiceAdmissionData_list_admissionDiagnosis_byAdmittedDateAndAdmittedBy[] | null;
}

export interface GetServiceAdmissionData_getServiceAdmissionData_list {
  __typename: "ServiceSummaryList";
  admissionDiagnosis: GetServiceAdmissionData_getServiceAdmissionData_list_admissionDiagnosis | null;
}

export interface GetServiceAdmissionData_getServiceAdmissionData {
  __typename: "ServiceSummaryWithList";
  servicesSummary: GetServiceAdmissionData_getServiceAdmissionData_servicesSummary[];
  list: GetServiceAdmissionData_getServiceAdmissionData_list;
}

export interface GetServiceAdmissionData {
  getServiceAdmissionData: GetServiceAdmissionData_getServiceAdmissionData;
}

export interface GetServiceAdmissionDataVariables {
  filter?: ServicesAnalyticsFilter | null;
}
