/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FamilyPlanningAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetInjectablesGiven
// ====================================================

export interface GetInjectablesGiven_getInjectablesGiven {
  __typename: "FamilyPlanningSummary";
  name: number | null;
  totalInjectablesGiven: number | null;
  category: string | null;
}

export interface GetInjectablesGiven {
  getInjectablesGiven: GetInjectablesGiven_getInjectablesGiven[];
}

export interface GetInjectablesGivenVariables {
  filter?: FamilyPlanningAnalyticsFilter | null;
}
