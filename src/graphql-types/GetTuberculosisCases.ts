/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CasesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetTuberculosisCases
// ====================================================

export interface GetTuberculosisCases_getTuberculosisCases {
  __typename: "CasesSummary";
  name: number | null;
  totalFemale: number | null;
  totalMale: number | null;
}

export interface GetTuberculosisCases {
  getTuberculosisCases: GetTuberculosisCases_getTuberculosisCases[];
}

export interface GetTuberculosisCasesVariables {
  filter?: CasesAnalyticsFilter | null;
}
