/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmosAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHmoFinanceData
// ====================================================

export interface GetHmoFinanceData_getHmoFinanceData {
  __typename: "FinanceReportData";
  code: string | null;
  batchStatus: string | null;
  claimStatus: string | null;
  providerName: string | null;
  providerCode: string | null;
  providerType: string | null;
  providerRegion: string | null;
  providerSubregion: string | null;
  payerName: string | null;
  employerName: string | null;
  claimId: string | null;
  enrolleeName: string | null;
  programPackage: string | null;
  programCover: string | null;
  membershipNumber: string | null;
  memberNumber: string | null;
  dateOfBirth: string | null;
  enrolleeAge: string | null;
  gender: string | null;
  treatmentDate: string | null;
  claimFinalizedDate: string | null;
  claimFinalizedBy: string | null;
  itemDescription: string | null;
  tariffAmount: string | null;
  phoneNumber: string | null;
  treatmentQuantity: string | null;
  totalTreatmentAmount: string | null;
  diagnosis: string | null;
  visitDate: string | null;
  doctorNote: string | null;
  rejectionReason: string | null;
  overridingComments: string | null;
  operator: string | null;
}

export interface GetHmoFinanceData {
  getHmoFinanceData: GetHmoFinanceData_getHmoFinanceData[];
}

export interface GetHmoFinanceDataVariables {
  filter: HmosAnalyticsFilter;
}
