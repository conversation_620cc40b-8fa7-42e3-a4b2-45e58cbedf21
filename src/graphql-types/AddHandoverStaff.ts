/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HandoverStaffInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddHandoverStaff
// ====================================================

export interface AddHandoverStaff_addHandoverStaff_staffProfile {
  __typename: "ProfileModel";
  fullName: string;
}

export interface AddHandoverStaff_addHandoverStaff {
  __typename: "HandoverStaffModel";
  id: string;
  status: string | null;
  currentShift: string | null;
  nextShift: string | null;
  staffProfileId: string | null;
  staffProfile: AddHandoverStaff_addHandoverStaff_staffProfile | null;
  handoverNoteId: string;
  createdDate: any;
  updatedDate: any;
  creatorId: string | null;
}

export interface AddHandoverStaff {
  addHandoverStaff: AddHandoverStaff_addHandoverStaff;
}

export interface AddHandoverStaffVariables {
  id: string;
  input: HandoverStaffInput;
}
