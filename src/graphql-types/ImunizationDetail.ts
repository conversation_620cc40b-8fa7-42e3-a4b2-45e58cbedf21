/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { immunizationCategory, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL fragment: ImunizationDetail
// ====================================================

export interface ImunizationDetail_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface ImunizationDetail_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: ImunizationDetail_preauthorizationDetails_provider | null;
}

export interface ImunizationDetail {
  __typename: "ImmunizationDetailModel";
  id: string;
  code: string | null;
  administrationType: string | null;
  period: string | null;
  adverseEffectsFollowingImmunization: string | null;
  provider: string | null;
  administrationSource: string | null;
  duration: string | null;
  immunizationName: string;
  priority: string | null;
  administeredDate: any | null;
  expiryDate: any | null;
  outcomeOfInvestigation: string | null;
  class: string | null;
  dosageUnit: string | null;
  stateEffects: string | null;
  batchNumber: string | null;
  category: immunizationCategory | null;
  quantity: string | null;
  adverseEffectsInvestigated: string | null;
  dosage: string | null;
  method: string | null;
  administratorName: string | null;
  itemId: string | null;
  preauthorizationDetails: ImunizationDetail_preauthorizationDetails | null;
}
