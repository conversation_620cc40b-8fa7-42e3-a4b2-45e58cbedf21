/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetBillStatusSummary
// ====================================================

export interface GetBillStatusSummary_getBillStatusDataSummary {
  __typename: "BillStatusSummaryResponse";
  name: number | null;
  totalCancelled: number;
  totalPaid: number;
  totalPartiallyPaid: number;
  totalPending: number;
}

export interface GetBillStatusSummary {
  getBillStatusDataSummary: GetBillStatusSummary_getBillStatusDataSummary;
}

export interface GetBillStatusSummaryVariables {
  filter: FinanceAnalyticsFilter;
}
