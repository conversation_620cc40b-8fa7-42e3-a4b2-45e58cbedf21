/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: EnrollmentAgentAdded
// ====================================================

export interface EnrollmentAgentAdded_EnrollmentAgentAdded_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface EnrollmentAgentAdded_EnrollmentAgentAdded_agency {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
  agencyType: string | null;
  isTpa: boolean | null;
}

export interface EnrollmentAgentAdded_EnrollmentAgentAdded_tpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
  isTpa: boolean | null;
}

export interface EnrollmentAgentAdded_EnrollmentAgentAdded {
  __typename: "EnrollmentAgentModel";
  id: string;
  profile: EnrollmentAgentAdded_EnrollmentAgentAdded_profile | null;
  agency: EnrollmentAgentAdded_EnrollmentAgentAdded_agency | null;
  tpaNonTpa: EnrollmentAgentAdded_EnrollmentAgentAdded_tpaNonTpa | null;
  accountNumber: string | null;
  accountName: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface EnrollmentAgentAdded {
  EnrollmentAgentAdded: EnrollmentAgentAdded_EnrollmentAgentAdded;
}

export interface EnrollmentAgentAddedVariables {
  hospitalId: string;
}
