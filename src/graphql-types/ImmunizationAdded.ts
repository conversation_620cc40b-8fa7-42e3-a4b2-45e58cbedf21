/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServiceType, FundTransferStatus, BillStatus, CommissionPayer, PercentOrAmount, PayoutStatus, VirtualAccountProvider } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: ImmunizationAdded
// ====================================================

export interface ImmunizationAdded_ImmunizationAdded_bill_details_serviceDetails {
  __typename: "BillServiceDetailInputType";
  serviceType: string | null;
  serviceName: string | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_details_additionalPayments {
  __typename: "AdditionalPaymentInputType";
  splitAmount: number | null;
  splitPaymentType: string | null;
  splitBankName: string | null;
  splitAccountNumber: string | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_details_subBills {
  __typename: "SubBillDetailsModel";
  id: string;
  amountDue: number | null;
  amountOwing: number | null;
  unitPrice: number | null;
  amountOutstanding: number | null;
  amountPaid: number | null;
  discountCode: string | null;
  amount: number | null;
  discountAmount: number | null;
  discountPercentage: number | null;
  vatAmount: number | null;
  vatPercentage: number | null;
  patientType: string | null;
  paymentType: string | null;
  serviceType: string | null;
  serviceName: string | null;
  quantity: number | null;
  reference: string | null;
  creatorName: string | null;
  lastModifierName: string | null;
  createdDate: any | null;
  updatedDate: any | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_details_createdBy {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_details_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_details {
  __typename: "BillDetailsModel";
  id: string;
  amountDue: number | null;
  amountOwing: number | null;
  unitPrice: number | null;
  amountOutstanding: number | null;
  amountPaid: number | null;
  discountCode: string | null;
  amount: number | null;
  discountAmount: number | null;
  discountPercentage: number | null;
  vatAmount: number | null;
  vatPercentage: number | null;
  professionalFeeAmount: number | null;
  professionalFeePercentage: number | null;
  bankName: string | null;
  accountNumber: string | null;
  reference: string | null;
  walletTransactionRef: string | null;
  billType: string | null;
  billName: string | null;
  serviceType: string | null;
  serviceName: string | null;
  subServiceType: string | null;
  serviceDetails: ImmunizationAdded_ImmunizationAdded_bill_details_serviceDetails[] | null;
  splitPayment: boolean;
  additionalPayments: ImmunizationAdded_ImmunizationAdded_bill_details_additionalPayments[] | null;
  description: string | null;
  patientType: string | null;
  paymentType: string | null;
  paymentMethod: string | null;
  quantity: number | null;
  /**
   * pointer to split payment parent or child (C:P)
   */
  splitReference: string | null;
  status: BillStatus;
  excluded: boolean | null;
  manuallyCreated: string | null;
  subBills: ImmunizationAdded_ImmunizationAdded_bill_details_subBills[] | null;
  createdBy: ImmunizationAdded_ImmunizationAdded_bill_details_createdBy | null;
  creatorName: string | null;
  lastModifierName: string | null;
  createdDate: any | null;
  updatedDate: any | null;
  billId: string | null;
  diagnosis: ImmunizationAdded_ImmunizationAdded_bill_details_diagnosis[] | null;
  admissionSeverity: string | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_senderProfile {
  __typename: "ProfileModel";
  id: string;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_senderHospital_phoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_senderHospital_billingInformation {
  __typename: "FacilityBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  bvn: string | null;
  branchName: string | null;
  isPreferredPayoutAccount: boolean | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_senderHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
  facilityLogo: string | null;
  website: string | null;
  phoneNumber: ImmunizationAdded_ImmunizationAdded_bill_senderHospital_phoneNumber | null;
  billingInformation: ImmunizationAdded_ImmunizationAdded_bill_senderHospital_billingInformation[] | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_receiverProfile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_receiverProfile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: ImmunizationAdded_ImmunizationAdded_bill_receiverProfile_coverageDetails_provider | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_receiverProfile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  type: string;
  fullName: string;
  coverageDetails: ImmunizationAdded_ImmunizationAdded_bill_receiverProfile_coverageDetails[] | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_receiverHospital {
  __typename: "HospitalModel";
  id: string;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_createdBy {
  __typename: "ProfileModel";
  id: string;
  type: string;
  clinifyId: string;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_virtualServicesPayment_virtualBankAccount {
  __typename: "VirtualBankAccountModel";
  id: string;
  bank: VirtualAccountProvider;
  accountName: string;
  accountNumber: string;
}

export interface ImmunizationAdded_ImmunizationAdded_bill_virtualServicesPayment {
  __typename: "VirtualServicesPaymentModel";
  id: string;
  amountDue: number;
  amountPaid: number;
  commissionFeeAmount: number | null;
  commissionFeePercentage: number | null;
  commissionPayer: CommissionPayer | null;
  controlledCommissionFee: PercentOrAmount | null;
  paymentStatus: string;
  payoutStatus: PayoutStatus;
  virtualBankAccount: ImmunizationAdded_ImmunizationAdded_bill_virtualServicesPayment_virtualBankAccount | null;
}

export interface ImmunizationAdded_ImmunizationAdded_bill {
  __typename: "BillModel";
  id: string;
  totalAmount: number;
  unitPrice: number;
  totalAccumulativePaid: number | null;
  amountPayable: number | null;
  amountPaid: number | null;
  discountAmount: number;
  vatAmount: number;
  professionalFeeAmount: number;
  invoiceNumber: string | null;
  amountDue: number;
  /**
   * Amount Owing
   */
  amountOwning: number;
  amountUnderpaid: number;
  amountOverpaid: number;
  hospitalName: string | null;
  hospitalAddress: string | null;
  amountOutstanding: number;
  reference: string | null;
  serviceType: ServiceType | null;
  description: string | null;
  paymentStatus: FundTransferStatus;
  billStatus: BillStatus;
  autoGenerated: boolean;
  additionalNote: string | null;
  raisedBy: string | null;
  collectedBy: string | null;
  collectionDateTime: any | null;
  billingDateTime: any | null;
  recalled: boolean | null;
  isPackage: boolean;
  details: ImmunizationAdded_ImmunizationAdded_bill_details[] | null;
  senderProfile: ImmunizationAdded_ImmunizationAdded_bill_senderProfile | null;
  senderHospital: ImmunizationAdded_ImmunizationAdded_bill_senderHospital | null;
  receiverProfile: ImmunizationAdded_ImmunizationAdded_bill_receiverProfile | null;
  receiverHospital: ImmunizationAdded_ImmunizationAdded_bill_receiverHospital | null;
  patientInformation: ImmunizationAdded_ImmunizationAdded_bill_patientInformation | null;
  createdBy: ImmunizationAdded_ImmunizationAdded_bill_createdBy | null;
  creatorName: string | null;
  lastModifierName: string | null;
  creatorId: string | null;
  createdDate: any;
  updatedDate: any;
  isWalkInBill: boolean;
  virtualServicesPayment: ImmunizationAdded_ImmunizationAdded_bill_virtualServicesPayment | null;
}

export interface ImmunizationAdded_ImmunizationAdded {
  __typename: "ImmunizationModel";
  bill: ImmunizationAdded_ImmunizationAdded_bill[] | null;
}

export interface ImmunizationAdded {
  ImmunizationAdded: ImmunizationAdded_ImmunizationAdded;
}

export interface ImmunizationAddedVariables {
  profileId: string;
  hospitalId: string;
}
