/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmoUtilizationFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetTopClaimDiagnosis
// ====================================================

export interface GetTopClaimDiagnosis_getTopClaimDiagnosis {
  __typename: "HmoUtilizationTopDiagnosis";
  diagnosis: string | null;
  total: number | null;
  name: string | null;
  enrolleeCount: number | null;
}

export interface GetTopClaimDiagnosis {
  getTopClaimDiagnosis: GetTopClaimDiagnosis_getTopClaimDiagnosis[];
}

export interface GetTopClaimDiagnosisVariables {
  filter: HmoUtilizationFilter;
}
