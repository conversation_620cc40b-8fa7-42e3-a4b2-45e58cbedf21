/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdateFeedbackCommentInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateFeedbackComment
// ====================================================

export interface UpdateFeedbackComment_updateFeedbackComment_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
}

export interface UpdateFeedbackComment_updateFeedbackComment_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface UpdateFeedbackComment_updateFeedbackComment {
  __typename: "FeedbackModel";
  id: string;
  feedbackDateTime: any | null;
  title: string;
  message: string;
  comment: string | null;
  category: string;
  isResolved: boolean | null;
  documentUrl: string[] | null;
  createdDate: any;
  creatorName: string | null;
  reviewerName: string | null;
  hospital: UpdateFeedbackComment_updateFeedbackComment_hospital | null;
  createdBy: UpdateFeedbackComment_updateFeedbackComment_createdBy;
}

export interface UpdateFeedbackComment {
  updateFeedbackComment: UpdateFeedbackComment_updateFeedbackComment;
}

export interface UpdateFeedbackCommentVariables {
  input: UpdateFeedbackCommentInput;
}
