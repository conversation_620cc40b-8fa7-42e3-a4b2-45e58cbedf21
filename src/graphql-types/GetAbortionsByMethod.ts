/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MaternalHealthAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetAbortionsByMethod
// ====================================================

export interface GetAbortionsByMethod_getAbortionsByMethod_abortionMethod {
  __typename: "CategoryCount";
  count: number | null;
  category: string | null;
}

export interface GetAbortionsByMethod_getAbortionsByMethod {
  __typename: "MaternalHealthSummary";
  name: number | null;
  totalAbortionByMethod: number | null;
  abortionMethod: GetAbortionsByMethod_getAbortionsByMethod_abortionMethod[] | null;
}

export interface GetAbortionsByMethod {
  getAbortionsByMethod: GetAbortionsByMethod_getAbortionsByMethod;
}

export interface GetAbortionsByMethodVariables {
  filter?: MaternalHealthAnalyticsFilter | null;
}
