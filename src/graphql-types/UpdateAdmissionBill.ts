/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AdmissionInput, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateAdmissionBill
// ====================================================

export interface UpdateAdmissionBill_updateAdmissionBill_admissionDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UpdateAdmissionBill_updateAdmissionBill_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface UpdateAdmissionBill_updateAdmissionBill_vteAndBleedingRiskAssessment_furtherAssessment {
  __typename: "Assessment";
  bleedingRiskAssessed: boolean | null;
  vteRiskAssessed: boolean | null;
  bleedingRiskAssessedLastModifiedDate: any | null;
  vteRiskAssessedLastModifiedDate: any | null;
  bleedingRiskAssessedLastModifierName: string | null;
  vteRiskAssessedLastModifierName: string | null;
  ref: string | null;
}

export interface UpdateAdmissionBill_updateAdmissionBill_vteAndBleedingRiskAssessment_onAdmission {
  __typename: "Assessment";
  bleedingRiskAssessed: boolean | null;
  vteRiskAssessed: boolean | null;
  bleedingRiskAssessedLastModifiedDate: any | null;
  vteRiskAssessedLastModifiedDate: any | null;
  bleedingRiskAssessedLastModifierName: string | null;
  vteRiskAssessedLastModifierName: string | null;
}

export interface UpdateAdmissionBill_updateAdmissionBill_vteAndBleedingRiskAssessment_within24Hours {
  __typename: "Assessment";
  bleedingRiskAssessed: boolean | null;
  vteRiskAssessed: boolean | null;
  bleedingRiskAssessedLastModifiedDate: any | null;
  vteRiskAssessedLastModifiedDate: any | null;
  bleedingRiskAssessedLastModifierName: string | null;
  vteRiskAssessedLastModifierName: string | null;
}

export interface UpdateAdmissionBill_updateAdmissionBill_vteAndBleedingRiskAssessment {
  __typename: "VTEAndBleedingRisk";
  furtherAssessment: UpdateAdmissionBill_updateAdmissionBill_vteAndBleedingRiskAssessment_furtherAssessment[] | null;
  onAdmission: UpdateAdmissionBill_updateAdmissionBill_vteAndBleedingRiskAssessment_onAdmission | null;
  within24Hours: UpdateAdmissionBill_updateAdmissionBill_vteAndBleedingRiskAssessment_within24Hours | null;
}

export interface UpdateAdmissionBill_updateAdmissionBill_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface UpdateAdmissionBill_updateAdmissionBill_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: UpdateAdmissionBill_updateAdmissionBill_profile_coverageDetails_provider | null;
}

export interface UpdateAdmissionBill_updateAdmissionBill_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: UpdateAdmissionBill_updateAdmissionBill_profile_coverageDetails[] | null;
}

export interface UpdateAdmissionBill_updateAdmissionBill_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UpdateAdmissionBill_updateAdmissionBill_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: UpdateAdmissionBill_updateAdmissionBill_preauthorizationDetails_provider | null;
}

export interface UpdateAdmissionBill_updateAdmissionBill_allergies {
  __typename: "AllergyModel";
  id: string;
}

export interface UpdateAdmissionBill_updateAdmissionBill_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface UpdateAdmissionBill_updateAdmissionBill_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface UpdateAdmissionBill_updateAdmissionBill_consultations {
  __typename: "ConsultationModel";
  id: string;
}

export interface UpdateAdmissionBill_updateAdmissionBill_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface UpdateAdmissionBill_updateAdmissionBill_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface UpdateAdmissionBill_updateAdmissionBill_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface UpdateAdmissionBill_updateAdmissionBill_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface UpdateAdmissionBill_updateAdmissionBill_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface UpdateAdmissionBill_updateAdmissionBill_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
}

export interface UpdateAdmissionBill_updateAdmissionBill {
  __typename: "AdmissionModel";
  id: string;
  admissionDate: any | null;
  admissionDiagnosis: UpdateAdmissionBill_updateAdmissionBill_admissionDiagnosis[] | null;
  duration: string | null;
  priority: string | null;
  category: string | null;
  severeness: string | null;
  admittedBy: string | null;
  doctorInCharge: string | null;
  ward: string | null;
  provider: string | null;
  providerServiceName: string | null;
  hospitalUnit: string | null;
  roomType: string | null;
  fileNumber: string | null;
  roomNumber: string | null;
  bedNumber: string | null;
  finding: string | null;
  rank: string | null;
  specialty: string | null;
  department: string | null;
  nurseName: string | null;
  hmoProviderId: string | null;
  serviceDetails: UpdateAdmissionBill_updateAdmissionBill_serviceDetails[] | null;
  isPackage: boolean;
  bedAvailable: string | null;
  roomOption: string | null;
  patientConsent: string | null;
  presentMedicalHistory: string | null;
  valuablesOrBelongings: string | null;
  patientEnvironmentOrientation: string | null;
  currentMedications: string | null;
  medicinesDeposition: string | null;
  instructedToSendHome: string | null;
  nonBroughtToHospital: string | null;
  otherPlacement: string | null;
  medicationOrDrug: string | null;
  bloodTransfusion: string | null;
  food: string | null;
  latex: string | null;
  adultAge: string | null;
  adultSedativeMedication: string | null;
  adultAmbulatorySupport: string | null;
  mentalStatus: string | null;
  childAge: string | null;
  dehydration: string | null;
  dizziness: string | null;
  respirationDistress: string | null;
  childAmbulatorySupport: string | null;
  childSedativeMedication: string | null;
  painScore: string | null;
  painDescriptors: string[] | null;
  specifyPainDescriptors: string | null;
  painLocation: string[] | null;
  specifyPainLocation: string | null;
  acuity: string | null;
  modifying: string | null;
  tobaccoUse: string | null;
  tobaccoUseDuration: string | null;
  alcoholUse: string | null;
  alcoholUseDuration: string | null;
  psychologicalStatus: string | null;
  specifyPsychologicalStatus: string | null;
  sleep: string | null;
  specifySleepDifficulty: string | null;
  sleepRoutine: string | null;
  specifySleepRoutine: string | null;
  whatMakesYouSleep: string | null;
  bathing: string | null;
  dressing: string | null;
  eating: string | null;
  mobility: string | null;
  stairClimbing: string | null;
  toiletUse: string | null;
  impairedHearing: string | null;
  impairedVision: string | null;
  canPerformAdl: string | null;
  canRead: string | null;
  canWrite: string | null;
  hearingAid: string | null;
  glasses: string | null;
  contacts: string | null;
  dentures: string | null;
  partial: string | null;
  difficultyInChewing: string | null;
  difficultyInSwallowing: string | null;
  specialDiet: string | null;
  specifySpecialDiet: string | null;
  reAdmission: string | null;
  lastAdmissionDateTime: any | null;
  specialCourtesy: string | null;
  specialArrangement: string | null;
  pallorSunkenEyesDehydrationAnorexia: string | null;
  vomittingDiarrheaEdema: string | null;
  newlyDiagnosedDiabeticOrHypertensive: string | null;
  hairOrSkinChange: string | null;
  nursingNeeds: string | null;
  nursingDiagnosis: string[] | null;
  objectives: string | null;
  nursingOrders: string | null;
  evaluation: string | null;
  dischargeDate: any | null;
  transferDate: any | null;
  roomInventoryId: string | null;
  clinicName: string | null;
  clinicAddress: string | null;
  appointmentId: string | null;
  billStatus: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  hospitalId: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  profileId: string | null;
  vteAndBleedingRiskAssessment: UpdateAdmissionBill_updateAdmissionBill_vteAndBleedingRiskAssessment | null;
  profile: UpdateAdmissionBill_updateAdmissionBill_profile | null;
  preauthorizationDetails: UpdateAdmissionBill_updateAdmissionBill_preauthorizationDetails | null;
  allergies: UpdateAdmissionBill_updateAdmissionBill_allergies[];
  medications: UpdateAdmissionBill_updateAdmissionBill_medications[];
  surgeries: UpdateAdmissionBill_updateAdmissionBill_surgeries[];
  consultations: UpdateAdmissionBill_updateAdmissionBill_consultations[];
  vitals: UpdateAdmissionBill_updateAdmissionBill_vitals[];
  radiology: UpdateAdmissionBill_updateAdmissionBill_radiology[];
  labTests: UpdateAdmissionBill_updateAdmissionBill_labTests[];
  investigations: UpdateAdmissionBill_updateAdmissionBill_investigations[];
  nursingServices: UpdateAdmissionBill_updateAdmissionBill_nursingServices[];
  hmoClaim: UpdateAdmissionBill_updateAdmissionBill_hmoClaim | null;
}

export interface UpdateAdmissionBill {
  updateAdmissionBill: UpdateAdmissionBill_updateAdmissionBill;
}

export interface UpdateAdmissionBillVariables {
  id: string;
  input: AdmissionInput;
}
