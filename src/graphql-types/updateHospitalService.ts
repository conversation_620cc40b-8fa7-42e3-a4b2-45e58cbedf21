/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HospitalServiceInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: updateHospitalService
// ====================================================

export interface updateHospitalService_updateHospitalService_services {
  __typename: "HospitalService";
  id: string | null;
  name: string | null;
  createdOn: any | null;
  updatedOn: any | null;
  description: string | null;
  creatorName: string | null;
}

export interface updateHospitalService_updateHospitalService {
  __typename: "HospitalModel";
  id: string;
  services: updateHospitalService_updateHospitalService_services[] | null;
}

export interface updateHospitalService {
  updateHospitalService: updateHospitalService_updateHospitalService;
}

export interface updateHospitalServiceVariables {
  service: HospitalServiceInput;
  id: string;
}
