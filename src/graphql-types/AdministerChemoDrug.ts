/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: AdministerChemoDrug
// ====================================================

export interface AdministerChemoDrug_administerChemoDrug_investigationDetails {
  __typename: "ChemoInvestigationDetails";
  investigationName: string | null;
  investigationType: string | null;
  investigationPerformed: boolean | null;
  investigationVerified: boolean | null;
}

export interface AdministerChemoDrug_administerChemoDrug_administrationRegister {
  __typename: "OncologyDrugAdministrationRegistration";
  administeredBy: string | null;
  administratorId: string | null;
  administrationDateTime: any | null;
  period: string;
}

export interface AdministerChemoDrug_administerChemoDrug {
  __typename: "OncologyChemoDrugModel";
  id: string;
  drugName: string;
  route: string | null;
  infusionUsed: string | null;
  dosage: string | null;
  dosagePercentage: string | null;
  totalDose: string | null;
  adjustedDose: string | null;
  quantity: string | null;
  day: string;
  cycleNumber: number;
  drugId: string | null;
  note: string | null;
  section: string;
  frequency: string | null;
  combinationGroupName: string | null;
  combinationName: string | null;
  chemoDiagnosis: string | null;
  inventoryClass: string | null;
  medicationDetailsId: string | null;
  investigationDetails: AdministerChemoDrug_administerChemoDrug_investigationDetails[] | null;
  administrationRegister: AdministerChemoDrug_administerChemoDrug_administrationRegister[] | null;
}

export interface AdministerChemoDrug {
  administerChemoDrug: AdministerChemoDrug_administerChemoDrug;
}

export interface AdministerChemoDrugVariables {
  id: string;
  period: string;
  status: boolean;
  clinifyId: string;
  pin?: string | null;
}
