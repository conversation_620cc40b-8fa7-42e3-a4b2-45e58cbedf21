/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ProfileInfosFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetCoverageInformations
// ====================================================

export interface GetCoverageInformations_profile_coverageInformations_list_hmoProfile_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface GetCoverageInformations_profile_coverageInformations_list_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  employeeType: string | null;
  employeeDivision: string | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  capturedDate: any | null;
  capturedBy: string | null;
  enrolledBy: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  paymentFrequency: string | null;
  memberStartDate: any | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  companyName: string | null;
  companyAddress: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  capitatedMember: string | null;
  retired: boolean | null;
  occupation: string | null;
  capitatedAmount: string | null;
  planEligibility: string | null;
  terminationDate: any | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionRate: string | null;
  commissionPayable: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
  activationDatetime: any | null;
  employerCode: string | null;
  totalPremiumAmountPaid: string | null;
  provider: GetCoverageInformations_profile_coverageInformations_list_hmoProfile_provider;
}

export interface GetCoverageInformations_profile_coverageInformations_list {
  __typename: "CoverageInformationModel";
  id: string;
  coverageType: string | null;
  companyName: string | null;
  companyAddress: string | null;
  familyName: string | null;
  familyAddress: string | null;
  name: string | null;
  contactAddress: string | null;
  memberNumber: string | null;
  memberPlan: string | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  hmoProfile: GetCoverageInformations_profile_coverageInformations_list_hmoProfile | null;
  fileNumber: string | null;
  existingFamily: boolean | null;
}

export interface GetCoverageInformations_profile_coverageInformations {
  __typename: "CoverageInformationResponse";
  totalCount: number;
  list: GetCoverageInformations_profile_coverageInformations_list[];
}

export interface GetCoverageInformations_profile {
  __typename: "ProfileModel";
  id: string;
  coverageInformations: GetCoverageInformations_profile_coverageInformations;
}

export interface GetCoverageInformations {
  profile: GetCoverageInformations_profile;
}

export interface GetCoverageInformationsVariables {
  filterInput: ProfileInfosFilterInput;
  id: string;
}
