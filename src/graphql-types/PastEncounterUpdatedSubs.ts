/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PastEncounterUpdatedSubs
// ====================================================

export interface PastEncounterUpdatedSubs_PastEncounterUpdated_details {
  __typename: "EncounterDetailModel";
  id: string;
  diagnosisDate: any | null;
  duration: string | null;
  diagnosedBy: string | null;
  specialty: string | null;
  symptoms: string[] | null;
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface PastEncounterUpdatedSubs_PastEncounterUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface PastEncounterUpdatedSubs_PastEncounterUpdated {
  __typename: "PastEncounterModel";
  id: string;
  clinicName: string | null;
  clinicAddress: string | null;
  details: PastEncounterUpdatedSubs_PastEncounterUpdated_details[];
  additionalNote: string | null;
  profile: PastEncounterUpdatedSubs_PastEncounterUpdated_profile | null;
}

export interface PastEncounterUpdatedSubs {
  PastEncounterUpdated: PastEncounterUpdatedSubs_PastEncounterUpdated;
}

export interface PastEncounterUpdatedSubsVariables {
  profileId: string;
}
