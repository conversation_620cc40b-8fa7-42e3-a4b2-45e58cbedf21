/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MessageType, MessageStatus } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: ChatConversationUpdated
// ====================================================

export interface ChatConversationUpdated_ChatConversationUpdated_lastMessage {
  __typename: "ChatMessageModel";
  id: string;
  createdDate: any;
  senderId: string;
  messageType: MessageType;
  content: string;
  status: MessageStatus;
  conversationId: string;
  senderName: string;
  facilityName: string | null;
}

export interface ChatConversationUpdated_ChatConversationUpdated_opponent_personalInformation {
  __typename: "PersonalInformation";
  displayPictureUrl: string | null;
}

export interface ChatConversationUpdated_ChatConversationUpdated_opponent {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  personalInformation: ChatConversationUpdated_ChatConversationUpdated_opponent_personalInformation | null;
}

export interface ChatConversationUpdated_ChatConversationUpdated {
  __typename: "ChatConversationModel";
  id: string;
  createdDate: any;
  participant1Id: string;
  participant2Id: string;
  participant1HospitalId: string | null;
  participant2HospitalId: string | null;
  participant1HospitalName: string | null;
  participant2HospitalName: string | null;
  hmoProviderId: string | null;
  unreadMessageCount: number | null;
  lastMessage: ChatConversationUpdated_ChatConversationUpdated_lastMessage | null;
  opponent: ChatConversationUpdated_ChatConversationUpdated_opponent | null;
}

export interface ChatConversationUpdated {
  ChatConversationUpdated: ChatConversationUpdated_ChatConversationUpdated;
}
