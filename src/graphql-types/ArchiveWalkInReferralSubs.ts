/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: ArchiveWalkInReferralSubs
// ====================================================

export interface ArchiveWalkInReferralSubs_WalkInReferralArchived_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface ArchiveWalkInReferralSubs_WalkInReferralArchived {
  __typename: "WalkInReferralModel";
  id: string;
  referralDateTime: any | null;
  referredBy: string | null;
  referralReason: string | null;
  concealReferralReason: boolean | null;
  referralFacilityName: string | null;
  referralFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: ArchiveWalkInReferralSubs_WalkInReferralArchived_patientInformation | null;
}

export interface ArchiveWalkInReferralSubs {
  WalkInReferralArchived: ArchiveWalkInReferralSubs_WalkInReferralArchived[];
}

export interface ArchiveWalkInReferralSubsVariables {
  hospitalId: string;
}
