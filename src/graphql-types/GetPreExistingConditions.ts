/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ProfileInfosFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPreExistingConditions
// ====================================================

export interface GetPreExistingConditions_profile_preExistingConditions_list_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface GetPreExistingConditions_profile_preExistingConditions_list {
  __typename: "PreExistingConditionModel";
  id: string;
  ageOfOnset: string | null;
  diagnosedDate: any | null;
  duration: string | null;
  additionalNote: string | null;
  conditionICD10: string | null;
  conditionICD11: string | null;
  conditionSNOMED: string | null;
  profile: GetPreExistingConditions_profile_preExistingConditions_list_profile | null;
}

export interface GetPreExistingConditions_profile_preExistingConditions {
  __typename: "PreExistingConditionsResponse";
  totalCount: number;
  list: GetPreExistingConditions_profile_preExistingConditions_list[];
}

export interface GetPreExistingConditions_profile {
  __typename: "ProfileModel";
  id: string;
  preExistingConditions: GetPreExistingConditions_profile_preExistingConditions;
}

export interface GetPreExistingConditions {
  profile: GetPreExistingConditions_profile;
}

export interface GetPreExistingConditionsVariables {
  filterInput: ProfileInfosFilterInput;
  id: string;
}
