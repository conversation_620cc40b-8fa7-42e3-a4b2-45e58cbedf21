/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { GynecologicHistoryInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateGynecologicHistory
// ====================================================

export interface UpdateGynecologicHistory_updateGynecologicHistoryInfo_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface UpdateGynecologicHistory_updateGynecologicHistoryInfo {
  __typename: "GynecologicHistoryModel";
  id: string;
  firstMenstrualAge: string | null;
  menstrualCycleLength: string | null;
  menstrualFlowDuration: string | null;
  lastMenstrualPeriod: any | null;
  menstrualFlow: string | null;
  contraceptiveUse: string | null;
  contraceptiveType: string | null;
  specifyContraceptiveType: string | null;
  newAcceptor: boolean | null;
  revisit: boolean | null;
  removeContraceptive: boolean | null;
  quantityGiven: string | null;
  miscarriageOrAbortion: string | null;
  miscarriageOrAbortionCount: number | null;
  pregnancyCount: string | null;
  currentlyPregnant: string | null;
  babyDelivered: string | null;
  menstrualStatus: string | null;
  breastFeeding: string | null;
  sourceOfReferral: string | null;
  counselledOnFp: string | null;
  conunselledOnPpfp: string | null;
  firstTimeFpUser: string | null;
  emergencyContraception: string | null;
  familyPlanningClientType: string | null;
  referredOut: string | null;
  followupVisit: any | null;
  additionalNote: string | null;
  profile: UpdateGynecologicHistory_updateGynecologicHistoryInfo_profile | null;
}

export interface UpdateGynecologicHistory {
  updateGynecologicHistoryInfo: UpdateGynecologicHistory_updateGynecologicHistoryInfo;
}

export interface UpdateGynecologicHistoryVariables {
  input: GynecologicHistoryInput;
  id: string;
}
