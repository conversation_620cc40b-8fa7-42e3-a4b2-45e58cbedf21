/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: DependentUpdatedSubs
// ====================================================

export interface DependentUpdatedSubs_DependentUpdated_phoneNumber {
  __typename: "PhoneNumberFields";
  value: string | null;
  countryCode: string | null;
  countryName: string | null;
}

export interface DependentUpdatedSubs_DependentUpdated_hmoProfile_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface DependentUpdatedSubs_DependentUpdated_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  memberPlan: string | null;
  memberNumber: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  primaryProviderAddress: string | null;
  provider: DependentUpdatedSubs_DependentUpdated_hmoProfile_provider;
}

export interface DependentUpdatedSubs_DependentUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface DependentUpdatedSubs_DependentUpdated {
  __typename: "DependentModel";
  id: string;
  title: string | null;
  firstName: string | null;
  lastName: string | null;
  middleName: string | null;
  gender: Gender | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  relationship: string | null;
  emailAddress: string | null;
  phoneNumber: DependentUpdatedSubs_DependentUpdated_phoneNumber | null;
  hmoProfile: DependentUpdatedSubs_DependentUpdated_hmoProfile | null;
  profile: DependentUpdatedSubs_DependentUpdated_profile | null;
  displayPictureUrl: string | null;
}

export interface DependentUpdatedSubs {
  DependentUpdated: DependentUpdatedSubs_DependentUpdated;
}

export interface DependentUpdatedSubsVariables {
  profileId: string;
}
