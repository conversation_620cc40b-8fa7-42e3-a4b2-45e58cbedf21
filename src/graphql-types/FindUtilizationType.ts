/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: FindUtilizationType
// ====================================================

export interface FindUtilizationType_findUtilizationType {
  __typename: "UtilizationTypeObject";
  /**
   * Substitute for utilization type name
   */
  label: string | null;
  /**
   * Substitute for utilization type id
   */
  value: string | null;
  code: string | null;
  hmoPlanBenefitId: string | null;
  utilizationCategory: string | null;
}

export interface FindUtilizationType {
  findUtilizationType: FindUtilizationType_findUtilizationType | null;
}

export interface FindUtilizationTypeVariables {
  providerId: string;
  visitTypeId: string;
  keyword: string;
}
