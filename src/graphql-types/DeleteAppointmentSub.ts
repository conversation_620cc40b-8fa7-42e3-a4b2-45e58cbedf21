/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NextAppointmentEntities } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: DeleteAppointmentSub
// ====================================================

export interface DeleteAppointmentSub_AppointmentsRemoved_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface DeleteAppointmentSub_AppointmentsRemoved_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface DeleteAppointmentSub_AppointmentsRemoved_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface DeleteAppointmentSub_AppointmentsRemoved_specialist {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  type: string;
  fullName: string;
}

export interface DeleteAppointmentSub_AppointmentsRemoved_serviceDetails {
  __typename: "ServiceDetailInputType";
  type: string | null;
  name: string | null;
}

export interface DeleteAppointmentSub_AppointmentsRemoved {
  __typename: "OrganisationAppointmentModel";
  id: string;
  hospital: DeleteAppointmentSub_AppointmentsRemoved_hospital | null;
  profile: DeleteAppointmentSub_AppointmentsRemoved_profile | null;
  patientInformation: DeleteAppointmentSub_AppointmentsRemoved_patientInformation | null;
  specialist: DeleteAppointmentSub_AppointmentsRemoved_specialist | null;
  createdDate: any;
  updatedDate: any;
  status: string | null;
  category: string | null;
  liveSessionUrl: string | null;
  rank: string | null;
  reason: string | null;
  appointmentDateTime: any | null;
  startDateTime: any | null;
  endDateTime: any | null;
  specialty: string | null;
  role: string | null;
  paymentType: string | null;
  serviceDetails: DeleteAppointmentSub_AppointmentsRemoved_serviceDetails[] | null;
  patientType: string | null;
  duration: string | null;
  confirmedBy: string | null;
  deliveryMethod: string | null;
  urgency: string | null;
  additionalNote: string | null;
  recordType: NextAppointmentEntities | null;
  documentUrl: string[] | null;
}

export interface DeleteAppointmentSub {
  AppointmentsRemoved: DeleteAppointmentSub_AppointmentsRemoved[];
}

export interface DeleteAppointmentSubVariables {
  profileId: string;
  hospitalId: string;
}
