/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InvoiceStatus, Currency, VirtualAccountProvider } from "./globalTypes";

// ====================================================
// GraphQL fragment: CachedInvoiceModel
// ====================================================

export interface CachedInvoiceModel_virtualAccount {
  __typename: "VirtualBankAccountModel";
  id: string;
  currency: Currency;
  updatedDate: any;
  createdDate: any;
  accountName: string;
  accountNumber: string;
  bank: VirtualAccountProvider;
  expiryDate: any | null;
}

export interface CachedInvoiceModel {
  __typename: "InvoiceModel";
  id: string;
  issueDate: any;
  invoiceStatus: InvoiceStatus;
  virtualAccountId: string | null;
  virtualAccount: CachedInvoiceModel_virtualAccount | null;
}
