/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: MakePinMandatory
// ====================================================

export interface MakePinMandatory_makePinMandatory {
  __typename: "UserModel";
  id: string;
  isPinMandatory: boolean | null;
  hasPin: boolean | null;
}

export interface MakePinMandatory {
  makePinMandatory: MakePinMandatory_makePinMandatory[];
}

export interface MakePinMandatoryVariables {
  ids: string[];
  isPinMandatory: boolean;
}
