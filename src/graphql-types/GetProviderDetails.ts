/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HospitalFilterInput, HospitalPlan, PlanStatus, HMOTariffBand } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetProviderDetails
// ====================================================

export interface GetProviderDetails_hospitals_list_hmoHospitals_provider {
  __typename: "HmoProviderModel";
  id: string;
}

export interface GetProviderDetails_hospitals_list_hmoHospitals_planVisibility {
  __typename: "PlanVisibilityDto";
  id: string;
  name: string;
}

export interface GetProviderDetails_hospitals_list_hmoHospitals {
  __typename: "HmoHospitalModel";
  id: string;
  hmoProviderUniqueId: string | null;
  hmoProviderId: string;
  tariffBand: HMOTariffBand | null;
  category: string | null;
  enrolleeCount: number | null;
  enrolleeLimit: number | null;
  provider: GetProviderDetails_hospitals_list_hmoHospitals_provider | null;
  planVisibility: GetProviderDetails_hospitals_list_hmoHospitals_planVisibility[] | null;
}

export interface GetProviderDetails_hospitals_list_phoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
}

export interface GetProviderDetails_hospitals_list_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
}

export interface GetProviderDetails_hospitals_list_orgAdmin_personalInformation {
  __typename: "PersonalInformation";
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
}

export interface GetProviderDetails_hospitals_list_orgAdmin_user {
  __typename: "UserModel";
  id: string;
  email: string | null;
}

export interface GetProviderDetails_hospitals_list_orgAdmin {
  __typename: "ProfileModel";
  id: string;
  title: string | null;
  fullName: string;
  personalInformation: GetProviderDetails_hospitals_list_orgAdmin_personalInformation | null;
  user: GetProviderDetails_hospitals_list_orgAdmin_user;
}

export interface GetProviderDetails_hospitals_list {
  __typename: "HospitalModel";
  id: string;
  clinifyId: string | null;
  name: string | null;
  address: string | null;
  ownership: string | null;
  lga: string | null;
  politicalWard: string | null;
  city: string | null;
  state: string | null;
  level: string | null;
  supportMail: string | null;
  country: string | null;
  plan: HospitalPlan;
  planStatus: PlanStatus;
  classification: string | null;
  hmoHospitals: GetProviderDetails_hospitals_list_hmoHospitals[] | null;
  phoneNumber: GetProviderDetails_hospitals_list_phoneNumber | null;
  secondaryPhoneNumber: GetProviderDetails_hospitals_list_secondaryPhoneNumber | null;
  /**
   * Organization administrator
   */
  orgAdmin: GetProviderDetails_hospitals_list_orgAdmin;
}

export interface GetProviderDetails_hospitals {
  __typename: "HospitalResponse";
  totalCount: number;
  list: GetProviderDetails_hospitals_list[];
}

export interface GetProviderDetails {
  hospitals: GetProviderDetails_hospitals;
}

export interface GetProviderDetailsVariables {
  filterOptions?: HospitalFilterInput | null;
}
