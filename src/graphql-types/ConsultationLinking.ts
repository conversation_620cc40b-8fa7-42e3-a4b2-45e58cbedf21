/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: ConsultationLinking
// ====================================================

export interface ConsultationLinking_allergies {
  __typename: "AllergyModel";
  id: string;
}

export interface ConsultationLinking_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface ConsultationLinking_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface ConsultationLinking_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface ConsultationLinking_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface ConsultationLinking_labTests {
  __typename: "LabResultModel";
  id: string;
}

export interface ConsultationLinking_radiology {
  __typename: "RadiologyResultModel";
  id: string;
}

export interface ConsultationLinking_investigations {
  __typename: "LabResultModel";
  id: string;
}

export interface ConsultationLinking_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface ConsultationLinking {
  __typename: "ConsultationModel";
  allergies: ConsultationLinking_allergies[];
  medications: ConsultationLinking_medications[];
  surgeries: ConsultationLinking_surgeries[];
  admissions: ConsultationLinking_admissions[];
  vitals: ConsultationLinking_vitals[];
  labTests: ConsultationLinking_labTests[];
  radiology: ConsultationLinking_radiology[];
  investigations: ConsultationLinking_investigations[];
  nursingServices: ConsultationLinking_nursingServices[];
}
