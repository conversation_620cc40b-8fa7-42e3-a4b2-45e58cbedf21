/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PackageAddedSubs
// ====================================================

export interface PackageAddedSubs_PackageAdded_serviceDetails {
  __typename: "ServiceDetailsFields";
  serviceType: string | null;
  serviceName: string | null;
  /**
   * Extra information about the service in json format
   */
  extraInformation: string | null;
}

export interface PackageAddedSubs_PackageAdded {
  __typename: "PackageModel";
  id: string;
  packageDate: any;
  name: string;
  price: string | null;
  gender: string;
  serviceDetails: PackageAddedSubs_PackageAdded_serviceDetails[] | null;
  createdDate: any;
  updatedDate: any;
}

export interface PackageAddedSubs {
  PackageAdded: PackageAddedSubs_PackageAdded;
}

export interface PackageAddedSubsVariables {
  hospitalId: string;
}
