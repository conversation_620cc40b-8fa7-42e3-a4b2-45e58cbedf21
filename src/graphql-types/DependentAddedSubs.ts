/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: DependentAddedSubs
// ====================================================

export interface DependentAddedSubs_DependentAdded_phoneNumber {
  __typename: "PhoneNumberFields";
  value: string | null;
  countryCode: string | null;
  countryName: string | null;
}

export interface DependentAddedSubs_DependentAdded_hmoProfile_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface DependentAddedSubs_DependentAdded_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  memberPlan: string | null;
  memberNumber: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  primaryProviderAddress: string | null;
  provider: DependentAddedSubs_DependentAdded_hmoProfile_provider;
}

export interface DependentAddedSubs_DependentAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface DependentAddedSubs_DependentAdded {
  __typename: "DependentModel";
  id: string;
  title: string | null;
  firstName: string | null;
  lastName: string | null;
  middleName: string | null;
  gender: Gender | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  relationship: string | null;
  emailAddress: string | null;
  phoneNumber: DependentAddedSubs_DependentAdded_phoneNumber | null;
  hmoProfile: DependentAddedSubs_DependentAdded_hmoProfile | null;
  profile: DependentAddedSubs_DependentAdded_profile | null;
  displayPictureUrl: string | null;
}

export interface DependentAddedSubs {
  DependentAdded: DependentAddedSubs_DependentAdded;
}

export interface DependentAddedSubsVariables {
  profileId: string;
}
