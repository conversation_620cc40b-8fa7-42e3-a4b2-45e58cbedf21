/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: AdmissionLinking
// ====================================================

export interface AdmissionLinking_allergies {
  __typename: "AllergyModel";
  id: string;
}

export interface AdmissionLinking_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface AdmissionLinking_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface AdmissionLinking_consultations {
  __typename: "ConsultationModel";
  id: string;
}

export interface AdmissionLinking_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface AdmissionLinking_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface AdmissionLinking_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface AdmissionLinking_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface AdmissionLinking_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface AdmissionLinking {
  __typename: "AdmissionModel";
  allergies: AdmissionLinking_allergies[];
  medications: AdmissionLinking_medications[];
  surgeries: AdmissionLinking_surgeries[];
  consultations: AdmissionLinking_consultations[];
  vitals: AdmissionLinking_vitals[];
  radiology: AdmissionLinking_radiology[];
  labTests: AdmissionLinking_labTests[];
  investigations: AdmissionLinking_investigations[];
  nursingServices: AdmissionLinking_nursingServices[];
}
