/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: SetPreferredPayoutAccount
// ====================================================

export interface SetPreferredPayoutAccount_setPreferredPayoutAccount {
  __typename: "FacilityBillingInformationModel";
  id: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  bvn: string | null;
  branchName: string | null;
  isPreferredPayoutAccount: boolean | null;
}

export interface SetPreferredPayoutAccount {
  setPreferredPayoutAccount: SetPreferredPayoutAccount_setPreferredPayoutAccount[];
}

export interface SetPreferredPayoutAccountVariables {
  id: string;
}
