/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AllergyInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdatePatientAllergy
// ====================================================

export interface UpdatePatientAllergy_updateAllergy_details_clinicalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UpdatePatientAllergy_updateAllergy_details {
  __typename: "AllergyDetails";
  type: string;
  trigger: string;
  reactions: string[] | null;
  severeness: string | null;
  clinicalDiagnosis: UpdatePatientAllergy_updateAllergy_details_clinicalDiagnosis[] | null;
}

export interface UpdatePatientAllergy_updateAllergy_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface UpdatePatientAllergy_updateAllergy_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface UpdatePatientAllergy_updateAllergy {
  __typename: "AllergyModel";
  id: string;
  occurenceDate: any | null;
  duration: string | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  doctorName: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  details: UpdatePatientAllergy_updateAllergy_details[] | null;
  profileId: string | null;
  profile: UpdatePatientAllergy_updateAllergy_profile | null;
  documentUrl: string[] | null;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
  createdDate: any;
  updatedDate: any;
  medications: UpdatePatientAllergy_updateAllergy_medications[];
}

export interface UpdatePatientAllergy {
  updateAllergy: UpdatePatientAllergy_updateAllergy;
}

export interface UpdatePatientAllergyVariables {
  input: AllergyInput;
  id: string;
}
