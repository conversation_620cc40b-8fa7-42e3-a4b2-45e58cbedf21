/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateCustomPaFormatType
// ====================================================

export interface UpdateCustomPaFormatType_updateCustomPaFormatType {
  __typename: "FacilityPreferenceModel";
  id: string;
  customPaFormatType: boolean | null;
}

export interface UpdateCustomPaFormatType {
  updateCustomPaFormatType: UpdateCustomPaFormatType_updateCustomPaFormatType;
}

export interface UpdateCustomPaFormatTypeVariables {
  mode: boolean;
}
