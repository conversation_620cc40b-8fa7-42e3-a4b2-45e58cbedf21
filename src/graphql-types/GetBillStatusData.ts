/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FinanceAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetBillStatusData
// ====================================================

export interface GetBillStatusData_getBillStatusData_billSummary {
  __typename: "BillSummaryType";
  hospitalName: string | null;
  hospitalId: string | null;
  totalAmountOutstanding: number | null;
  totalAmountDue: number | null;
  totalDiscount: number | null;
  totalAmount: number | null;
  totalAmountPaid: number | null;
  totalRevenue: number | null;
  totalPaid: number | null;
  totalPending: number | null;
  totalCancelled: number | null;
  totalPartial: number | null;
  totalTransactions: number | null;
}

export interface GetBillStatusData_getBillStatusData_depositSummary {
  __typename: "DepositSummaryType";
  hospitalName: string | null;
  hospitalId: string | null;
  totalAmountDeposited: number | null;
  totalAmountRefunded: number | null;
  totalAmountWithdrawn: number | null;
  totalAmountAvailable: number | null;
}

export interface GetBillStatusData_getBillStatusData {
  __typename: "FinanceData";
  name: number | null;
  totalTransactions: number | null;
  totalDiscount: number | null;
  totalRevenue: number | null;
  totalAmount: number | null;
  totalAmountPaid: number | null;
  totalPending: number | null;
  totalPaid: number | null;
  totalPartial: number | null;
  totalCancelled: number | null;
  totalAmountOutstanding: number | null;
  raisedBy: string | null;
  category: string | null;
  coverageName: string | null;
  coverageType: string | null;
  enrolleeId: string | null;
  patientClinifyId: string | null;
  patientEmailAddress: string | null;
  patientName: string | null;
  patientPhoneNumber: string | null;
  quantity: string | null;
  serviceName: string | null;
  serviceType: string | null;
  visitationDate: any | null;
  totalAmountDue: number | null;
  totalAmountDeposited: number | null;
  totalAmountAvailable: number | null;
  totalAmountWithdrawn: number | null;
  totalAmountRefunded: number | null;
  billSummary: GetBillStatusData_getBillStatusData_billSummary[] | null;
  depositSummary: GetBillStatusData_getBillStatusData_depositSummary[] | null;
}

export interface GetBillStatusData {
  getBillStatusData: GetBillStatusData_getBillStatusData[];
}

export interface GetBillStatusDataVariables {
  filter?: FinanceAnalyticsFilter | null;
}
