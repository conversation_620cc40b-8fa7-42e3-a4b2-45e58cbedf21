/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PartographInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddPatientPartograph
// ====================================================

export interface AddPatientPartograph_addPartograph_fhr {
  __typename: "FhrType";
  readingDate: any | null;
  value: string | null;
  time: string | null;
}

export interface AddPatientPartograph_addPartograph_amnoiticFluid {
  __typename: "AmnoiticFluidType";
  readingDate: any | null;
  value: string | null;
  moulding: string | null;
  time: string | null;
}

export interface AddPatientPartograph_addPartograph_cervixDecent {
  __typename: "CervixDecentType";
  readingDate: any | null;
  cervix: string | null;
  decent: string | null;
  period: any | null;
  time: string | null;
}

export interface AddPatientPartograph_addPartograph_contraction {
  __typename: "ContractionType";
  readingDate: any | null;
  value: string | null;
  duration: string | null;
  time: string | null;
}

export interface AddPatientPartograph_addPartograph_drugIv {
  __typename: "DrugIvType";
  readingDate: any | null;
  drugName: string[] | null;
  time: string | null;
}

export interface AddPatientPartograph_addPartograph_oxytocin {
  __typename: "OxytocinType";
  readingDate: any | null;
  value: string | null;
  drops: string | null;
  time: string | null;
}

export interface AddPatientPartograph_addPartograph_pulseBP {
  __typename: "PulseBPType";
  readingDate: any | null;
  pulse: string | null;
  bpSystolic: string | null;
  bpDiastolic: string | null;
  time: string | null;
}

export interface AddPatientPartograph_addPartograph_temperature {
  __typename: "TemperatureType";
  readingDate: any | null;
  value: string | null;
  time: string | null;
  unit: string | null;
}

export interface AddPatientPartograph_addPartograph_urine {
  __typename: "UrineType";
  readingDate: any | null;
  protein: string | null;
  acetone: string | null;
  volume: string | null;
  time: string | null;
}

export interface AddPatientPartograph_addPartograph_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface AddPatientPartograph_addPartograph {
  __typename: "PartographModel";
  id: string;
  notes: string | null;
  fhr: AddPatientPartograph_addPartograph_fhr[] | null;
  amnoiticFluid: AddPatientPartograph_addPartograph_amnoiticFluid[] | null;
  cervixDecent: AddPatientPartograph_addPartograph_cervixDecent[] | null;
  contraction: AddPatientPartograph_addPartograph_contraction[] | null;
  drugIv: AddPatientPartograph_addPartograph_drugIv[] | null;
  oxytocin: AddPatientPartograph_addPartograph_oxytocin[] | null;
  pulseBP: AddPatientPartograph_addPartograph_pulseBP[] | null;
  temperature: AddPatientPartograph_addPartograph_temperature[] | null;
  urine: AddPatientPartograph_addPartograph_urine[] | null;
  profileId: string | null;
  profile: AddPatientPartograph_addPartograph_profile | null;
  facilityName: string | null;
  facilityAddress: string | null;
}

export interface AddPatientPartograph {
  addPartograph: AddPatientPartograph_addPartograph;
}

export interface AddPatientPartographVariables {
  id?: string | null;
  input: PartographInput;
  pin?: string | null;
}
