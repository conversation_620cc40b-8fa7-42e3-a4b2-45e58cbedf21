/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicationOptionType, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: ArchiveMedicationSubs
// ====================================================

export interface ArchiveMedicationSubs_MedicationArchived_details_medicationConsumables {
  __typename: "MedicationConsumables";
  name: string | null;
  drugInventoryId: string | null;
  unitPrice: number | null;
  quantity: string | null;
  inventoryClass: string | null;
}

export interface ArchiveMedicationSubs_MedicationArchived_details_priceDetails {
  __typename: "MedPriceDetailInputType";
  type: string | null;
  name: string | null;
  pricePerUnit: string | null;
  patientType: string | null;
  paymentType: string | null;
  quantity: string | null;
}

export interface ArchiveMedicationSubs_MedicationArchived_details_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface ArchiveMedicationSubs_MedicationArchived_details_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: ArchiveMedicationSubs_MedicationArchived_details_preauthorizationDetails_provider | null;
}

export interface ArchiveMedicationSubs_MedicationArchived_details_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface ArchiveMedicationSubs_MedicationArchived_details_chemoDrugs_investigationDetails {
  __typename: "ChemoInvestigationDetails";
  investigationName: string | null;
  investigationType: string | null;
  investigationPerformed: boolean | null;
  investigationVerified: boolean | null;
}

export interface ArchiveMedicationSubs_MedicationArchived_details_chemoDrugs_administrationRegister {
  __typename: "OncologyDrugAdministrationRegistration";
  administeredBy: string | null;
  administratorId: string | null;
  administrationDateTime: any | null;
  period: string;
}

export interface ArchiveMedicationSubs_MedicationArchived_details_chemoDrugs_oncologyConsultationHistory_chemoComments {
  __typename: "ChemoCommentInputType";
  cycleNumber: number;
  section: string;
  comment: string | null;
  creatorId: string;
  creatorName: string;
  createdDate: any;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
}

export interface ArchiveMedicationSubs_MedicationArchived_details_chemoDrugs_oncologyConsultationHistory {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  chemoComments: ArchiveMedicationSubs_MedicationArchived_details_chemoDrugs_oncologyConsultationHistory_chemoComments[] | null;
}

export interface ArchiveMedicationSubs_MedicationArchived_details_chemoDrugs {
  __typename: "OncologyChemoDrugModel";
  id: string;
  drugName: string;
  route: string | null;
  infusionUsed: string | null;
  dosage: string | null;
  dosagePercentage: string | null;
  totalDose: string | null;
  adjustedDose: string | null;
  quantity: string | null;
  day: string;
  cycleNumber: number;
  drugId: string | null;
  note: string | null;
  section: string;
  frequency: string | null;
  combinationGroupName: string | null;
  combinationName: string | null;
  chemoDiagnosis: string | null;
  inventoryClass: string | null;
  medicationDetailsId: string | null;
  investigationDetails: ArchiveMedicationSubs_MedicationArchived_details_chemoDrugs_investigationDetails[] | null;
  administrationRegister: ArchiveMedicationSubs_MedicationArchived_details_chemoDrugs_administrationRegister[] | null;
  oncologyConsultationHistory: ArchiveMedicationSubs_MedicationArchived_details_chemoDrugs_oncologyConsultationHistory | null;
}

export interface ArchiveMedicationSubs_MedicationArchived_details {
  __typename: "MedicationDetailsModel";
  id: string;
  datePrescribed: any | null;
  duration: string | null;
  medicationName: string | null;
  medicationCategory: string | null;
  purpose: string | null;
  administrationMethod: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  prescriptionNote: string | null;
  concealPrescriptionNote: boolean | null;
  type: string | null;
  quantity: string | null;
  startDate: any | null;
  endDate: any | null;
  bank: string | null;
  drugInventoryId: string | null;
  option: MedicationOptionType | null;
  unitPrice: string | null;
  medicationStatus: string | null;
  provider: string | null;
  fromBundle: string | null;
  medicationConsumables: ArchiveMedicationSubs_MedicationArchived_details_medicationConsumables[] | null;
  priceDetails: ArchiveMedicationSubs_MedicationArchived_details_priceDetails | null;
  discontinue: string | null;
  discontinueReason: string | null;
  adverseEffectsFollowingMedication: string | null;
  stateEffects: string | null;
  adverseEffectsInvestigated: string | null;
  outcomeOfInvestigation: string | null;
  refillNumber: number | null;
  frequency: string | null;
  isPackage: boolean;
  hospitalId: string | null;
  medicationType: string | null;
  preauthorizationDetails: ArchiveMedicationSubs_MedicationArchived_details_preauthorizationDetails | null;
  diagnosis: ArchiveMedicationSubs_MedicationArchived_details_diagnosis[] | null;
  chemoDrugs: ArchiveMedicationSubs_MedicationArchived_details_chemoDrugs[] | null;
  inventoryClass: string | null;
}

export interface ArchiveMedicationSubs_MedicationArchived_administrationNotes {
  __typename: "AdministrationNoteModel";
  id: string;
  administrationNote: string | null;
  conceal: boolean | null;
  medicationId: string;
}

export interface ArchiveMedicationSubs_MedicationArchived_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface ArchiveMedicationSubs_MedicationArchived_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: ArchiveMedicationSubs_MedicationArchived_profile_coverageDetails_provider | null;
}

export interface ArchiveMedicationSubs_MedicationArchived_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
  coverageDetails: ArchiveMedicationSubs_MedicationArchived_profile_coverageDetails[] | null;
}

export interface ArchiveMedicationSubs_MedicationArchived {
  __typename: "MedicationModel";
  id: string;
  prescribedBy: string | null;
  rank: string | null;
  department: string | null;
  specialty: string | null;
  verificationCode: string | null;
  details: ArchiveMedicationSubs_MedicationArchived_details[] | null;
  administrationNotes: ArchiveMedicationSubs_MedicationArchived_administrationNotes[] | null;
  dispenseIds: string[] | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  setReminder: boolean | null;
  reminderStartDate: any | null;
  reminderEndDate: any | null;
  medicationStartTime: string | null;
  medicationEndTime: string | null;
  interval: number | null;
  intervalUnit: string | null;
  remindMe: string | null;
  documentUrl: string[] | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  createdDate: any;
  updatedDate: any;
  clinifyId: string | null;
  billStatus: string | null;
  hospitalId: string | null;
  profileId: string | null;
  profile: ArchiveMedicationSubs_MedicationArchived_profile | null;
}

export interface ArchiveMedicationSubs {
  MedicationArchived: ArchiveMedicationSubs_MedicationArchived[];
}

export interface ArchiveMedicationSubsVariables {
  profileId: string;
  hospitalId: string;
}
