/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VitalFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPatientVitalSignsList
// ====================================================

export interface GetPatientVitalSignsList_profile_vitals_list_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface GetPatientVitalSignsList_profile_vitals_list_anthropometry {
  __typename: "AnthropometryModel";
  id: string;
  bmi: string | null;
  bsa: string | null;
  readingDateTime: any | null;
  height: string | null;
  heightUnit: string | null;
  weight: string | null;
  weightUnit: string | null;
  hipCircumference: string | null;
  hipCircumferenceUnit: string | null;
  waistCircumference: string | null;
  waistCircumferenceUnit: string | null;
  skinfoldThickness: string | null;
  skinfoldThicknessUnit: string | null;
  leftUpperLimbCircumference: string | null;
  rightUpperLimbCircumference: string | null;
  upperLimbCircumferenceUnit: string | null;
  leftLowerLimbCircumference: string | null;
  rightLowerLimbCircumference: string | null;
  lowerLimbCircumferenceUnit: string | null;
  leftThighCircumference: string | null;
  rightThighCircumference: string | null;
  thighCircumferenceUnit: string | null;
  abdominalGirth: string | null;
  abdominalGirthUnit: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isAbdominalGirthCritical: boolean | null;
  isHeightCritical: boolean | null;
  isWeightCritical: boolean | null;
  isSkinfoldThicknessCritical: boolean | null;
  isWaistCircumferenceCritical: boolean | null;
  isHipCircumferenceCritical: boolean | null;
}

export interface GetPatientVitalSignsList_profile_vitals_list_bloodGlucose {
  __typename: "BloodGlucoseModel";
  id: string;
  readingDateTime: any | null;
  reading: string | null;
  readingUnit: string | null;
  mealTime: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
}

export interface GetPatientVitalSignsList_profile_vitals_list_bloodPressure {
  __typename: "BloodPressureModel";
  id: string;
  readingDateTime: any | null;
  diastolic: string | null;
  systolic: string | null;
  meanArterialPressure: string | null;
  heartRate: string | null;
  fetalHeartRate: string | null;
  location: string | null;
  method: string | null;
  additionalNote: string | null;
  position: string | null;
  rhythm: string | null;
  concealAdditionalNote: boolean | null;
  isDiastolicCritical: boolean | null;
  isSystolicCritical: boolean | null;
  isHeartRateCritical: boolean | null;
  isFetalHeartRateCritical: boolean | null;
}

export interface GetPatientVitalSignsList_profile_vitals_list_pulseRate {
  __typename: "PulseRateModel";
  id: string;
  readingDateTime: any | null;
  reading: string | null;
  checkMethod: string | null;
  checkMethodSpecify: string | null;
  rhythm: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
}

export interface GetPatientVitalSignsList_profile_vitals_list_respiratoryRate {
  __typename: "RespiratoryRateModel";
  id: string;
  readingDateTime: any | null;
  reading: string | null;
  oxygenSaturation: string | null;
  spO2Site: string | null;
  O2FlowRate: string | null;
  fIO2: string | null;
  O2Therapy: string | null;
  etco2: string | null;
  etco2Unit: string | null;
  cardiacRythm: string | null;
  rhythm: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
  isO2FlowRateCritical: boolean | null;
  isOxygenSaturationCritical: boolean | null;
  isEtco2Critical: boolean | null;
  isFIO2Critical: boolean | null;
}

export interface GetPatientVitalSignsList_profile_vitals_list_temperature {
  __typename: "TemperatureModel";
  id: string;
  readingDateTime: any | null;
  checkMethod: string | null;
  reading: string | null;
  readingUnit: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
}

export interface GetPatientVitalSignsList_profile_vitals_list_visualAcuity {
  __typename: "VisualAcuityModel";
  id: string;
  readingDateTime: any | null;
  withGlassesLeft: string | null;
  withGlassesRight: string | null;
  withoutGlassesLeft: string | null;
  withoutGlassesRight: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
}

export interface GetPatientVitalSignsList_profile_vitals_list_urineDipstick {
  __typename: "UrineDipstickModel";
  id: string;
  readingDateTime: any | null;
  blood: string | null;
  glucose: string | null;
  ketones: string | null;
  ph: string | null;
  protein: string | null;
  nitrites: string | null;
  leucocyte: string | null;
  urobilinogen: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isPhCritical: boolean | null;
}

export interface GetPatientVitalSignsList_profile_vitals_list_pain {
  __typename: "PainModel";
  id: string;
  score: string | null;
  type: string | null;
  dateTimePainStarted: any | null;
  location: string[] | null;
  specifyLocation: string | null;
  orientation: string[] | null;
  specifyOrientation: string | null;
  radiatingTowards: string | null;
  descriptors: string[] | null;
  specifyDescriptors: string | null;
  frequency: string[] | null;
  specifyFrequency: string | null;
  onset: string[] | null;
  specifyOnset: string | null;
  clinicalProgression: string[] | null;
  specifyClinicalProgression: string | null;
  aggravatingFactors: string[] | null;
  specifyAggravatingFactors: string | null;
  painCausedAsResultOfInjury: string | null;
  workRelatedInjury: string | null;
  painGoal: string | null;
  interventions: string[] | null;
  specifyInterventions: string | null;
  responsetoInterventions: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
}

export interface GetPatientVitalSignsList_profile_vitals_list {
  __typename: "VitalModel";
  id: string | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  profileId: string | null;
  profile: GetPatientVitalSignsList_profile_vitals_list_profile | null;
  anthropometry: GetPatientVitalSignsList_profile_vitals_list_anthropometry[] | null;
  bloodGlucose: GetPatientVitalSignsList_profile_vitals_list_bloodGlucose[] | null;
  bloodPressure: GetPatientVitalSignsList_profile_vitals_list_bloodPressure[] | null;
  pulseRate: GetPatientVitalSignsList_profile_vitals_list_pulseRate[] | null;
  respiratoryRate: GetPatientVitalSignsList_profile_vitals_list_respiratoryRate[] | null;
  temperature: GetPatientVitalSignsList_profile_vitals_list_temperature[] | null;
  visualAcuity: GetPatientVitalSignsList_profile_vitals_list_visualAcuity[] | null;
  urineDipstick: GetPatientVitalSignsList_profile_vitals_list_urineDipstick[] | null;
  pain: GetPatientVitalSignsList_profile_vitals_list_pain[] | null;
}

export interface GetPatientVitalSignsList_profile_vitals {
  __typename: "VitalResponse";
  totalCount: number;
  list: GetPatientVitalSignsList_profile_vitals_list[];
}

export interface GetPatientVitalSignsList_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  vitals: GetPatientVitalSignsList_profile_vitals;
}

export interface GetPatientVitalSignsList {
  profile: GetPatientVitalSignsList_profile;
}

export interface GetPatientVitalSignsListVariables {
  filterOptions?: VitalFilterInput | null;
  id: string;
}
