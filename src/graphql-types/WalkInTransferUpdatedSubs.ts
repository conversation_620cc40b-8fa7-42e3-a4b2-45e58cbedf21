/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: WalkInTransferUpdatedSubs
// ====================================================

export interface WalkInTransferUpdatedSubs_WalkInTransferUpdated_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface WalkInTransferUpdatedSubs_WalkInTransferUpdated {
  __typename: "WalkInTransferModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  transferFacilityName: string | null;
  transferFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: WalkInTransferUpdatedSubs_WalkInTransferUpdated_patientInformation | null;
}

export interface WalkInTransferUpdatedSubs {
  WalkInTransferUpdated: WalkInTransferUpdatedSubs_WalkInTransferUpdated;
}

export interface WalkInTransferUpdatedSubsVariables {
  hospitalId: string;
}
