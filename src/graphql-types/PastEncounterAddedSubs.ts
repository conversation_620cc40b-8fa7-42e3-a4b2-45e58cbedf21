/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PastEncounterAddedSubs
// ====================================================

export interface PastEncounterAddedSubs_PastEncounterAdded_details {
  __typename: "EncounterDetailModel";
  id: string;
  diagnosisDate: any | null;
  duration: string | null;
  diagnosedBy: string | null;
  specialty: string | null;
  symptoms: string[] | null;
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface PastEncounterAddedSubs_PastEncounterAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface PastEncounterAddedSubs_PastEncounterAdded {
  __typename: "PastEncounterModel";
  id: string;
  clinicName: string | null;
  clinicAddress: string | null;
  details: PastEncounterAddedSubs_PastEncounterAdded_details[];
  additionalNote: string | null;
  profile: PastEncounterAddedSubs_PastEncounterAdded_profile | null;
}

export interface PastEncounterAddedSubs {
  PastEncounterAdded: PastEncounterAddedSubs_PastEncounterAdded;
}

export interface PastEncounterAddedSubsVariables {
  profileId: string;
}
