/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PatientCareTeamInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdatePatientCareTeam
// ====================================================

export interface UpdatePatientCareTeam_updatePatientCareTeam_team {
  __typename: "PatientCareSpecialist";
  specialistFullName: string | null;
  specialistId: string | null;
  specialistRole: string | null;
  specialty: string | null;
  specialistTitle: string | null;
  rank: string | null;
}

export interface UpdatePatientCareTeam_updatePatientCareTeam {
  __typename: "PatientCareTeamModel";
  id: string;
  team: (UpdatePatientCareTeam_updatePatientCareTeam_team | null)[] | null;
}

export interface UpdatePatientCareTeam {
  updatePatientCareTeam: UpdatePatientCareTeam_updatePatientCareTeam;
}

export interface UpdatePatientCareTeamVariables {
  input: PatientCareTeamInput;
}
