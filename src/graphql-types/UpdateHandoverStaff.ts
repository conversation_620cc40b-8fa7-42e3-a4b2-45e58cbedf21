/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HandoverStaffInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateHandoverStaff
// ====================================================

export interface UpdateHandoverStaff_updateHandoverStaff_staffProfile {
  __typename: "ProfileModel";
  fullName: string;
}

export interface UpdateHandoverStaff_updateHandoverStaff {
  __typename: "HandoverStaffModel";
  id: string;
  status: string | null;
  currentShift: string | null;
  nextShift: string | null;
  staffProfileId: string | null;
  staffProfile: UpdateHandoverStaff_updateHandoverStaff_staffProfile | null;
  handoverNoteId: string;
  createdDate: any;
  updatedDate: any;
  creatorId: string | null;
}

export interface UpdateHandoverStaff {
  updateHandoverStaff: UpdateHandoverStaff_updateHandoverStaff;
}

export interface UpdateHandoverStaffVariables {
  id: string;
  input: HandoverStaffInput;
}
