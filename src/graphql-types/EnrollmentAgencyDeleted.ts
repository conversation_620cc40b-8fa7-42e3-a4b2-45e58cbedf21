/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: EnrollmentAgencyDeleted
// ====================================================

export interface EnrollmentAgencyDeleted_EnrollmentAgencyDeleted_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface EnrollmentAgencyDeleted_EnrollmentAgencyDeleted_tpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
}

export interface EnrollmentAgencyDeleted_EnrollmentAgencyDeleted_primaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface EnrollmentAgencyDeleted_EnrollmentAgencyDeleted_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface EnrollmentAgencyDeleted_EnrollmentAgencyDeleted_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface EnrollmentAgencyDeleted_EnrollmentAgencyDeleted_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface EnrollmentAgencyDeleted_EnrollmentAgencyDeleted {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
  agencyCode: string | null;
  address: string | null;
  country: string | null;
  state: string | null;
  agencyType: string | null;
  profile: EnrollmentAgencyDeleted_EnrollmentAgencyDeleted_profile | null;
  tpaNonTpa: EnrollmentAgencyDeleted_EnrollmentAgencyDeleted_tpaNonTpa | null;
  localGovernmentArea: string | null;
  primaryPhoneNumber: EnrollmentAgencyDeleted_EnrollmentAgencyDeleted_primaryPhoneNumber | null;
  primaryEmailAddress: string | null;
  secondaryPhoneNumber: EnrollmentAgencyDeleted_EnrollmentAgencyDeleted_secondaryPhoneNumber | null;
  secondaryEmailAddress: string | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: EnrollmentAgencyDeleted_EnrollmentAgencyDeleted_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: EnrollmentAgencyDeleted_EnrollmentAgencyDeleted_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  accountName: string | null;
  accountNumber: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface EnrollmentAgencyDeleted {
  EnrollmentAgencyDeleted: EnrollmentAgencyDeleted_EnrollmentAgencyDeleted[];
}

export interface EnrollmentAgencyDeletedVariables {
  hospitalId: string;
}
