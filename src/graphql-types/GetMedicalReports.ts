/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicalReportFilterInput, MedicalReportStatus, BillStatus } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetMedicalReports
// ====================================================

export interface GetMedicalReports_medicalReports_list_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface GetMedicalReports_medicalReports_list_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface GetMedicalReports_medicalReports_list_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface GetMedicalReports_medicalReports_list_reportType {
  __typename: "ReportTypeInputType";
  ref: string | null;
  name: string;
  itemId: string | null;
}

export interface GetMedicalReports_medicalReports_list_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface GetMedicalReports_medicalReports_list_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface GetMedicalReports_medicalReports_list_bill {
  __typename: "BillModel";
  id: string;
  billStatus: BillStatus;
  createdDate: any;
}

export interface GetMedicalReports_medicalReports_list {
  __typename: "MedicalReportModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  updatedBy: GetMedicalReports_medicalReports_list_updatedBy | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  createdBy: GetMedicalReports_medicalReports_list_createdBy;
  creatorId: string | null;
  creatorName: string | null;
  profile: GetMedicalReports_medicalReports_list_profile | null;
  reportDate: any | null;
  reportType: GetMedicalReports_medicalReports_list_reportType[] | null;
  patientClinifyId: string | null;
  patientFullname: string | null;
  patientPhone: string | null;
  patientEmail: string | null;
  report: string[] | null;
  rank: string | null;
  department: string | null;
  doctorName: string | null;
  specialty: string | null;
  serviceDetails: GetMedicalReports_medicalReports_list_serviceDetails[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  hospital: GetMedicalReports_medicalReports_list_hospital | null;
  hospitalId: string | null;
  status: MedicalReportStatus | null;
  bill: GetMedicalReports_medicalReports_list_bill | null;
  billStatus: string | null;
}

export interface GetMedicalReports_medicalReports {
  __typename: "MedicalReportResponse";
  totalCount: number;
  list: GetMedicalReports_medicalReports_list[];
}

export interface GetMedicalReports {
  medicalReports: GetMedicalReports_medicalReports;
}

export interface GetMedicalReportsVariables {
  filterOptions: MedicalReportFilterInput;
}
