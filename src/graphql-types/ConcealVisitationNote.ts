/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealVisitationNote
// ====================================================

export interface ConcealVisitationNote_concealVisitationNote {
  __typename: "AntenatalDetailsModel";
  id: string;
  concealVisitationNote: boolean | null;
  visitationNote: string | null;
}

export interface ConcealVisitationNote {
  concealVisitationNote: ConcealVisitationNote_concealVisitationNote;
}

export interface ConcealVisitationNoteVariables {
  id: string;
  concealStatus: boolean;
}
