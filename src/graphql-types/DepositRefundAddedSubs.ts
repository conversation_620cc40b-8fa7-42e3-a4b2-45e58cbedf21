/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Currency } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: DepositRefundAddedSubs
// ====================================================

export interface DepositRefundAddedSubs_RefundDepositAdded_collectedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface DepositRefundAddedSubs_RefundDepositAdded_withdrawnBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface DepositRefundAddedSubs_RefundDepositAdded {
  __typename: "PaymentDepositModel";
  id: string;
  depositDate: any;
  depositMethod: string | null;
  currency: Currency;
  profileId: string;
  hospitalId: string;
  amountDeposited: number;
  amountUsed: number;
  additionalNote: string | null;
  collectedById: string | null;
  creatorId: string;
  lastModifierId: string | null;
  description: string | null;
  createdDate: any;
  updatedDate: any | null;
  autoGenerated: boolean | null;
  amountRefunded: number | null;
  finalDepositBalance: number | null;
  initialDepositBalance: number | null;
  isManualRefund: boolean | null;
  refundDate: any | null;
  refundedBy: string | null;
  collectedBy: DepositRefundAddedSubs_RefundDepositAdded_collectedBy | null;
  withdrawnBy: DepositRefundAddedSubs_RefundDepositAdded_withdrawnBy | null;
}

export interface DepositRefundAddedSubs {
  RefundDepositAdded: DepositRefundAddedSubs_RefundDepositAdded;
}

export interface DepositRefundAddedSubsVariables {
  hospitalId: string;
  profileId: string;
}
