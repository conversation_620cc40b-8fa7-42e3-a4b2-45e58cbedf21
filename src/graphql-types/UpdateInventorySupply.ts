/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InventorySupplyInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateInventorySupply
// ====================================================

export interface UpdateInventorySupply_updateInventorySupply_hospital {
  __typename: "HospitalModel";
  name: string | null;
}

export interface UpdateInventorySupply_updateInventorySupply_supplyTo {
  __typename: "HospitalModel";
  name: string | null;
}

export interface UpdateInventorySupply_updateInventorySupply_order {
  __typename: "InventoryOrderModel";
  id: string;
  creatorName: string | null;
  createdDate: any | null;
}

export interface UpdateInventorySupply_updateInventorySupply {
  __typename: "InventorySupplyModel";
  id: string;
  sn: number | null;
  addedDateTime: string | null;
  deliveredDateTime: any | null;
  section: string | null;
  supplier: string | null;
  invoiceNumber: string | null;
  name: string | null;
  type: string | null;
  size: string | null;
  bedNumber: string | null;
  ward: string | null;
  group: string | null;
  flag: string | null;
  description: string | null;
  strength: string | null;
  category: string | null;
  code: string | null;
  batchNumber: string | null;
  barcode: string | null;
  bedAvailable: string | null;
  expiryDate: string | null;
  expiryStatus: string | null;
  damagedCount: string | null;
  markup: string | null;
  unitCost: string | null;
  unitSellingPrice: string | null;
  totalCost: string | null;
  totalSale: string | null;
  quantityRemaining: string | null;
  quantityPurchased: string | null;
  quantityAvailable: string | null;
  quantityDispensed: string | null;
  quantitySold: string | null;
  manufacturer: string | null;
  recievedDateTime: string | null;
  reorderLevel: string | null;
  receivedBy: string | null;
  addedBy: string | null;
  purchasedBy: string | null;
  vin: string | null;
  plateNumber: string | null;
  year: string | null;
  model: string | null;
  colour: string | null;
  status: string | null;
  comments: string | null;
  images: string | null;
  creatorId: string | null;
  createdDate: any | null;
  creatorName: string | null;
  quantityOrdered: string | null;
  canceled: boolean | null;
  orderStatus: string | null;
  quantityDelivered: string | null;
  deliveredBy: string | null;
  hospital: UpdateInventorySupply_updateInventorySupply_hospital | null;
  supplyTo: UpdateInventorySupply_updateInventorySupply_supplyTo | null;
  order: UpdateInventorySupply_updateInventorySupply_order | null;
}

export interface UpdateInventorySupply {
  updateInventorySupply: UpdateInventorySupply_updateInventorySupply;
}

export interface UpdateInventorySupplyVariables {
  id: string;
  input: InventorySupplyInput;
}
