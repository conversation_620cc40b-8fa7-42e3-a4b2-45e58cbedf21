/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateUtilizationQuantity
// ====================================================

export interface UpdateUtilizationQuantity_updateUtilizationQuantity {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  quantity: string | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
}

export interface UpdateUtilizationQuantity {
  updateUtilizationQuantity: UpdateUtilizationQuantity_updateUtilizationQuantity;
}

export interface UpdateUtilizationQuantityVariables {
  id: string;
  quantity: number;
}
