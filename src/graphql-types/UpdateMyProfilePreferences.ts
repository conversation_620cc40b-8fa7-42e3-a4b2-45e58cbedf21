/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdateProfilePreferenceInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateMyProfilePreferences
// ====================================================

export interface UpdateMyProfilePreferences_updateMyProfilePreference {
  __typename: "ProfilePreferenceModel";
  id: string;
  autoProcessClaims: boolean | null;
  autoProcessPreauthorizations: boolean | null;
}

export interface UpdateMyProfilePreferences {
  updateMyProfilePreference: UpdateMyProfilePreferences_updateMyProfilePreference;
}

export interface UpdateMyProfilePreferencesVariables {
  input: UpdateProfilePreferenceInput;
}
