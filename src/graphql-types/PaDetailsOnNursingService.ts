/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL fragment: PaDetailsOnNursingService
// ====================================================

export interface PaDetailsOnNursingService_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface PaDetailsOnNursingService_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: PaDetailsOnNursingService_preauthorizationDetails_provider | null;
}

export interface PaDetailsOnNursingService {
  __typename: "NursingServiceModel";
  preauthorizationDetails: PaDetailsOnNursingService_preauthorizationDetails | null;
}
