/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealObservations
// ====================================================

export interface ConcealObservations_concealObservations {
  __typename: "OutputDetailsModel";
  id: string;
  observations: string | null;
  concealObservations: boolean | null;
}

export interface ConcealObservations {
  concealObservations: ConcealObservations_concealObservations;
}

export interface ConcealObservationsVariables {
  id: string;
  concealStatus: boolean;
}
