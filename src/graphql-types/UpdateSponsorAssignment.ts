/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { SponsorResponseInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateSponsorAssignment
// ====================================================

export interface UpdateSponsorAssignment_updateSponsorAssignment_enrolleeSponsorAssigments_sponsorPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateSponsorAssignment_updateSponsorAssignment_enrolleeSponsorAssigments {
  __typename: "SponsorResponseDto";
  ref: string | null;
  sponsorName: string | null;
  sponsorType: string | null;
  sponsorLives: string | null;
  agencyLives: string | null;
  amountDue: string | null;
  paymentFrequency: string | null;
  nextRenewalDate: any | null;
  renewalCount: string | null;
  paymentStatus: string | null;
  paymentDateTime: any | null;
  sponsoredPremiumPerLife: string | null;
  totalSponsoredPremium: string | null;
  status: string | null;
  percentageCovered: string | null;
  sponsorEmailAddress: string | null;
  sponsorPhoneNumber: UpdateSponsorAssignment_updateSponsorAssignment_enrolleeSponsorAssigments_sponsorPhoneNumber | null;
}

export interface UpdateSponsorAssignment_updateSponsorAssignment {
  __typename: "FacilityPreferenceModel";
  id: string;
  enrolleeSponsorAssigments: UpdateSponsorAssignment_updateSponsorAssignment_enrolleeSponsorAssigments[] | null;
}

export interface UpdateSponsorAssignment {
  updateSponsorAssignment: UpdateSponsorAssignment_updateSponsorAssignment;
}

export interface UpdateSponsorAssignmentVariables {
  sponsor: SponsorResponseInput;
}
