/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: UpdateShowServiceDetails
// ====================================================

export interface UpdateShowServiceDetails_updateShowServiceDetails {
  __typename: "FacilityPreferenceModel";
  id: string;
  showServiceDetails: boolean | null;
  rolesServiceDetailsIsHidden: string[] | null;
  inventoryClass: string | null;
  hospitalId: string;
}

export interface UpdateShowServiceDetails {
  updateShowServiceDetails: UpdateShowServiceDetails_updateShowServiceDetails;
}

export interface UpdateShowServiceDetailsVariables {
  mode: boolean;
}
