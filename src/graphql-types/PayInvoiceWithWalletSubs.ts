/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PayInvoiceWithWalletSubs
// ====================================================

export interface PayInvoiceWithWalletSubs_PayInvoiceWithWallet {
  __typename: "PayInvoiceResponse";
  invoicePayments: string[];
  success: boolean;
}

export interface PayInvoiceWithWalletSubs {
  PayInvoiceWithWallet: PayInvoiceWithWalletSubs_PayInvoiceWithWallet;
}

export interface PayInvoiceWithWalletSubsVariables {
  profileId: string;
}
