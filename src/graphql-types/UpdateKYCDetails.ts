/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VirtualBankAccountKYCInput, Currency, VirtualAccountProvider } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateKYCDetails
// ====================================================

export interface UpdateKYCDetails_updateKYCDetails {
  __typename: "VirtualBankAccountModel";
  id: string;
  accountNumber: string;
  accountName: string;
  currency: Currency;
  bank: VirtualAccountProvider;
  walletId: string | null;
  bvn: string | null;
  phoneNumber: string | null;
}

export interface UpdateKYCDetails {
  updateKYCDetails: UpdateKYCDetails_updateKYCDetails;
}

export interface UpdateKYCDetailsVariables {
  accountNumber: string;
  input: VirtualBankAccountKYCInput;
}
