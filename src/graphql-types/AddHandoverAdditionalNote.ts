/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HandoverAdditionalNoteInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddHandoverAdditionalNote
// ====================================================

export interface AddHandoverAdditionalNote_addHandoverAdditionalNote {
  __typename: "HandoverAdditionalNoteModel";
  id: string;
  additionalNote: string;
  handoverNoteId: string;
  creatorId: string;
  createdDate: any;
  updatedDate: any | null;
}

export interface AddHandoverAdditionalNote {
  addHandoverAdditionalNote: AddHandoverAdditionalNote_addHandoverAdditionalNote;
}

export interface AddHandoverAdditionalNoteVariables {
  id: string;
  input: HandoverAdditionalNoteInput;
}
