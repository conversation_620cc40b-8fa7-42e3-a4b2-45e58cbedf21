/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmosAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetHmosAnalyticsData
// ====================================================

export interface GetHmosAnalyticsData_getHmosData_summary {
  __typename: "HmosSummary";
  hmoProvider: string | null;
  hospitalName: string | null;
  name: number | null;
  totalEnrollees: number | null;
  totalEnrolleeVisitations: number | null;
  totalActiveEnrollees: number | null;
  totalInactiveEnrollees: number | null;
  totalApprovedClaims: number | null;
  totalClaims: number | null;
  totalDraftClaims: number | null;
  totalRejectedClaims: number | null;
  totalSubmittedClaims: number | null;
  totalClaimsAmount: number | null;
  totalApprovedClaimsAmount: number | null;
  totalRejectedClaimsAmount: number | null;
  totalDraftClaimsAmount: number | null;
  totalSubmittedClaimsAmount: number | null;
  totalFlaggedClaims: number | null;
  totalPaidClaims: number | null;
  totalFlaggedClaimsAmount: number | null;
  totalPaidClaimsAmount: number | null;
  totalPendingClaims: number | null;
  totalPendingClaimsAmount: number | null;
  totalClaimOfficerApprovedClaims: number | null;
  totalClaimOfficerHODApprovedClaims: number | null;
  totalClaimReviewerApprovedClaims: number | null;
  totalClaimReviewerHODApprovedClaims: number | null;
  totalClaimAuditApprovedClaims: number | null;
  totalClaimAuditHODApprovedClaims: number | null;
  totalClaimAdminApprovedClaims: number | null;
  totalClaimFinanceApprovedClaims: number | null;
  totalClaimAccountApprovedClaims: number | null;
  totalClaimOfficerApprovedClaimsAmount: number | null;
  totalClaimOfficerHODApprovedClaimsAmount: number | null;
  totalClaimReviewerApprovedClaimsAmount: number | null;
  totalClaimReviewerHODApprovedClaimsAmount: number | null;
  totalClaimAuditApprovedClaimsAmount: number | null;
  totalClaimAuditHODApprovedClaimsAmount: number | null;
  totalClaimAdminApprovedClaimsAmount: number | null;
  totalClaimFinanceApprovedClaimsAmount: number | null;
  totalClaimAccountApprovedClaimsAmount: number | null;
  totalClaimOfficerRejectedClaims: number | null;
  totalClaimOfficerHODRejectedClaims: number | null;
  totalClaimReviewerRejectedClaims: number | null;
  totalClaimReviewerHODRejectedClaims: number | null;
  totalClaimAuditRejectedClaims: number | null;
  totalClaimAuditHODRejectedClaims: number | null;
  totalClaimAdminRejectedClaims: number | null;
  totalClaimFinanceRejectedClaims: number | null;
  totalClaimAccountRejectedClaims: number | null;
  totalClaimOfficerRejectedClaimsAmount: number | null;
  totalClaimOfficerHODRejectedClaimsAmount: number | null;
  totalClaimReviewerRejectedClaimsAmount: number | null;
  totalClaimReviewerHODRejectedClaimsAmount: number | null;
  totalClaimAuditRejectedClaimsAmount: number | null;
  totalClaimAuditHODRejectedClaimsAmount: number | null;
  totalClaimAdminRejectedClaimsAmount: number | null;
  totalClaimFinanceRejectedClaimsAmount: number | null;
  totalClaimAccountRejectedClaimsAmount: number | null;
  totalClaimOfficerPendingClaims: number | null;
  totalClaimOfficerHODPendingClaims: number | null;
  totalClaimReviewerPendingClaims: number | null;
  totalClaimReviewerHODPendingClaims: number | null;
  totalClaimAuditPendingClaims: number | null;
  totalClaimAuditHODPendingClaims: number | null;
  totalClaimAdminPendingClaims: number | null;
  totalClaimFinancePendingClaims: number | null;
  totalClaimAccountPendingClaims: number | null;
  totalClaimOfficerPendingClaimsAmount: number | null;
  totalClaimOfficerHODPendingClaimsAmount: number | null;
  totalClaimReviewerPendingClaimsAmount: number | null;
  totalClaimReviewerHODPendingClaimsAmount: number | null;
  totalClaimAuditPendingClaimsAmount: number | null;
  totalClaimAuditHODPendingClaimsAmount: number | null;
  totalClaimAdminPendingClaimsAmount: number | null;
  totalClaimFinancePendingClaimsAmount: number | null;
  totalClaimAccountPendingClaimsAmount: number | null;
  totalClaimConfirmationPendingClaims: number | null;
  totalClaimConfirmationPendingClaimsAmount: number | null;
  totalClaimOfficerFlaggedClaims: number | null;
  totalClaimOfficerHODFlaggedClaims: number | null;
  totalClaimReviewerFlaggedClaims: number | null;
  totalClaimReviewerHODFlaggedClaims: number | null;
  totalClaimAuditFlaggedClaims: number | null;
  totalClaimAuditHODFlaggedClaims: number | null;
  totalClaimAdminFlaggedClaims: number | null;
  totalClaimFinanceFlaggedClaims: number | null;
  totalClaimAccountFlaggedClaims: number | null;
  totalClaimOfficerFlaggedClaimsAmount: number | null;
  totalClaimOfficerHODFlaggedClaimsAmount: number | null;
  totalClaimReviewerFlaggedClaimsAmount: number | null;
  totalClaimReviewerHODFlaggedClaimsAmount: number | null;
  totalClaimAuditFlaggedClaimsAmount: number | null;
  totalClaimAuditHODFlaggedClaimsAmount: number | null;
  totalClaimAdminFlaggedClaimsAmount: number | null;
  totalClaimFinanceFlaggedClaimsAmount: number | null;
  totalClaimAccountFlaggedClaimsAmount: number | null;
  totalClaimConfirmationFlaggedClaims: number | null;
  totalClaimConfirmationFlaggedClaimsAmount: number | null;
  totalClaimOfficerStaffCount: number | null;
  totalClaimOfficerHODStaffCount: number | null;
  totalClaimReviewerStaffCount: number | null;
  totalClaimReviewerHODStaffCount: number | null;
  totalClaimAuditStaffCount: number | null;
  totalClaimAuditHODStaffCount: number | null;
  totalClaimAdminStaffCount: number | null;
  totalClaimFinanceStaffCount: number | null;
  totalClaimAccountStaffCount: number | null;
  totalClaimConfirmationStaffCount: number | null;
}

export interface GetHmosAnalyticsData_getHmosData_detailedData_utilizationStatus {
  __typename: "ClaimsApprovalInputType";
  status: string | null;
  vettingGroup: string;
}

export interface GetHmosAnalyticsData_getHmosData_detailedData {
  __typename: "HmoDetailedData";
  hospitalName: string | null;
  treatmentDateTime: string | null;
  treatmentStartDate: string | null;
  treatmentEndDate: string | null;
  visitationType: string | null;
  enrolleeNumber: string | null;
  enrolleeName: string | null;
  serviceTypeName: string | null;
  claimStatus: string | null;
  claimId: string | null;
  batchNumber: string | null;
  totalQuantity: string | null;
  amountSubmitted: string | null;
  amountApproved: string | null;
  amountRejected: string | null;
  submittedBy: string | null;
  approvedBy: string | null;
  rejectedBy: string | null;
  clinicalDiagnosis: string | null;
  flaggedBy: string | null;
  memberPlan: string | null;
  companyName: string | null;
  reasonForRejection: string | null;
  amountPaid: string | null;
  causeOfDeath: string | null;
  timeOfDeath: string | null;
  birthCount: string | null;
  deliveryDateTime: string | null;
  gestationalAge: string | null;
  age: string | null;
  gender: string | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  lga: string | null;
  ward: string | null;
  utilizationStatus: GetHmosAnalyticsData_getHmosData_detailedData_utilizationStatus[] | null;
}

export interface GetHmosAnalyticsData_getHmosData_automaticAndManualData_byRoles {
  __typename: "SummaryCount";
  name: string | null;
  count: number | null;
  manualCount: number | null;
}

export interface GetHmosAnalyticsData_getHmosData_automaticAndManualData_byStaffs {
  __typename: "SummaryCount";
  name: string | null;
  count: number | null;
  type: string | null;
  manualCount: number | null;
}

export interface GetHmosAnalyticsData_getHmosData_automaticAndManualData {
  __typename: "AutomaticAndManualHmoClaimsData";
  totalAutomated: number | null;
  totalManual: number | null;
  byRoles: GetHmosAnalyticsData_getHmosData_automaticAndManualData_byRoles[] | null;
  byStaffs: GetHmosAnalyticsData_getHmosData_automaticAndManualData_byStaffs[] | null;
}

export interface GetHmosAnalyticsData_getHmosData_deathAgeRanges {
  __typename: "CategoryCount";
  count: number | null;
  category: string | null;
}

export interface GetHmosAnalyticsData_getHmosData {
  __typename: "HmoClaimDataResponse";
  summary: GetHmosAnalyticsData_getHmosData_summary[] | null;
  detailedData: GetHmosAnalyticsData_getHmosData_detailedData[] | null;
  automaticAndManualData: GetHmosAnalyticsData_getHmosData_automaticAndManualData | null;
  totalMaleDeath: number | null;
  totalFemaleDeath: number | null;
  deathAgeRanges: GetHmosAnalyticsData_getHmosData_deathAgeRanges[] | null;
}

export interface GetHmosAnalyticsData {
  getHmosData: GetHmosAnalyticsData_getHmosData;
}

export interface GetHmosAnalyticsDataVariables {
  filter?: HmosAnalyticsFilter | null;
}
