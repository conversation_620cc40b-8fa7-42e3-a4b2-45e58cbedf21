/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UnarchiveAllergySubs
// ====================================================

export interface UnarchiveAllergySubs_AllergyUnarchived_details_clinicalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UnarchiveAllergySubs_AllergyUnarchived_details {
  __typename: "AllergyDetails";
  type: string;
  trigger: string;
  reactions: string[] | null;
  severeness: string | null;
  clinicalDiagnosis: UnarchiveAllergySubs_AllergyUnarchived_details_clinicalDiagnosis[] | null;
}

export interface UnarchiveAllergySubs_AllergyUnarchived_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface UnarchiveAllergySubs_AllergyUnarchived_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface UnarchiveAllergySubs_AllergyUnarchived {
  __typename: "AllergyModel";
  id: string;
  occurenceDate: any | null;
  duration: string | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  doctorName: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  details: UnarchiveAllergySubs_AllergyUnarchived_details[] | null;
  profileId: string | null;
  profile: UnarchiveAllergySubs_AllergyUnarchived_profile | null;
  documentUrl: string[] | null;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
  createdDate: any;
  updatedDate: any;
  medications: UnarchiveAllergySubs_AllergyUnarchived_medications[];
}

export interface UnarchiveAllergySubs {
  AllergyUnarchived: UnarchiveAllergySubs_AllergyUnarchived[];
}

export interface UnarchiveAllergySubsVariables {
  profileId: string;
}
