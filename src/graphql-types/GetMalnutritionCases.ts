/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CasesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetMalnutritionCases
// ====================================================

export interface GetMalnutritionCases_getMalnutritionCases_ageRanges {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetMalnutritionCases_getMalnutritionCases {
  __typename: "CasesSummary";
  name: number | null;
  totalFemale: number | null;
  totalMale: number | null;
  ageRanges: GetMalnutritionCases_getMalnutritionCases_ageRanges[] | null;
}

export interface GetMalnutritionCases {
  getMalnutritionCases: GetMalnutritionCases_getMalnutritionCases[];
}

export interface GetMalnutritionCasesVariables {
  filter?: CasesAnalyticsFilter | null;
}
