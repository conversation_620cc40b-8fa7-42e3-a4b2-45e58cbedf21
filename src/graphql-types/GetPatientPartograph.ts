/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetPatientPartograph
// ====================================================

export interface GetPatientPartograph_partograph_fhr {
  __typename: "FhrType";
  readingDate: any | null;
  value: string | null;
  time: string | null;
}

export interface GetPatientPartograph_partograph_amnoiticFluid {
  __typename: "AmnoiticFluidType";
  readingDate: any | null;
  value: string | null;
  moulding: string | null;
  time: string | null;
}

export interface GetPatientPartograph_partograph_cervixDecent {
  __typename: "CervixDecentType";
  readingDate: any | null;
  cervix: string | null;
  decent: string | null;
  period: any | null;
  time: string | null;
}

export interface GetPatientPartograph_partograph_contraction {
  __typename: "ContractionType";
  readingDate: any | null;
  value: string | null;
  duration: string | null;
  time: string | null;
}

export interface GetPatientPartograph_partograph_drugIv {
  __typename: "DrugIvType";
  readingDate: any | null;
  drugName: string[] | null;
  time: string | null;
}

export interface GetPatientPartograph_partograph_oxytocin {
  __typename: "OxytocinType";
  readingDate: any | null;
  value: string | null;
  drops: string | null;
  time: string | null;
}

export interface GetPatientPartograph_partograph_pulseBP {
  __typename: "PulseBPType";
  readingDate: any | null;
  pulse: string | null;
  bpSystolic: string | null;
  bpDiastolic: string | null;
  time: string | null;
}

export interface GetPatientPartograph_partograph_temperature {
  __typename: "TemperatureType";
  readingDate: any | null;
  value: string | null;
  time: string | null;
  unit: string | null;
}

export interface GetPatientPartograph_partograph_urine {
  __typename: "UrineType";
  readingDate: any | null;
  protein: string | null;
  acetone: string | null;
  volume: string | null;
  time: string | null;
}

export interface GetPatientPartograph_partograph_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface GetPatientPartograph_partograph {
  __typename: "PartographModel";
  id: string;
  notes: string | null;
  fhr: GetPatientPartograph_partograph_fhr[] | null;
  amnoiticFluid: GetPatientPartograph_partograph_amnoiticFluid[] | null;
  cervixDecent: GetPatientPartograph_partograph_cervixDecent[] | null;
  contraction: GetPatientPartograph_partograph_contraction[] | null;
  drugIv: GetPatientPartograph_partograph_drugIv[] | null;
  oxytocin: GetPatientPartograph_partograph_oxytocin[] | null;
  pulseBP: GetPatientPartograph_partograph_pulseBP[] | null;
  temperature: GetPatientPartograph_partograph_temperature[] | null;
  urine: GetPatientPartograph_partograph_urine[] | null;
  profileId: string | null;
  profile: GetPatientPartograph_partograph_profile | null;
  facilityName: string | null;
  facilityAddress: string | null;
}

export interface GetPatientPartograph {
  partograph: GetPatientPartograph_partograph;
}

export interface GetPatientPartographVariables {
  clinifyId: string;
  id: string;
}
