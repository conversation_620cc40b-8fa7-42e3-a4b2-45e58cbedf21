/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MaternalHealthAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPregnantWomenCounselled
// ====================================================

export interface GetPregnantWomenCounselled_getPregnantWomenCounselled {
  __typename: "MaternalHealthSummary";
  name: number | null;
  totalPregnantWomenCounselled: number | null;
  totalCounselledFGM: number | null;
  totalCounselledFamilyPlanning: number | null;
  totalCounselledMaternalNutrition: number | null;
  totalCounselledLabourBirthPreparedness: number | null;
  category: string | null;
}

export interface GetPregnantWomenCounselled {
  getPregnantWomenCounselled: GetPregnantWomenCounselled_getPregnantWomenCounselled[];
}

export interface GetPregnantWomenCounselledVariables {
  filter?: MaternalHealthAnalyticsFilter | null;
}
