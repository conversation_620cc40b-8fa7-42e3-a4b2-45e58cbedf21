/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NewWalkInTransferInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddWalkInTransfer
// ====================================================

export interface AddWalkInTransfer_addWalkInTransfer_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface AddWalkInTransfer_addWalkInTransfer {
  __typename: "WalkInTransferModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  transferFacilityName: string | null;
  transferFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: AddWalkInTransfer_addWalkInTransfer_patientInformation | null;
}

export interface AddWalkInTransfer {
  addWalkInTransfer: AddWalkInTransfer_addWalkInTransfer;
}

export interface AddWalkInTransferVariables {
  input: NewWalkInTransferInput;
}
