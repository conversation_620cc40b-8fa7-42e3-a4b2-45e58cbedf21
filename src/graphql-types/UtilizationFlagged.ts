/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UtilizationFlagged
// ====================================================

export interface UtilizationFlagged_UtilizationFlagged_flags {
  __typename: "FlagDto";
  flag: string | null;
  ruleId: string | null;
}

export interface UtilizationFlagged_UtilizationFlagged {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  flags: UtilizationFlagged_UtilizationFlagged_flags[] | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
}

export interface UtilizationFlagged {
  UtilizationFlagged: UtilizationFlagged_UtilizationFlagged;
}

export interface UtilizationFlaggedVariables {
  hospitalId: string;
}
