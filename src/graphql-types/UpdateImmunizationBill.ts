/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ImmunizationInput, immunizationCategory, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateImmunizationBill
// ====================================================

export interface UpdateImmunizationBill_updateImmunizationBill_details_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UpdateImmunizationBill_updateImmunizationBill_details_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: UpdateImmunizationBill_updateImmunizationBill_details_preauthorizationDetails_provider | null;
}

export interface UpdateImmunizationBill_updateImmunizationBill_details {
  __typename: "ImmunizationDetailModel";
  id: string;
  code: string | null;
  administrationType: string | null;
  period: string | null;
  adverseEffectsFollowingImmunization: string | null;
  provider: string | null;
  administrationSource: string | null;
  duration: string | null;
  immunizationName: string;
  priority: string | null;
  administeredDate: any | null;
  expiryDate: any | null;
  outcomeOfInvestigation: string | null;
  class: string | null;
  dosageUnit: string | null;
  stateEffects: string | null;
  batchNumber: string | null;
  category: immunizationCategory | null;
  quantity: string | null;
  adverseEffectsInvestigated: string | null;
  dosage: string | null;
  method: string | null;
  administratorName: string | null;
  itemId: string | null;
  preauthorizationDetails: UpdateImmunizationBill_updateImmunizationBill_details_preauthorizationDetails | null;
}

export interface UpdateImmunizationBill_updateImmunizationBill_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  paymentType: string | null;
  patientType: string | null;
  reference: string | null;
}

export interface UpdateImmunizationBill_updateImmunizationBill_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface UpdateImmunizationBill_updateImmunizationBill_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: UpdateImmunizationBill_updateImmunizationBill_profile_coverageDetails_provider | null;
}

export interface UpdateImmunizationBill_updateImmunizationBill_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: UpdateImmunizationBill_updateImmunizationBill_profile_coverageDetails[] | null;
}

export interface UpdateImmunizationBill_updateImmunizationBill_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
}

export interface UpdateImmunizationBill_updateImmunizationBill {
  __typename: "ImmunizationModel";
  id: string;
  details: UpdateImmunizationBill_updateImmunizationBill_details[] | null;
  appointmentId: string | null;
  remindMe: boolean | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  additionalNote: string | null;
  hmoProviderId: string | null;
  serviceDetails: UpdateImmunizationBill_updateImmunizationBill_serviceDetails[] | null;
  isPackage: boolean;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  concealAdditionalNote: boolean | null;
  hospitalId: string | null;
  profileId: string | null;
  profile: UpdateImmunizationBill_updateImmunizationBill_profile | null;
  billStatus: string | null;
  hmoClaim: UpdateImmunizationBill_updateImmunizationBill_hmoClaim | null;
}

export interface UpdateImmunizationBill {
  updateImmunizationBill: UpdateImmunizationBill_updateImmunizationBill;
}

export interface UpdateImmunizationBillVariables {
  id: string;
  input: ImmunizationInput;
}
