/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: EnrollmentAgencyUpdated
// ====================================================

export interface EnrollmentAgencyUpdated_EnrollmentAgencyUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
}

export interface EnrollmentAgencyUpdated_EnrollmentAgencyUpdated_tpaNonTpa {
  __typename: "EnrollmentTpaNonTpaModel";
  id: string;
  name: string | null;
}

export interface EnrollmentAgencyUpdated_EnrollmentAgencyUpdated_primaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface EnrollmentAgencyUpdated_EnrollmentAgencyUpdated_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface EnrollmentAgencyUpdated_EnrollmentAgencyUpdated_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface EnrollmentAgencyUpdated_EnrollmentAgencyUpdated_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface EnrollmentAgencyUpdated_EnrollmentAgencyUpdated {
  __typename: "EnrollmentAgencyModel";
  id: string;
  name: string | null;
  agencyCode: string | null;
  address: string | null;
  country: string | null;
  state: string | null;
  agencyType: string | null;
  profile: EnrollmentAgencyUpdated_EnrollmentAgencyUpdated_profile | null;
  tpaNonTpa: EnrollmentAgencyUpdated_EnrollmentAgencyUpdated_tpaNonTpa | null;
  localGovernmentArea: string | null;
  primaryPhoneNumber: EnrollmentAgencyUpdated_EnrollmentAgencyUpdated_primaryPhoneNumber | null;
  primaryEmailAddress: string | null;
  secondaryPhoneNumber: EnrollmentAgencyUpdated_EnrollmentAgencyUpdated_secondaryPhoneNumber | null;
  secondaryEmailAddress: string | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: EnrollmentAgencyUpdated_EnrollmentAgencyUpdated_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: EnrollmentAgencyUpdated_EnrollmentAgencyUpdated_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  accountName: string | null;
  accountNumber: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface EnrollmentAgencyUpdated {
  EnrollmentAgencyUpdated: EnrollmentAgencyUpdated_EnrollmentAgencyUpdated;
}

export interface EnrollmentAgencyUpdatedVariables {
  hospitalId: string;
}
