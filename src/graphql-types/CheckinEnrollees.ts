/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CheckinInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: CheckinEnrollees
// ====================================================

export interface CheckinEnrollees_checkinEnrollees_list_hmos_list_provider {
  __typename: "HmoProviderModel";
  id: string;
}

export interface CheckinEnrollees_checkinEnrollees_list_hmos_list {
  __typename: "HmoProfileModel";
  checkedIn: boolean | null;
  verificationCode: string | null;
  visitationId: string | null;
  provider: CheckinEnrollees_checkinEnrollees_list_hmos_list_provider;
}

export interface CheckinEnrollees_checkinEnrollees_list_hmos {
  __typename: "HmoProfileResponse";
  list: CheckinEnrollees_checkinEnrollees_list_hmos_list[];
}

export interface CheckinEnrollees_checkinEnrollees_list {
  __typename: "ProfileModel";
  id: string;
  hmos: CheckinEnrollees_checkinEnrollees_list_hmos;
  lastCheckinDate: any | null;
}

export interface CheckinEnrollees_checkinEnrollees {
  __typename: "ProfilesResponse";
  list: CheckinEnrollees_checkinEnrollees_list[];
}

export interface CheckinEnrollees {
  checkinEnrollees: CheckinEnrollees_checkinEnrollees;
}

export interface CheckinEnrolleesVariables {
  input: CheckinInput;
}
