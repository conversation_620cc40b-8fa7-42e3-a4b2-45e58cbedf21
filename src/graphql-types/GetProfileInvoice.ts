/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InvoiceListFilterInput, InvoiceStatus, PercentOrAmount, Currency, VirtualAccountProvider, PayoutStatus } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetProfileInvoice
// ====================================================

export interface GetProfileInvoice_profile_invoices_list_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface GetProfileInvoice_profile_invoices_list_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface GetProfileInvoice_profile_invoices_list_recipient {
  __typename: "InvoiceRecipient";
  address: string | null;
  email: string | null;
  name: string;
  phone: string | null;
  clinifyId: string | null;
}

export interface GetProfileInvoice_profile_invoices_list_senderHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
  facilityLogo: string | null;
  website: string | null;
}

export interface GetProfileInvoice_profile_invoices_list_virtualAccount {
  __typename: "VirtualBankAccountModel";
  id: string;
  currency: Currency;
  updatedDate: any;
  createdDate: any;
  accountName: string;
  accountNumber: string;
  bank: VirtualAccountProvider;
  expiryDate: any | null;
}

export interface GetProfileInvoice_profile_invoices_list_employeesDetails_dependents {
  __typename: "DependentInfo";
  fullName: string;
  enrolleeId: string | null;
  relationship: string | null;
}

export interface GetProfileInvoice_profile_invoices_list_employeesDetails {
  __typename: "EmployeesInfo";
  enrolleeId: string | null;
  fullName: string;
  planCategory: string | null;
  planType: string | null;
  planAmount: number | null;
  paymentFrequency: string | null;
  planStartDate: any | null;
  planDueDate: any | null;
  planStatus: string | null;
  isCovered: boolean | null;
  dependents: GetProfileInvoice_profile_invoices_list_employeesDetails_dependents[] | null;
}

export interface GetProfileInvoice_profile_invoices_list_sponsorDetails {
  __typename: "SponsorEnrolleeDetails";
  status: string | null;
  fullName: string | null;
  isCovered: boolean | null;
  memberNumber: string | null;
  planDueDate: any | null;
  planStartDate: any | null;
}

export interface GetProfileInvoice_profile_invoices_list_invoiceItems {
  __typename: "InvoiceItemModel";
  id: string;
  description: string;
  updatedDate: any;
  createdDate: any;
  invoiceType: string | null;
  discountAmount: number | null;
  discountPercentage: number | null;
  invoiceId: string;
  quantity: number;
  unitPrice: number | null;
}

export interface GetProfileInvoice_profile_invoices_list_invoicePayments {
  __typename: "InvoicePaymentModel";
  id: string;
  paymentStatus: string;
  paymentMethod: string;
  amountPaid: number;
  payoutStatus: PayoutStatus;
}

export interface GetProfileInvoice_profile_invoices_list {
  __typename: "InvoiceModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  discountPercentage: number | null;
  discountAmount: number | null;
  profileId: string | null;
  employerId: string | null;
  creatorId: string;
  description: string | null;
  subTotal: number;
  /**
   * Returns total amount in lowest denomination
   */
  totalAmount: number;
  createdBy: GetProfileInvoice_profile_invoices_list_createdBy;
  lastModifierId: string | null;
  updatedBy: GetProfileInvoice_profile_invoices_list_updatedBy | null;
  additionalNote: string | null;
  amountPaid: number;
  bankTransactionIds: string[] | null;
  dueDate: any;
  invoiceReference: string;
  invoiceStatus: InvoiceStatus;
  issueDate: any;
  paymentDate: any | null;
  sponsorName: string | null;
  sponsorRef: string | null;
  nextYearlyPremium: number | null;
  sponsorLivesCovered: number | null;
  agencyLivesCovered: number | null;
  sponsorPremiumPerLives: number | null;
  recipient: GetProfileInvoice_profile_invoices_list_recipient;
  senderHospitalId: string | null;
  senderHospital: GetProfileInvoice_profile_invoices_list_senderHospital;
  professionalFeeAmount: number | null;
  professionalFeePercentage: number | null;
  controlledDiscount: PercentOrAmount | null;
  controlledProfessionalFee: PercentOrAmount | null;
  vatPercentage: number | null;
  vatAmount: number | null;
  controlledVat: PercentOrAmount | null;
  virtualAccount: GetProfileInvoice_profile_invoices_list_virtualAccount | null;
  employeesDetails: GetProfileInvoice_profile_invoices_list_employeesDetails[] | null;
  sponsorDetails: GetProfileInvoice_profile_invoices_list_sponsorDetails[] | null;
  paymentFrequency: string | null;
  plasticIdCardCount: number | null;
  plasticIdCardAmount: number | null;
  laminatedIdCardCount: number | null;
  laminatedIdCardAmount: number | null;
  creatorName: string;
  lastModifierName: string | null;
  periodStartDate: any | null;
  periodEndDate: any | null;
  invoiceItems: GetProfileInvoice_profile_invoices_list_invoiceItems[];
  invoicePayments: GetProfileInvoice_profile_invoices_list_invoicePayments[] | null;
}

export interface GetProfileInvoice_profile_invoices {
  __typename: "InvoiceListResponse";
  totalCount: number;
  list: GetProfileInvoice_profile_invoices_list[];
}

export interface GetProfileInvoice_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  invoices: GetProfileInvoice_profile_invoices;
}

export interface GetProfileInvoice {
  profile: GetProfileInvoice_profile;
}

export interface GetProfileInvoiceVariables {
  filterOptions?: InvoiceListFilterInput | null;
  id: string;
}
