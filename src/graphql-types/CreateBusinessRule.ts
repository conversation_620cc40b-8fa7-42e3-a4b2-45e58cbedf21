/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NewBusinessRuleInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: CreateBusinessRule
// ====================================================

export interface CreateBusinessRule_createBusinessRule_items_extra {
  __typename: "BusinessRuleItemExtra";
  frequencyUnit: string | null;
  frequencyTarget: string | null;
  frequencyTargetValue: string | null;
  frequencyTargetUnit: string | null;
  frequencyTargetQuantity: string | null;
  frequencyTargetOperator: string | null;
}

export interface CreateBusinessRule_createBusinessRule_items {
  __typename: "BusinessRuleItemModel";
  id: string;
  type: string;
  category: string;
  operator: string;
  value: string;
  extra: CreateBusinessRule_createBusinessRule_items_extra | null;
}

export interface CreateBusinessRule_createBusinessRule {
  __typename: "BusinessRuleModel";
  id: string;
  flag: string | null;
  matchAll: boolean | null;
  sumAll: boolean | null;
  items: CreateBusinessRule_createBusinessRule_items[];
}

export interface CreateBusinessRule {
  createBusinessRule: CreateBusinessRule_createBusinessRule[];
}

export interface CreateBusinessRuleVariables {
  rules: NewBusinessRuleInput[];
}
