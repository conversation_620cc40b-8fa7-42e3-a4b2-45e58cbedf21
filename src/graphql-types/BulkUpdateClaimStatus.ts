/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: BulkUpdateClaimStatus
// ====================================================

export interface BulkUpdateClaimStatus_bulkUpdateClaimStatus {
  __typename: "HmoClaimModel";
  id: string;
  status: string | null;
  updatedDate: any;
  lastModifierName: string | null;
}

export interface BulkUpdateClaimStatus {
  bulkUpdateClaimStatus: BulkUpdateClaimStatus_bulkUpdateClaimStatus[];
}

export interface BulkUpdateClaimStatusVariables {
  claimIds: string[];
  status: string;
}
