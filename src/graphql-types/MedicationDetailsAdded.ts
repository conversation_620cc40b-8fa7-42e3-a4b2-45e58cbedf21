/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicationOptionType, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: MedicationDetailsAdded
// ====================================================

export interface MedicationDetailsAdded_MedicationDetailsAdded_medicationConsumables {
  __typename: "MedicationConsumables";
  name: string | null;
  drugInventoryId: string | null;
  unitPrice: number | null;
  quantity: string | null;
  inventoryClass: string | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_priceDetails {
  __typename: "MedPriceDetailInputType";
  type: string | null;
  name: string | null;
  pricePerUnit: string | null;
  patientType: string | null;
  paymentType: string | null;
  quantity: string | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: MedicationDetailsAdded_MedicationDetailsAdded_preauthorizationDetails_provider | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_chemoDrugs_investigationDetails {
  __typename: "ChemoInvestigationDetails";
  investigationName: string | null;
  investigationType: string | null;
  investigationPerformed: boolean | null;
  investigationVerified: boolean | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_chemoDrugs_administrationRegister {
  __typename: "OncologyDrugAdministrationRegistration";
  administeredBy: string | null;
  administratorId: string | null;
  administrationDateTime: any | null;
  period: string;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_chemoDrugs_oncologyConsultationHistory_chemoComments {
  __typename: "ChemoCommentInputType";
  cycleNumber: number;
  section: string;
  comment: string | null;
  creatorId: string;
  creatorName: string;
  createdDate: any;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_chemoDrugs_oncologyConsultationHistory {
  __typename: "OncologyConsultationHistoryModel";
  id: string;
  chemoComments: MedicationDetailsAdded_MedicationDetailsAdded_chemoDrugs_oncologyConsultationHistory_chemoComments[] | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_chemoDrugs {
  __typename: "OncologyChemoDrugModel";
  id: string;
  drugName: string;
  route: string | null;
  infusionUsed: string | null;
  dosage: string | null;
  dosagePercentage: string | null;
  totalDose: string | null;
  adjustedDose: string | null;
  quantity: string | null;
  day: string;
  cycleNumber: number;
  drugId: string | null;
  note: string | null;
  section: string;
  frequency: string | null;
  combinationGroupName: string | null;
  combinationName: string | null;
  chemoDiagnosis: string | null;
  inventoryClass: string | null;
  medicationDetailsId: string | null;
  investigationDetails: MedicationDetailsAdded_MedicationDetailsAdded_chemoDrugs_investigationDetails[] | null;
  administrationRegister: MedicationDetailsAdded_MedicationDetailsAdded_chemoDrugs_administrationRegister[] | null;
  oncologyConsultationHistory: MedicationDetailsAdded_MedicationDetailsAdded_chemoDrugs_oncologyConsultationHistory | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_dispenseRegister_details_periods_audits {
  __typename: "dospenseRegisterAudit";
  fullName: string | null;
  dateTime: any | null;
  desc: string | null;
  checkId: number | null;
  profileId: string | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_dispenseRegister_details_periods {
  __typename: "PeriodsDetails";
  no: number | null;
  values: string | null;
  count: number | null;
  audits: MedicationDetailsAdded_MedicationDetailsAdded_dispenseRegister_details_periods_audits[] | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_dispenseRegister_details {
  __typename: "dispenseRegisterDetails";
  medicationName: string;
  reference: string | null;
  periodName: string;
  periods: MedicationDetailsAdded_MedicationDetailsAdded_dispenseRegister_details_periods[];
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_dispenseRegister {
  __typename: "DispenseRegistersModel";
  id: string;
  details: MedicationDetailsAdded_MedicationDetailsAdded_dispenseRegister_details | null;
  creator: string | null;
  updater: string | null;
  updatedDate: any;
  createdDate: any;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_oxygenTherapy_details {
  __typename: "OxygenTherapyDetails";
  administeredBy: string | null;
  administrationDateTime: any | null;
  administrationHour: string | null;
  saturation: string | null;
  therapyType: string | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_oxygenTherapy {
  __typename: "OxygenTherapyModel";
  id: string;
  createdDate: any;
  creator: string | null;
  medicationDetailId: string | null;
  updatedDate: any;
  updater: string | null;
  details: MedicationDetailsAdded_MedicationDetailsAdded_oxygenTherapy_details[] | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_infusion_details {
  __typename: "MedicationInfusionDetails";
  typeOrStrength: string | null;
  volume: string | null;
  drugName: string | null;
  dosage: string | null;
  route: string | null;
  dripRate: string | null;
  batchNumber: string | null;
  prescribedBy: string | null;
  administrationDateTime: any | null;
  administeredBy: string | null;
  checkedBy: string | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded_infusion {
  __typename: "MedicationInfusionModel";
  id: string;
  creator: string | null;
  updater: string | null;
  createdDate: any;
  updatedDate: any;
  medicationDetailId: string | null;
  details: MedicationDetailsAdded_MedicationDetailsAdded_infusion_details[] | null;
}

export interface MedicationDetailsAdded_MedicationDetailsAdded {
  __typename: "MedicationDetailsModel";
  id: string;
  datePrescribed: any | null;
  duration: string | null;
  medicationName: string | null;
  medicationCategory: string | null;
  purpose: string | null;
  administrationMethod: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  prescriptionNote: string | null;
  concealPrescriptionNote: boolean | null;
  type: string | null;
  quantity: string | null;
  startDate: any | null;
  endDate: any | null;
  bank: string | null;
  drugInventoryId: string | null;
  option: MedicationOptionType | null;
  unitPrice: string | null;
  medicationStatus: string | null;
  provider: string | null;
  fromBundle: string | null;
  medicationConsumables: MedicationDetailsAdded_MedicationDetailsAdded_medicationConsumables[] | null;
  priceDetails: MedicationDetailsAdded_MedicationDetailsAdded_priceDetails | null;
  discontinue: string | null;
  discontinueReason: string | null;
  adverseEffectsFollowingMedication: string | null;
  stateEffects: string | null;
  adverseEffectsInvestigated: string | null;
  outcomeOfInvestigation: string | null;
  refillNumber: number | null;
  frequency: string | null;
  isPackage: boolean;
  hospitalId: string | null;
  medicationType: string | null;
  preauthorizationDetails: MedicationDetailsAdded_MedicationDetailsAdded_preauthorizationDetails | null;
  diagnosis: MedicationDetailsAdded_MedicationDetailsAdded_diagnosis[] | null;
  chemoDrugs: MedicationDetailsAdded_MedicationDetailsAdded_chemoDrugs[] | null;
  inventoryClass: string | null;
  dispenseRegister: MedicationDetailsAdded_MedicationDetailsAdded_dispenseRegister | null;
  oxygenTherapy: MedicationDetailsAdded_MedicationDetailsAdded_oxygenTherapy | null;
  infusion: MedicationDetailsAdded_MedicationDetailsAdded_infusion | null;
  medicationId: string | null;
}

export interface MedicationDetailsAdded {
  MedicationDetailsAdded: MedicationDetailsAdded_MedicationDetailsAdded;
}

export interface MedicationDetailsAddedVariables {
  profileId: string;
}
