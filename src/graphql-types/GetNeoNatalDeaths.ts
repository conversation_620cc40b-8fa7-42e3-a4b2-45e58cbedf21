/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { DeathsAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetNeoNatalDeaths
// ====================================================

export interface GetNeoNatalDeaths_getNeoNatalDeaths_byCauseOfDeath {
  __typename: "CategoryCount";
  category: string | null;
  count: number | null;
}

export interface GetNeoNatalDeaths_getNeoNatalDeaths {
  __typename: "DeathsSummary";
  name: number | null;
  totalFemale: number | null;
  totalMale: number | null;
  byCauseOfDeath: GetNeoNatalDeaths_getNeoNatalDeaths_byCauseOfDeath[] | null;
}

export interface GetNeoNatalDeaths {
  getNeoNatalDeaths: GetNeoNatalDeaths_getNeoNatalDeaths;
}

export interface GetNeoNatalDeathsVariables {
  filter?: DeathsAnalyticsFilter | null;
}
