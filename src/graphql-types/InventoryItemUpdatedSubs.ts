/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: InventoryItemUpdatedSubs
// ====================================================

export interface InventoryItemUpdatedSubs_InventoryItemUpdated_inventoryItems {
  __typename: "HospitalInventoryItem";
  id: string | null;
  name: string | null;
  description: string | null;
  createdOn: any | null;
  creatorName: string | null;
}

export interface InventoryItemUpdatedSubs_InventoryItemUpdated {
  __typename: "HospitalModel";
  id: string;
  inventoryItems: InventoryItemUpdatedSubs_InventoryItemUpdated_inventoryItems[] | null;
}

export interface InventoryItemUpdatedSubs {
  InventoryItemUpdated: InventoryItemUpdatedSubs_InventoryItemUpdated;
}

export interface InventoryItemUpdatedSubsVariables {
  hospitalId: string;
}
