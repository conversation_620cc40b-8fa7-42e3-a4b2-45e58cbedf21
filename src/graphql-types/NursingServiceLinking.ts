/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: NursingServiceLinking
// ====================================================

export interface NursingServiceLinking_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface NursingServiceLinking_admissions {
  __typename: "AdmissionModel";
  id: string;
}

export interface NursingServiceLinking_consultations {
  __typename: "ConsultationModel";
  id: string;
}

export interface NursingServiceLinking_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface NursingServiceLinking_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface NursingServiceLinking_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface NursingServiceLinking_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface NursingServiceLinking_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface NursingServiceLinking_immunizations {
  __typename: "AdmissionModel";
  id: string;
}

export interface NursingServiceLinking {
  __typename: "NursingServiceModel";
  vitals: NursingServiceLinking_vitals[];
  admissions: NursingServiceLinking_admissions[];
  consultations: NursingServiceLinking_consultations[];
  medications: NursingServiceLinking_medications[];
  surgeries: NursingServiceLinking_surgeries[];
  investigations: NursingServiceLinking_investigations[];
  labTests: NursingServiceLinking_labTests[];
  radiology: NursingServiceLinking_radiology[];
  immunizations: NursingServiceLinking_immunizations[];
}
