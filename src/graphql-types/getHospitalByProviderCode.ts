/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmoFilterOptions } from "./globalTypes";

// ====================================================
// GraphQL query operation: getHospitalByProviderCode
// ====================================================

export interface getHospitalByProviderCode_getHospitalByProviderCode_list {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface getHospitalByProviderCode_getHospitalByProviderCode {
  __typename: "HmoHospitalResponse";
  totalCount: number;
  list: getHospitalByProviderCode_getHospitalByProviderCode_list[];
}

export interface getHospitalByProviderCode {
  getHospitalByProviderCode: getHospitalByProviderCode_getHospitalByProviderCode;
}

export interface getHospitalByProviderCodeVariables {
  providerCode: string;
  filterOptions?: HmoFilterOptions | null;
}
