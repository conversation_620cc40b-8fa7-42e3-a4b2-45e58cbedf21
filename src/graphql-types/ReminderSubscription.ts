/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: ReminderSubscription
// ====================================================

export interface ReminderSubscription_ReminderSubscription_data_metaData {
  __typename: "Reminders";
  id: string | null;
  clinifyId: string | null;
  profileId: string | null;
  hospitalId: string | null;
  providerId: string | null;
  claimId: string | null;
  preAuthId: string | null;
  referralId: string | null;
  utilisationId: string | null;
  enrolleeId: string | null;
  ids: string[] | null;
}

export interface ReminderSubscription_ReminderSubscription_data {
  __typename: "RemindersModel";
  id: string;
  title: string;
  description: string;
  tag: string;
  priority: string | null;
  dueDate: any | null;
  createdDate: any;
  profileId: string | null;
  hospitalId: string;
  hmoProviderId: string | null;
  icon: string;
  viewedBy: string[] | null;
  isSeen: boolean;
  metaData: ReminderSubscription_ReminderSubscription_data_metaData | null;
}

export interface ReminderSubscription_ReminderSubscription {
  __typename: "ReminderSubscriptionResponse";
  triggeredBy: string;
  data: ReminderSubscription_ReminderSubscription_data;
}

export interface ReminderSubscription {
  ReminderSubscription: ReminderSubscription_ReminderSubscription;
}
