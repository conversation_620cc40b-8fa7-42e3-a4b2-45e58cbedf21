/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PricesFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetOnlyHospitalPricesList
// ====================================================

export interface GetOnlyHospitalPricesList_hospital_prices_list {
  __typename: "PricesModel";
  id: string;
  name: string | null;
  code: string | null;
  category: string | null;
  price: string | null;
  aliasCode: string | null;
  serviceType: string | null;
  providerCode: string | null;
  aliasName: string | null;
}

export interface GetOnlyHospitalPricesList_hospital_prices {
  __typename: "PriceResponse";
  totalCount: number;
  list: GetOnlyHospitalPricesList_hospital_prices_list[];
}

export interface GetOnlyHospitalPricesList_hospital {
  __typename: "HospitalModel";
  id: string;
  /**
   * Get Hospital Prices
   */
  prices: GetOnlyHospitalPricesList_hospital_prices;
}

export interface GetOnlyHospitalPricesList {
  hospital: GetOnlyHospitalPricesList_hospital;
}

export interface GetOnlyHospitalPricesListVariables {
  filterOptions?: PricesFilterInput | null;
}
