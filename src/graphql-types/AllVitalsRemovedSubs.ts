/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: AllVitalsRemovedSubs
// ====================================================

export interface AllVitalsRemovedSubs_AllVitalsRemoved {
  __typename: "DeleteAllVitalsResponse";
  vitalsId: string | null;
  temperatureId: string | null;
  respiratoryRateId: string | null;
  pulseRateId: string | null;
  bloodPressureId: string | null;
  bloodGlucoseId: string | null;
  anthropometryId: string | null;
  painId: string | null;
}

export interface AllVitalsRemovedSubs {
  AllVitalsRemoved: AllVitalsRemovedSubs_AllVitalsRemoved;
}

export interface AllVitalsRemovedSubsVariables {
  profileId: string;
}
