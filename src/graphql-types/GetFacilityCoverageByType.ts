/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetFacilityCoverageByType
// ====================================================

export interface GetFacilityCoverageByType_getFacilityCoverageByType {
  __typename: "CoverageProviderDetails";
  id: string;
  name: string;
}

export interface GetFacilityCoverageByType {
  getFacilityCoverageByType: GetFacilityCoverageByType_getFacilityCoverageByType[];
}

export interface GetFacilityCoverageByTypeVariables {
  type: string;
}
