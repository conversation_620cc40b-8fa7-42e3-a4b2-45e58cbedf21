/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { AdmissionAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetAdmissionDischargeData
// ====================================================

export interface GetAdmissionDischargeData_getAdmissionDischargeData_summary {
  __typename: "AdmissionSummary";
  totalAdmissions: number | null;
  totalDischarge: number | null;
  name: number | null;
}

export interface GetAdmissionDischargeData_getAdmissionDischargeData_list {
  __typename: "AdmissionAndDischargeDetailed";
  patientName: string | null;
  patientAge: string | null;
  patientGender: string | null;
  diagnosis: string[] | null;
  dateOfAdmission: string | null;
  dateOfDischarge: string | null;
  noOfDaysSpent: number | null;
}

export interface GetAdmissionDischargeData_getAdmissionDischargeData {
  __typename: "AdmissionAndDischargeResponse";
  summary: GetAdmissionDischargeData_getAdmissionDischargeData_summary[] | null;
  list: GetAdmissionDischargeData_getAdmissionDischargeData_list[] | null;
}

export interface GetAdmissionDischargeData {
  getAdmissionDischargeData: GetAdmissionDischargeData_getAdmissionDischargeData;
}

export interface GetAdmissionDischargeDataVariables {
  filter?: AdmissionAnalyticsFilter | null;
}
