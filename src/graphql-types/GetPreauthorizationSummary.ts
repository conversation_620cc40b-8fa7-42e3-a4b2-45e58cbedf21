/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PreauthorizationFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPreauthorizationSummary
// ====================================================

export interface GetPreauthorizationSummary_getPreauthorizationSummary {
  __typename: "PreauthorizationSummary";
  totalPreauthorizations: number | null;
  totalPendingPreauthorizations: number | null;
  totalApprovedApprovedPreauthorizations: number | null;
  totalRejectedPreauthorizations: number | null;
  totalPreauthorizationsAmount: number | null;
  totalPendingPreauthorizationsAmount: number | null;
  totalApprovedApprovedPreauthorizationsAmount: number | null;
  totalRejectedPreauthorizationsAmount: number | null;
}

export interface GetPreauthorizationSummary {
  getPreauthorizationSummary: GetPreauthorizationSummary_getPreauthorizationSummary;
}

export interface GetPreauthorizationSummaryVariables {
  filterOptions: PreauthorizationFilterInput;
  profileId?: string | null;
}
