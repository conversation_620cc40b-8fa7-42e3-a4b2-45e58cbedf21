/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: AddInvoiceItemSubs
// ====================================================

export interface AddInvoiceItemSubs_InvoiceItemAdded_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
}

export interface AddInvoiceItemSubs_InvoiceItemAdded_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
}

export interface AddInvoiceItemSubs_InvoiceItemAdded {
  __typename: "InvoiceItemModel";
  id: string;
  description: string;
  updatedDate: any;
  createdDate: any;
  invoiceType: string | null;
  discountAmount: number | null;
  discountPercentage: number | null;
  invoiceId: string;
  quantity: number;
  unitPrice: number | null;
  createdBy: AddInvoiceItemSubs_InvoiceItemAdded_createdBy;
  updatedBy: AddInvoiceItemSubs_InvoiceItemAdded_updatedBy | null;
  creatorName: string;
  lastModifierName: string | null;
}

export interface AddInvoiceItemSubs {
  InvoiceItemAdded: AddInvoiceItemSubs_InvoiceItemAdded;
}

export interface AddInvoiceItemSubsVariables {
  hospitalId: string;
  profileId: string;
}
