/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetPatientJourney
// ====================================================

export interface GetPatientJourney_getPatientVisitationHistorySummary_list {
  __typename: "PatientVisitationHistorySummary";
  hospitalName: string | null;
  date: any;
  serviceTypes: string[];
}

export interface GetPatientJourney_getPatientVisitationHistorySummary {
  __typename: "PatientVisitationHistorySummaryResponse";
  registeredDate: any;
  registeredWith: string | null;
  list: GetPatientJourney_getPatientVisitationHistorySummary_list[];
}

export interface GetPatientJourney {
  getPatientVisitationHistorySummary: GetPatientJourney_getPatientVisitationHistorySummary;
}

export interface GetPatientJourneyVariables {
  patientId: string;
}
