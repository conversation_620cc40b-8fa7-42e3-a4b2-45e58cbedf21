/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicationBundleInput, MedicationOptionType } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateMedicationBundle
// ====================================================

export interface UpdateMedicationBundle_updateMedicationBundle_medicationBundleItems_medicationConsumables {
  __typename: "MedicationConsumables";
  drugInventoryId: string | null;
  name: string | null;
  quantity: string | null;
  unitPrice: number | null;
  inventoryClass: string | null;
}

export interface UpdateMedicationBundle_updateMedicationBundle_medicationBundleItems_priceDetails {
  __typename: "MedPriceDetailInputType";
  type: string | null;
  name: string | null;
  pricePerUnit: string | null;
  patientType: string | null;
  paymentType: string | null;
  quantity: string | null;
}

export interface UpdateMedicationBundle_updateMedicationBundle_medicationBundleItems_diagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UpdateMedicationBundle_updateMedicationBundle_medicationBundleItems {
  __typename: "MedicationBundleItemModel";
  id: string;
  administrationMethod: string | null;
  bank: string | null;
  bundleDate: any;
  createdDate: any;
  creatorId: string | null;
  creatorName: string | null;
  dosage: string | null;
  dosageUnit: string | null;
  duration: string | null;
  frequency: string | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  medicationCategory: string | null;
  drugInventoryId: string | null;
  inventoryClass: string | null;
  medicationConsumables: UpdateMedicationBundle_updateMedicationBundle_medicationBundleItems_medicationConsumables[] | null;
  priceDetails: UpdateMedicationBundle_updateMedicationBundle_medicationBundleItems_priceDetails | null;
  provider: string | null;
  medicationName: string | null;
  option: MedicationOptionType | null;
  purpose: string | null;
  quantity: string | null;
  unitPrice: string | null;
  updatedDate: any;
  prescriptionNote: string | null;
  medicationType: string | null;
  diagnosis: UpdateMedicationBundle_updateMedicationBundle_medicationBundleItems_diagnosis[] | null;
}

export interface UpdateMedicationBundle_updateMedicationBundle {
  __typename: "MedicationBundleModel";
  id: string;
  bundleName: string;
  clinifyId: string;
  createdDate: any;
  updatedDate: any;
  creatorId: string | null;
  creatorName: string | null;
  documentUrl: string[] | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  additionalNote: string | null;
  medicationBundleItems: UpdateMedicationBundle_updateMedicationBundle_medicationBundleItems[] | null;
}

export interface UpdateMedicationBundle {
  updateMedicationBundle: UpdateMedicationBundle_updateMedicationBundle;
}

export interface UpdateMedicationBundleVariables {
  id: string;
  input: MedicationBundleInput;
}
