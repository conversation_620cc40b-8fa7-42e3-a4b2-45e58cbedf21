/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ContactFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPatientContacts
// ====================================================

export interface GetPatientContacts_profile_contacts {
  __typename: "ContactsResponse";
  id: string;
  firstName: string;
  middleName: string | null;
  lastName: string;
  clinifyId: string;
}

export interface GetPatientContacts_profile {
  __typename: "ProfileModel";
  id: string;
  contacts: GetPatientContacts_profile_contacts[];
}

export interface GetPatientContacts {
  profile: GetPatientContacts_profile;
}

export interface GetPatientContactsVariables {
  clinifyId: string;
  filterOptions?: ContactFilterInput | null;
}
