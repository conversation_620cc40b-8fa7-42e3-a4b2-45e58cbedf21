/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetCoverageInformation
// ====================================================

export interface GetCoverageInformation_coverageInformation_hmoProfile_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface GetCoverageInformation_coverageInformation_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  employeeType: string | null;
  employeeDivision: string | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  capturedDate: any | null;
  capturedBy: string | null;
  enrolledBy: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  paymentFrequency: string | null;
  memberStartDate: any | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  companyName: string | null;
  companyAddress: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  capitatedMember: string | null;
  retired: boolean | null;
  occupation: string | null;
  capitatedAmount: string | null;
  planEligibility: string | null;
  terminationDate: any | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionRate: string | null;
  commissionPayable: string | null;
  tpaNonTpaCommissionRate: string | null;
  tpaNonTpaCommissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
  activationDatetime: any | null;
  employerCode: string | null;
  totalPremiumAmountPaid: string | null;
  provider: GetCoverageInformation_coverageInformation_hmoProfile_provider;
}

export interface GetCoverageInformation_coverageInformation {
  __typename: "CoverageInformationModel";
  id: string;
  coverageType: string | null;
  companyName: string | null;
  companyAddress: string | null;
  familyName: string | null;
  familyAddress: string | null;
  name: string | null;
  contactAddress: string | null;
  memberNumber: string | null;
  memberPlan: string | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  hmoProfile: GetCoverageInformation_coverageInformation_hmoProfile | null;
  fileNumber: string | null;
  existingFamily: boolean | null;
}

export interface GetCoverageInformation {
  coverageInformation: GetCoverageInformation_coverageInformation;
}

export interface GetCoverageInformationVariables {
  id: string;
}
