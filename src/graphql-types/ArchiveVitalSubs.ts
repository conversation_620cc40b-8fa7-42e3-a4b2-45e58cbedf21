/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: ArchiveVitalSubs
// ====================================================

export interface ArchiveVitalSubs_VitalsArchived_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface ArchiveVitalSubs_VitalsArchived {
  __typename: "VitalModel";
  id: string | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  profileId: string | null;
  profile: ArchiveVitalSubs_VitalsArchived_profile | null;
}

export interface ArchiveVitalSubs {
  VitalsArchived: ArchiveVitalSubs_VitalsArchived[];
}

export interface ArchiveVitalSubsVariables {
  profileId: string;
}
