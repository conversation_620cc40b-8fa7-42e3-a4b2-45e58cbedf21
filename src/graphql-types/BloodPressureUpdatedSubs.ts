/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: BloodPressureUpdatedSubs
// ====================================================

export interface BloodPressureUpdatedSubs_BloodPressureUpdated {
  __typename: "BloodPressureModel";
  id: string;
  readingDateTime: any | null;
  diastolic: string | null;
  systolic: string | null;
  meanArterialPressure: string | null;
  heartRate: string | null;
  fetalHeartRate: string | null;
  location: string | null;
  method: string | null;
  additionalNote: string | null;
  position: string | null;
  rhythm: string | null;
  concealAdditionalNote: boolean | null;
  isDiastolicCritical: boolean | null;
  isSystolicCritical: boolean | null;
  isHeartRateCritical: boolean | null;
  isFetalHeartRateCritical: boolean | null;
}

export interface BloodPressureUpdatedSubs {
  BloodPressureUpdated: BloodPressureUpdatedSubs_BloodPressureUpdated;
}

export interface BloodPressureUpdatedSubsVariables {
  profileId: string;
}
