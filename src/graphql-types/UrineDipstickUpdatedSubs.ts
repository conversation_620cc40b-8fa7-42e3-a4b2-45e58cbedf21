/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: UrineDipstickUpdatedSubs
// ====================================================

export interface UrineDipstickUpdatedSubs_UrineDipstickUpdated {
  __typename: "UrineDipstickModel";
  id: string;
  readingDateTime: any | null;
  blood: string | null;
  glucose: string | null;
  ketones: string | null;
  ph: string | null;
  protein: string | null;
  nitrites: string | null;
  leucocyte: string | null;
  urobilinogen: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isPhCritical: boolean | null;
}

export interface UrineDipstickUpdatedSubs {
  UrineDipstickUpdated: UrineDipstickUpdatedSubs_UrineDipstickUpdated;
}

export interface UrineDipstickUpdatedSubsVariables {
  profileId: string;
}
