/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UserType } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HandoverNoteItemAddedSubs
// ====================================================

export interface HandoverNoteItemAddedSubs_HandoverNoteItemAdded_patientInformation {
  __typename: "PatientInformation";
  fullName: string;
  clinifyId: string | null;
  phone: string | null;
  email: string | null;
}

export interface HandoverNoteItemAddedSubs_HandoverNoteItemAdded {
  __typename: "HandoverNoteItemModel";
  id: string;
  createdDate: any;
  updatedDate: any | null;
  admissionWard: string | null;
  patientProfileId: string;
  patientInformation: HandoverNoteItemAddedSubs_HandoverNoteItemAdded_patientInformation;
  note: string | null;
  priority: string | null;
  handoverNoteId: string;
  creatorId: string;
}

export interface HandoverNoteItemAddedSubs {
  HandoverNoteItemAdded: HandoverNoteItemAddedSubs_HandoverNoteItemAdded;
}

export interface HandoverNoteItemAddedSubsVariables {
  profileId: string;
  hospitalId?: string | null;
  userType: UserType;
}
