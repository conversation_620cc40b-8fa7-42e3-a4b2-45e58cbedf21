/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { NewRequestPackageInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddRequestPackage
// ====================================================

export interface AddRequestPackage_addRequestPackage_serviceDetails {
  __typename: "ServiceDetailsFields";
  serviceType: string | null;
  serviceName: string | null;
}

export interface AddRequestPackage_addRequestPackage_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface AddRequestPackage_addRequestPackage {
  __typename: "RequestPackageModel";
  id: string;
  requestDate: any;
  packageName: string;
  priority: string | null;
  category: string | null;
  orderedBy: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  price: string;
  paymentType: string | null;
  patientType: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  serviceDetails: AddRequestPackage_addRequestPackage_serviceDetails[] | null;
  createdDate: any;
  updatedDate: any;
  profileId: string | null;
  profile: AddRequestPackage_addRequestPackage_profile | null;
}

export interface AddRequestPackage {
  addRequestPackage: AddRequestPackage_addRequestPackage;
}

export interface AddRequestPackageVariables {
  input: NewRequestPackageInput;
  id?: string | null;
}
