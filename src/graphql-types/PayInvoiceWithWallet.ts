/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: PayInvoiceWithWallet
// ====================================================

export interface PayInvoiceWithWallet_payInvoiceWithWallet {
  __typename: "PayInvoiceResponse";
  invoicePayments: string[];
  success: boolean;
}

export interface PayInvoiceWithWallet {
  payInvoiceWithWallet: PayInvoiceWithWallet_payInvoiceWithWallet;
}

export interface PayInvoiceWithWalletVariables {
  invoiceId: string;
  passcode: string;
}
