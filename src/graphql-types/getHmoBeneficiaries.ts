/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: getHmoBeneficiaries
// ====================================================

export interface getHmoBeneficiaries_getProfileBeneficiaries_beneficiaries_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface getHmoBeneficiaries_getProfileBeneficiaries_beneficiaries {
  __typename: "HmoBeneficiary";
  birthDate: string;
  emailAddress: string;
  enrolleeId: string;
  fullName: string;
  provider: getHmoBeneficiaries_getProfileBeneficiaries_beneficiaries_provider;
  phoneNumber: string;
  picUrl: string;
  relationship: string;
  dateEnrolled: string;
}

export interface getHmoBeneficiaries_getProfileBeneficiaries {
  __typename: "HmoBeneficiariesResponse";
  success: boolean;
  errorMsg: string;
  beneficiaries: getHmoBeneficiaries_getProfileBeneficiaries_beneficiaries[];
}

export interface getHmoBeneficiaries {
  getProfileBeneficiaries: getHmoBeneficiaries_getProfileBeneficiaries;
}

export interface getHmoBeneficiariesVariables {
  profileId: string;
}
