/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { PayoutFilterInput } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPayoutSummary
// ====================================================

export interface GetPayoutSummary_getPayoutSummary {
  __typename: "PayoutSummaryResponse";
  totalPayouts: number;
  totalPayoutsAmount: number;
  totalPaidPayouts: number;
  totalPaidPayoutsAmount: number | null;
  totalFailedPayouts: number;
  totalFailedPayoutsAmount: number | null;
  totalPendingPayouts: number;
  totalPendingPayoutsAmount: number | null;
  totalReversedPayouts: number;
  totalReversedPayoutsAmount: number | null;
}

export interface GetPayoutSummary {
  getPayoutSummary: GetPayoutSummary_getPayoutSummary;
}

export interface GetPayoutSummaryVariables {
  options: PayoutFilterInput;
}
