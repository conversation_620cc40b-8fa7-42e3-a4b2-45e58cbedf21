/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ObstetricHistoryInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddObstetricHistory
// ====================================================

export interface AddObstetricHistory_addObstetricInfo_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface AddObstetricHistory_addObstetricInfo {
  __typename: "ObstetricHistoryModel";
  id: string;
  childrenCount: number | null;
  lastBirth: any | null;
  placeOfBirth: string | null;
  modeOfBirth: string | null;
  typeOfClient: string | null;
  decisionSeekingCare: string | null;
  modeOfTransportation: string | null;
  parity: string | null;
  partographUsed: string | null;
  osytocinReceiveed: string | null;
  misoprostolReceived: string | null;
  marternalComplication: string | null;
  receivedMgso4WithEclampsia: string | null;
  counselledOnBreastFeeding: string | null;
  counselledOnFamilyPlanning: string | null;
  admitted: string | null;
  discharged: string | null;
  referredOut: string | null;
  postAbortionCare: string | null;
  motherStatus: string | null;
  mdaConducted: string | null;
  deliveryDateTime: any | null;
  gender: string | null;
  abortion: string | null;
  preTerm: string | null;
  notBreathingOrCrying: string | null;
  babyResuscitated: string | null;
  liveBirthWeight: string | null;
  stillBirth: string | null;
  deadLessThan7days: string | null;
  liveHivBirth: string | null;
  deliveredBy: string | null;
  timeCordClamped: any | null;
  chxGelApplied: string | null;
  babyPutToBreast: string | null;
  temperatureAt1hr: string | null;
  temperatureAt1hrUnit: string | null;
  additionalNote: string | null;
  profile: AddObstetricHistory_addObstetricInfo_profile | null;
  motherCauseOfDeath: string | null;
  admittedForComplicationsOfUnsafeAbortion: string | null;
  admittedOnKMC: string | null;
  dischargedAfterKMC: string | null;
  babyCauseOfDeath: string | null;
  babyStatus: string | null;
}

export interface AddObstetricHistory {
  addObstetricInfo: AddObstetricHistory_addObstetricInfo;
}

export interface AddObstetricHistoryVariables {
  input: ObstetricHistoryInput;
  id?: string | null;
}
