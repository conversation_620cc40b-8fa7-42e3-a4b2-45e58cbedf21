/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { StocksAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetDeviceStatusData
// ====================================================

export interface GetDeviceStatusData_getDeviceStatusData_summary {
  __typename: "StockStatusData";
  totalDeviceNotInUse: number | null;
  totalDeviceInUse: number | null;
  totalDevices: number | null;
  name: number | null;
  totalDeviceInUseCostAmount: number | null;
  totalDeviceNotInUseCostAmount: number | null;
  totalDeviceInUseSalesAmount: number | null;
  totalDeviceNotInUseSalesAmount: number | null;
  totalQuantityAvailable: number | null;
  totalQuantityAvailableCostAmount: number | null;
  totalQuantityAvailableSalesAmount: number | null;
  totalQuantityExpired: number | null;
  totalQuantityExpiredCostAmount: number | null;
  totalQuantityExpiredSalesAmount: number | null;
}

export interface GetDeviceStatusData_getDeviceStatusData_details {
  __typename: "StockStatusDetails";
  dateAdded: string | null;
  itemName: string | null;
  addedBy: string | null;
  totalQuantityPurchased: number | null;
  totalQuantityDispensed: number | null;
  totalQuantityRemaining: number | null;
  totalQuantityExpired: number | null;
  totalQuantityDamaged: number | null;
  totalQuantityAvailable: number | null;
  totalQuantityInUse: number | null;
  totalQuantityNotInUse: number | null;
  supplier: string | null;
  totalCosts: number | null;
  totalSales: number | null;
  totalAverageCost: number | null;
  totalExpiredAmount: number | null;
  totalDamagedAmount: number | null;
  restockLevel: number | null;
}

export interface GetDeviceStatusData_getDeviceStatusData {
  __typename: "StockStatusResponse";
  summary: GetDeviceStatusData_getDeviceStatusData_summary | null;
  details: GetDeviceStatusData_getDeviceStatusData_details[] | null;
}

export interface GetDeviceStatusData {
  getDeviceStatusData: GetDeviceStatusData_getDeviceStatusData;
}

export interface GetDeviceStatusDataVariables {
  filter?: StocksAnalyticsFilter | null;
}
