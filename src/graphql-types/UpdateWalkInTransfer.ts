/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { WalkInTransferInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateWalkInTransfer
// ====================================================

export interface UpdateWalkInTransfer_updateWalkInTransfer_patientInformation {
  __typename: "PatientInformation";
  clinifyId: string | null;
  fullName: string;
  email: string | null;
  phone: string | null;
}

export interface UpdateWalkInTransfer_updateWalkInTransfer {
  __typename: "WalkInTransferModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  transferFacilityName: string | null;
  transferFacilityAddress: string | null;
  documentUrl: string[] | null;
  patientInformation: UpdateWalkInTransfer_updateWalkInTransfer_patientInformation | null;
}

export interface UpdateWalkInTransfer {
  updateWalkInTransfer: UpdateWalkInTransfer_updateWalkInTransfer;
}

export interface UpdateWalkInTransferVariables {
  input: WalkInTransferInput;
  id: string;
}
