/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: VitalUpdatedSubs
// ====================================================

export interface VitalUpdatedSubs_VitalsUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface VitalUpdatedSubs_VitalsUpdated {
  __typename: "VitalModel";
  id: string | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  profileId: string | null;
  profile: VitalUpdatedSubs_VitalsUpdated_profile | null;
}

export interface VitalUpdatedSubs {
  VitalsUpdated: VitalUpdatedSubs_VitalsUpdated;
}

export interface VitalUpdatedSubsVariables {
  profileId: string;
}
