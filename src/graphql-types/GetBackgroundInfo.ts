/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: GetBackgroundInfo
// ====================================================

export interface GetBackgroundInfo_profile_backgroundInformation {
  __typename: "backgroundInformation";
  id: string | null;
  maritalStatus: string | null;
  numberOfChildren: number | null;
  education: string | null;
  state: string | null;
  religion: string | null;
  nationality: string | null;
  organDonor: string | null;
  occupation: string | null;
  salaryRange: string | null;
  bloodDonor: string | null;
  preferredLanguage: string | null;
  modeOfCommunication: string | null;
  tissueDonor: string | null;
  boneMarrowDonor: string | null;
  originLga: string | null;
  ethnicity: string | null;
}

export interface GetBackgroundInfo_profile_user {
  __typename: "UserModel";
  id: string;
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
  country: string | null;
}

export interface GetBackgroundInfo_profile {
  __typename: "ProfileModel";
  id: string;
  creatorName: string | null;
  createdDate: any | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  backgroundInformation: GetBackgroundInfo_profile_backgroundInformation | null;
  user: GetBackgroundInfo_profile_user;
}

export interface GetBackgroundInfo {
  profile: GetBackgroundInfo_profile;
}

export interface GetBackgroundInfoVariables {
  id: string;
}
