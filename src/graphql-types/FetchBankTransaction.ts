/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { VirtualAccountTransactionType, TransactionStatus, BillStatus, InvoiceStatus } from "./globalTypes";

// ====================================================
// GraphQL query operation: FetchBankTransaction
// ====================================================

export interface FetchBankTransaction_fetchBankAccountTransaction_sender {
  __typename: "BankAccountDetails";
  name: string;
  bankName: string | null;
  bankCode: string | null;
  accountNumber: string;
}

export interface FetchBankTransaction_fetchBankAccountTransaction_receiver {
  __typename: "BankAccountDetails";
  name: string;
  accountNumber: string;
  bankName: string | null;
  bankCode: string | null;
}

export interface FetchBankTransaction_fetchBankAccountTransaction_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface FetchBankTransaction_fetchBankAccountTransaction_bill {
  __typename: "BillModel";
  id: string;
  billingDateTime: any | null;
  billStatus: BillStatus;
}

export interface FetchBankTransaction_fetchBankAccountTransaction_invoice_recipient {
  __typename: "InvoiceRecipient";
  name: string;
  clinifyId: string | null;
  email: string | null;
  address: string | null;
  phone: string | null;
}

export interface FetchBankTransaction_fetchBankAccountTransaction_invoice_invoicePayments {
  __typename: "InvoicePaymentModel";
  id: string;
  paymentStatus: string;
  amountPaid: number;
}

export interface FetchBankTransaction_fetchBankAccountTransaction_invoice {
  __typename: "InvoiceModel";
  id: string;
  issueDate: any;
  invoiceStatus: InvoiceStatus;
  invoiceReference: string;
  recipient: FetchBankTransaction_fetchBankAccountTransaction_invoice_recipient;
  invoicePayments: FetchBankTransaction_fetchBankAccountTransaction_invoice_invoicePayments[] | null;
}

export interface FetchBankTransaction_fetchBankAccountTransaction {
  __typename: "BankAccountTransactionModel";
  id: string;
  transactionDateTime: any;
  description: string | null;
  reference: string;
  amount: number;
  transactionType: VirtualAccountTransactionType;
  transactionStatus: TransactionStatus;
  sender: FetchBankTransaction_fetchBankAccountTransaction_sender;
  receiver: FetchBankTransaction_fetchBankAccountTransaction_receiver;
  hospital: FetchBankTransaction_fetchBankAccountTransaction_hospital | null;
  bill: FetchBankTransaction_fetchBankAccountTransaction_bill | null;
  invoice: FetchBankTransaction_fetchBankAccountTransaction_invoice | null;
}

export interface FetchBankTransaction {
  fetchBankAccountTransaction: FetchBankTransaction_fetchBankAccountTransaction;
}

export interface FetchBankTransactionVariables {
  id: string;
}
