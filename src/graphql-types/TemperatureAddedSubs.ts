/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: TemperatureAddedSubs
// ====================================================

export interface TemperatureAddedSubs_TemperatureAdded {
  __typename: "TemperatureModel";
  id: string;
  readingDateTime: any | null;
  checkMethod: string | null;
  reading: string | null;
  readingUnit: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean | null;
  isReadingCritical: boolean | null;
  vitalId: string | null;
}

export interface TemperatureAddedSubs {
  TemperatureAdded: TemperatureAddedSubs_TemperatureAdded;
}

export interface TemperatureAddedSubsVariables {
  profileId: string;
}
