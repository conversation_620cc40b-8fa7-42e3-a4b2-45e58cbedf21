/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { SupplyItemInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateSupplyItem
// ====================================================

export interface UpdateSupplyItem_updateSupplyItem {
  __typename: "SupplyItemModel";
  id: string;
  name: string | null;
  type: string | null;
  addedBy: string | null;
  category: string | null;
  addedDateTime: any | null;
  size: string | null;
  description: string | null;
  code: string | null;
  batchNumber: string | null;
  totalQuantity: string | null;
  quantityRemaining: string | null;
  quantityOutstanding: string | null;
  quantityTransferred: string | null;
  quantityReturned: string | null;
  quantityConsumed: string | null;
  quantityRecalled: string | null;
  quantityDestroyed: string | null;
}

export interface UpdateSupplyItem {
  updateSupplyItem: UpdateSupplyItem_updateSupplyItem;
}

export interface UpdateSupplyItemVariables {
  id: string;
  input: SupplyItemInput;
}
