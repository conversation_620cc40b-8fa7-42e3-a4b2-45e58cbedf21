/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL mutation operation: ConcealOperationNote
// ====================================================

export interface ConcealOperationNote_concealOperationNote {
  __typename: "OperationNoteModel";
  id: string;
  operationNote: string | null;
  postOperationNote: string | null;
  conceal: boolean;
}

export interface ConcealOperationNote {
  concealOperationNote: ConcealOperationNote_concealOperationNote;
}

export interface ConcealOperationNoteVariables {
  id: string;
  concealStatus: boolean;
}
