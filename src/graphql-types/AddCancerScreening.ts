/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CancerScreeningInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: AddCancerScreening
// ====================================================

export interface AddCancerScreening_addCancerScreening_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface AddCancerScreening_addCancerScreening {
  __typename: "CancerScreeningModel";
  id: string;
  screeningToBeDone: string | null;
  screeningType: string | null;
  seenRiskAssessmentForm: string | null;
  mammogramDate: any | null;
  breastUltrasoundDate: any | null;
  cervicalScreeningDate: any | null;
  clinicalBreastExaminationDate: any | null;
  digitalRectalExaminationDate: any | null;
  prostrateSpecificAntigenDate: any | null;
  faecalImmunochemicalOccultDate: any | null;
  colonoscopyDateTime: any | null;
  hpvDnaDate: any | null;
  lastHpvVaccineDate: any | null;
  otherScreeningInformation: string | null;
  lastMenstrualPeriod: any | null;
  existingPastMedicalCondition: string | null;
  currentDrugs: string | null;
  pregnancies: string | null;
  terminationsOrMiscarriages: string | null;
  contraceptiveMethod: string | null;
  tubalLitigation: string | null;
  vasectomy: string | null;
  pastBiopsies: string | null;
  otherMedicalInformation: string | null;
  pastResultsAvailable: string | null;
  specifyPastResult: string | null;
  procedureAndFindingDocumented: string | null;
  laboratoryFormRequested: string | null;
  planOrRecommendations: string | null;
  otherComments: string | null;
  followUpAppointmentWithResult: string | null;
  recallAppointment: string[] | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean;
  documentUrl: string[] | null;
  profileId: string | null;
  profile: AddCancerScreening_addCancerScreening_profile | null;
}

export interface AddCancerScreening {
  addCancerScreening: AddCancerScreening_addCancerScreening;
}

export interface AddCancerScreeningVariables {
  id?: string | null;
  input: CancerScreeningInput;
  pin?: string | null;
}
