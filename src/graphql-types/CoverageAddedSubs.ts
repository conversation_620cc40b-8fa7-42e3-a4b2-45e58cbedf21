/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: CoverageAddedSubs
// ====================================================

export interface CoverageAddedSubs_CoverageInformationAdded_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
}

export interface CoverageAddedSubs_CoverageInformationAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface CoverageAddedSubs_CoverageInformationAdded {
  __typename: "HmoProfileModel";
  id: string;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  memberStartDate: any | null;
  memberStatus: string | null;
  companyName: string | null;
  companyAddress: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  memberPlanId: string | null;
  provider: CoverageAddedSubs_CoverageInformationAdded_provider;
  profile: CoverageAddedSubs_CoverageInformationAdded_profile | null;
}

export interface CoverageAddedSubs {
  CoverageInformationAdded: CoverageAddedSubs_CoverageInformationAdded;
}

export interface CoverageAddedSubsVariables {
  profileId: string;
}
