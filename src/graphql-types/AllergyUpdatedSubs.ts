/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: AllergyUpdatedSubs
// ====================================================

export interface AllergyUpdatedSubs_AllergyUpdated_details_clinicalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface AllergyUpdatedSubs_AllergyUpdated_details {
  __typename: "AllergyDetails";
  type: string;
  trigger: string;
  reactions: string[] | null;
  severeness: string | null;
  clinicalDiagnosis: AllergyUpdatedSubs_AllergyUpdated_details_clinicalDiagnosis[] | null;
}

export interface AllergyUpdatedSubs_AllergyUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface AllergyUpdatedSubs_AllergyUpdated_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface AllergyUpdatedSubs_AllergyUpdated {
  __typename: "AllergyModel";
  id: string;
  occurenceDate: any | null;
  duration: string | null;
  hospitalName: string | null;
  hospitalAddress: string | null;
  doctorName: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  details: AllergyUpdatedSubs_AllergyUpdated_details[] | null;
  profileId: string | null;
  profile: AllergyUpdatedSubs_AllergyUpdated_profile | null;
  documentUrl: string[] | null;
  concealAdditionalNote: boolean | null;
  additionalNote: string | null;
  createdDate: any;
  updatedDate: any;
  medications: AllergyUpdatedSubs_AllergyUpdated_medications[];
}

export interface AllergyUpdatedSubs {
  AllergyUpdated: AllergyUpdatedSubs_AllergyUpdated;
}

export interface AllergyUpdatedSubsVariables {
  profileId: string;
}
