/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { RequestPayoutInput, PayoutStatus, Currency, CommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: EditRequestPayout
// ====================================================

export interface EditRequestPayout_editRequestPayout_receiverAccount {
  __typename: "BankAccountInformation";
  bankName: string;
  accountNumber: string;
  accountName: string | null;
  bankCode: string | null;
  accountType: string | null;
}

export interface EditRequestPayout_editRequestPayout_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface EditRequestPayout_editRequestPayout_virtualServicesPayments_bill_receiverProfile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface EditRequestPayout_editRequestPayout_virtualServicesPayments_bill {
  __typename: "BillModel";
  id: string;
  billingDateTime: any | null;
  raisedBy: string | null;
  receiverProfile: EditRequestPayout_editRequestPayout_virtualServicesPayments_bill_receiverProfile | null;
}

export interface EditRequestPayout_editRequestPayout_virtualServicesPayments {
  __typename: "VirtualServicesPaymentModel";
  id: string;
  billId: string | null;
  bill: EditRequestPayout_editRequestPayout_virtualServicesPayments_bill | null;
  amountDue: number;
  amountPaid: number;
  paymentStatus: string;
  commissionPayer: CommissionPayer | null;
  commissionFeeAmount: number | null;
  payoutId: string | null;
  payoutStatus: PayoutStatus;
  createdDate: any;
  updatedDate: any | null;
}

export interface EditRequestPayout_editRequestPayout_invoicePayments_invoice_recipient {
  __typename: "InvoiceRecipient";
  phone: string | null;
  address: string | null;
  email: string | null;
  name: string;
  clinifyId: string | null;
}

export interface EditRequestPayout_editRequestPayout_invoicePayments_invoice {
  __typename: "InvoiceModel";
  id: string;
  invoiceReference: string;
  issueDate: any;
  dueDate: any;
  recipient: EditRequestPayout_editRequestPayout_invoicePayments_invoice_recipient;
}

export interface EditRequestPayout_editRequestPayout_invoicePayments {
  __typename: "InvoicePaymentModel";
  id: string;
  paymentStatus: string;
  payoutStatus: PayoutStatus;
  amountDue: number;
  amountPaid: number;
  commissionFeeAmount: number | null;
  commissionFeePercentage: number | null;
  paymentMethod: string;
  invoice: EditRequestPayout_editRequestPayout_invoicePayments_invoice;
}

export interface EditRequestPayout_editRequestPayout_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface EditRequestPayout_editRequestPayout_payout {
  __typename: "PayoutModel";
  id: string;
  amountPaid: number;
  payoutStatus: PayoutStatus;
}

export interface EditRequestPayout_editRequestPayout {
  __typename: "RequestPayoutModel";
  id: string;
  requestPayoutDateTime: any;
  initiatedBy: string | null;
  createdDate: any;
  updatedDate: any;
  payoutStatus: PayoutStatus;
  payoutDescription: string | null;
  receiverAccount: EditRequestPayout_editRequestPayout_receiverAccount;
  currency: Currency;
  hospitalId: string;
  receiverInitialWalletBalanceBeforePayout: number;
  receiverInitialWalletBalanceBeforeRequest: number;
  requestAmount: number;
  totalCommissionFeeAmount: number;
  transactionStartDate: any;
  transactionEndDate: any;
  creatorName: string | null;
  lastModifierName: string | null;
  createdBy: EditRequestPayout_editRequestPayout_createdBy;
  virtualServicesPayments: EditRequestPayout_editRequestPayout_virtualServicesPayments[];
  invoicePayments: EditRequestPayout_editRequestPayout_invoicePayments[];
  hospital: EditRequestPayout_editRequestPayout_hospital;
  payout: EditRequestPayout_editRequestPayout_payout | null;
  additionalNote: string | null;
}

export interface EditRequestPayout {
  editRequestPayout: EditRequestPayout_editRequestPayout;
}

export interface EditRequestPayoutVariables {
  id: string;
  input: RequestPayoutInput;
}
