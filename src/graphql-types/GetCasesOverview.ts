/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { CasesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetCasesOverview
// ====================================================

export interface GetCasesOverview_getCasesOverview {
  __typename: "CasesSummary";
  count: number | null;
  category: string | null;
  hospitalName: string | null;
}

export interface GetCasesOverview {
  getCasesOverview: GetCasesOverview_getCasesOverview[];
}

export interface GetCasesOverviewVariables {
  filter?: CasesAnalyticsFilter | null;
}
