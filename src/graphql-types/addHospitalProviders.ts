/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HospitalProviderInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: addHospitalProviders
// ====================================================

export interface addHospitalProviders_addHospitalProviders_providers {
  __typename: "HospitalProviders";
  id: string | null;
  name: string | null;
  code: string | null;
  createdOn: any | null;
  creatorName: string | null;
}

export interface addHospitalProviders_addHospitalProviders {
  __typename: "HospitalModel";
  id: string;
  providers: addHospitalProviders_addHospitalProviders_providers[] | null;
}

export interface addHospitalProviders {
  addHospitalProviders: addHospitalProviders_addHospitalProviders;
}

export interface addHospitalProvidersVariables {
  providers: HospitalProviderInput[];
  csvImport?: boolean | null;
}
