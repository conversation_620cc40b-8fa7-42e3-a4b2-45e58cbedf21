/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL fragment: DispenseRegister
// ====================================================

export interface DispenseRegister_details_periods_audits {
  __typename: "dospenseRegisterAudit";
  fullName: string | null;
  dateTime: any | null;
  desc: string | null;
  checkId: number | null;
  profileId: string | null;
}

export interface DispenseRegister_details_periods {
  __typename: "PeriodsDetails";
  no: number | null;
  values: string | null;
  count: number | null;
  audits: DispenseRegister_details_periods_audits[] | null;
}

export interface DispenseRegister_details {
  __typename: "dispenseRegisterDetails";
  medicationName: string;
  reference: string | null;
  periodName: string;
  periods: DispenseRegister_details_periods[];
}

export interface DispenseRegister {
  __typename: "DispenseRegistersModel";
  id: string;
  details: DispenseRegister_details | null;
  creator: string | null;
  updater: string | null;
  updatedDate: any;
  createdDate: any;
}
