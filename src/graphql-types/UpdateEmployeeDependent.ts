/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdateEmployerDependantInput, Gender } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateEmployeeDependent
// ====================================================

export interface UpdateEmployeeDependent_updateDependants_hmoProfile {
  __typename: "HmoProfileModel";
  id: string;
  memberNumber: string | null;
  primaryProviderId: string | null;
  primaryProviderAddress: string | null;
  primaryProviderName: string | null;
}

export interface UpdateEmployeeDependent_updateDependants_profile_details {
  __typename: "ProfileDetailsModel";
  id: string;
  dateOfBirth: any | null;
}

export interface UpdateEmployeeDependent_updateDependants_profile_user {
  __typename: "UserModel";
  nonCorporateEmail: string | null;
  phoneNumber: string | null;
}

export interface UpdateEmployeeDependent_updateDependants_profile {
  __typename: "ProfileModel";
  id: string;
  gender: Gender | null;
  title: string | null;
  details: UpdateEmployeeDependent_updateDependants_profile_details | null;
  user: UpdateEmployeeDependent_updateDependants_profile_user;
}

export interface UpdateEmployeeDependent_updateDependants {
  __typename: "EmployeeDependantModel";
  id: string;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  relationship: string | null;
  title: string | null;
  employeeId: string;
  lastModifierName: string | null;
  creatorName: string | null;
  createdDate: any;
  updatedDate: any;
  displayPictureUrl: string | null;
  hmoProfile: UpdateEmployeeDependent_updateDependants_hmoProfile | null;
  profile: UpdateEmployeeDependent_updateDependants_profile | null;
}

export interface UpdateEmployeeDependent {
  updateDependants: UpdateEmployeeDependent_updateDependants[];
}

export interface UpdateEmployeeDependentVariables {
  employeeId: string;
  input: UpdateEmployerDependantInput[];
}
