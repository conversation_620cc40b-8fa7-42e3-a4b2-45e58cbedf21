/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: AdmissionAddedSubs
// ====================================================

export interface AdmissionAddedSubs_AdmissionAdded_admissionDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_vteAndBleedingRiskAssessment_furtherAssessment {
  __typename: "Assessment";
  bleedingRiskAssessed: boolean | null;
  vteRiskAssessed: boolean | null;
  bleedingRiskAssessedLastModifiedDate: any | null;
  vteRiskAssessedLastModifiedDate: any | null;
  bleedingRiskAssessedLastModifierName: string | null;
  vteRiskAssessedLastModifierName: string | null;
  ref: string | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_vteAndBleedingRiskAssessment_onAdmission {
  __typename: "Assessment";
  bleedingRiskAssessed: boolean | null;
  vteRiskAssessed: boolean | null;
  bleedingRiskAssessedLastModifiedDate: any | null;
  vteRiskAssessedLastModifiedDate: any | null;
  bleedingRiskAssessedLastModifierName: string | null;
  vteRiskAssessedLastModifierName: string | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_vteAndBleedingRiskAssessment_within24Hours {
  __typename: "Assessment";
  bleedingRiskAssessed: boolean | null;
  vteRiskAssessed: boolean | null;
  bleedingRiskAssessedLastModifiedDate: any | null;
  vteRiskAssessedLastModifiedDate: any | null;
  bleedingRiskAssessedLastModifierName: string | null;
  vteRiskAssessedLastModifierName: string | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_vteAndBleedingRiskAssessment {
  __typename: "VTEAndBleedingRisk";
  furtherAssessment: AdmissionAddedSubs_AdmissionAdded_vteAndBleedingRiskAssessment_furtherAssessment[] | null;
  onAdmission: AdmissionAddedSubs_AdmissionAdded_vteAndBleedingRiskAssessment_onAdmission | null;
  within24Hours: AdmissionAddedSubs_AdmissionAdded_vteAndBleedingRiskAssessment_within24Hours | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_profile_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_profile_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  name: string | null;
  coverageType: string | null;
  companyName: string | null;
  familyName: string | null;
  memberNumber: string | null;
  provider: AdmissionAddedSubs_AdmissionAdded_profile_coverageDetails_provider | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  coverageDetails: AdmissionAddedSubs_AdmissionAdded_profile_coverageDetails[] | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface AdmissionAddedSubs_AdmissionAdded_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: AdmissionAddedSubs_AdmissionAdded_preauthorizationDetails_provider | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_allergies {
  __typename: "AllergyModel";
  id: string;
}

export interface AdmissionAddedSubs_AdmissionAdded_medications {
  __typename: "MedicationModel";
  id: string;
}

export interface AdmissionAddedSubs_AdmissionAdded_surgeries {
  __typename: "SurgeryModel";
  id: string;
}

export interface AdmissionAddedSubs_AdmissionAdded_consultations {
  __typename: "ConsultationModel";
  id: string;
}

export interface AdmissionAddedSubs_AdmissionAdded_vitals {
  __typename: "VitalModel";
  id: string | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_radiology {
  __typename: "InvestigationModel";
  id: string;
}

export interface AdmissionAddedSubs_AdmissionAdded_labTests {
  __typename: "InvestigationModel";
  id: string;
}

export interface AdmissionAddedSubs_AdmissionAdded_investigations {
  __typename: "InvestigationModel";
  id: string;
}

export interface AdmissionAddedSubs_AdmissionAdded_nursingServices {
  __typename: "NursingServiceModel";
  id: string;
}

export interface AdmissionAddedSubs_AdmissionAdded_bloodTransfusions {
  __typename: "BloodTransfusionModel";
  id: string;
  transfusionDateTime: any | null;
  transfusionOrderGiven: string;
  transfusionDoctor: string | null;
  transfusionNurse: string | null;
  patientBloodGroup: string | null;
  patientGenoType: string | null;
  crossMatchingTime: string | null;
  bloodLabel: string | null;
  bloodProduct: string | null;
  expiryDate: string | null;
  donorBloodType: string | null;
  bloodPint: string | null;
  lengthOfTransfusion: string | null;
  transfusionStartDateTime: any | null;
  transfusionEndDateTime: any | null;
  adverseReaction: string | null;
  reaction: string | null;
  transfusionNote: string | null;
  patientConsent: string | null;
  consentReason: string | null;
  bloodSource: string | null;
  bloodDonorStatus: string | null;
  concealTransfusionNote: boolean | null;
  postTransfusionFBC: string | null;
  concealPostTransfusionFBC: boolean | null;
  diuretic: string | null;
  diureticType: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_transferPatients_transferHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  clinifyId: string | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_transferPatients {
  __typename: "TransferPatientModel";
  id: string;
  transferDateTime: any | null;
  transferredBy: string | null;
  transferReason: string | null;
  concealTransferReason: boolean | null;
  roomOption: string | null;
  transferSource: string | null;
  roomInventoryId: string | null;
  transferHospital: AdmissionAddedSubs_AdmissionAdded_transferPatients_transferHospital | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_dischargePatients_dischargeDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_dischargePatients {
  __typename: "DischargePatientModel";
  id: string;
  dischargeDate: any | null;
  dischargedStatus: string | null;
  deathDateTime: any | null;
  deathCause: string | null;
  deathLocation: string | null;
  deathCertificateIssued: string | null;
  dischargeSummary: string | null;
  dischargedBy: string | null;
  dischargedBySignature: string | null;
  dischargedBySignatureType: string | null;
  dischargedBySignatureDateTime: any | null;
  dischargeDiagnosis: AdmissionAddedSubs_AdmissionAdded_dischargePatients_dischargeDiagnosis[] | null;
  causeOfDeath: string | null;
  concealDischargeSummary: boolean | null;
  followupDate: any | null;
  dischargeLocation: string | null;
  dischargeAddress: string | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_admissionNotes {
  __typename: "AdmissionNoteModel";
  id: string;
  creatorProfileType: string;
  note: string | null;
  conceal: boolean | null;
}

export interface AdmissionAddedSubs_AdmissionAdded_bill {
  __typename: "BillModel";
  id: string;
  createdDate: any;
}

export interface AdmissionAddedSubs_AdmissionAdded {
  __typename: "AdmissionModel";
  id: string;
  admissionDate: any | null;
  admissionDiagnosis: AdmissionAddedSubs_AdmissionAdded_admissionDiagnosis[] | null;
  duration: string | null;
  priority: string | null;
  category: string | null;
  severeness: string | null;
  admittedBy: string | null;
  doctorInCharge: string | null;
  ward: string | null;
  provider: string | null;
  providerServiceName: string | null;
  hospitalUnit: string | null;
  roomType: string | null;
  fileNumber: string | null;
  roomNumber: string | null;
  bedNumber: string | null;
  finding: string | null;
  rank: string | null;
  specialty: string | null;
  department: string | null;
  nurseName: string | null;
  hmoProviderId: string | null;
  serviceDetails: AdmissionAddedSubs_AdmissionAdded_serviceDetails[] | null;
  isPackage: boolean;
  bedAvailable: string | null;
  roomOption: string | null;
  patientConsent: string | null;
  presentMedicalHistory: string | null;
  valuablesOrBelongings: string | null;
  patientEnvironmentOrientation: string | null;
  currentMedications: string | null;
  medicinesDeposition: string | null;
  instructedToSendHome: string | null;
  nonBroughtToHospital: string | null;
  otherPlacement: string | null;
  medicationOrDrug: string | null;
  bloodTransfusion: string | null;
  food: string | null;
  latex: string | null;
  adultAge: string | null;
  adultSedativeMedication: string | null;
  adultAmbulatorySupport: string | null;
  mentalStatus: string | null;
  childAge: string | null;
  dehydration: string | null;
  dizziness: string | null;
  respirationDistress: string | null;
  childAmbulatorySupport: string | null;
  childSedativeMedication: string | null;
  painScore: string | null;
  painDescriptors: string[] | null;
  specifyPainDescriptors: string | null;
  painLocation: string[] | null;
  specifyPainLocation: string | null;
  acuity: string | null;
  modifying: string | null;
  tobaccoUse: string | null;
  tobaccoUseDuration: string | null;
  alcoholUse: string | null;
  alcoholUseDuration: string | null;
  psychologicalStatus: string | null;
  specifyPsychologicalStatus: string | null;
  sleep: string | null;
  specifySleepDifficulty: string | null;
  sleepRoutine: string | null;
  specifySleepRoutine: string | null;
  whatMakesYouSleep: string | null;
  bathing: string | null;
  dressing: string | null;
  eating: string | null;
  mobility: string | null;
  stairClimbing: string | null;
  toiletUse: string | null;
  impairedHearing: string | null;
  impairedVision: string | null;
  canPerformAdl: string | null;
  canRead: string | null;
  canWrite: string | null;
  hearingAid: string | null;
  glasses: string | null;
  contacts: string | null;
  dentures: string | null;
  partial: string | null;
  difficultyInChewing: string | null;
  difficultyInSwallowing: string | null;
  specialDiet: string | null;
  specifySpecialDiet: string | null;
  reAdmission: string | null;
  lastAdmissionDateTime: any | null;
  specialCourtesy: string | null;
  specialArrangement: string | null;
  pallorSunkenEyesDehydrationAnorexia: string | null;
  vomittingDiarrheaEdema: string | null;
  newlyDiagnosedDiabeticOrHypertensive: string | null;
  hairOrSkinChange: string | null;
  nursingNeeds: string | null;
  nursingDiagnosis: string[] | null;
  objectives: string | null;
  nursingOrders: string | null;
  evaluation: string | null;
  dischargeDate: any | null;
  transferDate: any | null;
  roomInventoryId: string | null;
  clinicName: string | null;
  clinicAddress: string | null;
  appointmentId: string | null;
  billStatus: string | null;
  documentUrl: string[] | null;
  createdDate: any;
  updatedDate: any;
  hospitalId: string | null;
  patientConsentSignature: string | null;
  patientConsentSignatureType: string | null;
  patientConsentSignatureDateTime: any | null;
  profileId: string | null;
  vteAndBleedingRiskAssessment: AdmissionAddedSubs_AdmissionAdded_vteAndBleedingRiskAssessment | null;
  profile: AdmissionAddedSubs_AdmissionAdded_profile | null;
  preauthorizationDetails: AdmissionAddedSubs_AdmissionAdded_preauthorizationDetails | null;
  allergies: AdmissionAddedSubs_AdmissionAdded_allergies[];
  medications: AdmissionAddedSubs_AdmissionAdded_medications[];
  surgeries: AdmissionAddedSubs_AdmissionAdded_surgeries[];
  consultations: AdmissionAddedSubs_AdmissionAdded_consultations[];
  vitals: AdmissionAddedSubs_AdmissionAdded_vitals[];
  radiology: AdmissionAddedSubs_AdmissionAdded_radiology[];
  labTests: AdmissionAddedSubs_AdmissionAdded_labTests[];
  investigations: AdmissionAddedSubs_AdmissionAdded_investigations[];
  nursingServices: AdmissionAddedSubs_AdmissionAdded_nursingServices[];
  bloodTransfusions: AdmissionAddedSubs_AdmissionAdded_bloodTransfusions[] | null;
  transferPatients: AdmissionAddedSubs_AdmissionAdded_transferPatients[] | null;
  dischargePatients: AdmissionAddedSubs_AdmissionAdded_dischargePatients[] | null;
  admissionNotes: AdmissionAddedSubs_AdmissionAdded_admissionNotes[];
  status: string;
  bill: AdmissionAddedSubs_AdmissionAdded_bill | null;
}

export interface AdmissionAddedSubs {
  AdmissionAdded: AdmissionAddedSubs_AdmissionAdded;
}

export interface AdmissionAddedSubsVariables {
  profileId: string;
  hospitalId: string;
}
