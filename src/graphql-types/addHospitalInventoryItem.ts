/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HospitalInventoryItemInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: addHospitalInventoryItem
// ====================================================

export interface addHospitalInventoryItem_addHospitalInventoryItem_inventoryItems {
  __typename: "HospitalInventoryItem";
  id: string | null;
  name: string | null;
  description: string | null;
  createdOn: any | null;
  creatorName: string | null;
}

export interface addHospitalInventoryItem_addHospitalInventoryItem {
  __typename: "HospitalModel";
  id: string;
  inventoryItems: addHospitalInventoryItem_addHospitalInventoryItem_inventoryItems[] | null;
}

export interface addHospitalInventoryItem {
  addHospitalInventoryItem: addHospitalInventoryItem_addHospitalInventoryItem;
}

export interface addHospitalInventoryItemVariables {
  inventoryItems: HospitalInventoryItemInput[];
}
