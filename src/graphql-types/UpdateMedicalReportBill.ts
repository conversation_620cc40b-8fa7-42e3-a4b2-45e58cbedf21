/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicalReportInput, MedicalReportStatus } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateMedicalReportBill
// ====================================================

export interface UpdateMedicalReportBill_updateMedicalReportBill_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface UpdateMedicalReportBill_updateMedicalReportBill_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface UpdateMedicalReportBill_updateMedicalReportBill_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface UpdateMedicalReportBill_updateMedicalReportBill_reportType {
  __typename: "ReportTypeInputType";
  ref: string | null;
  name: string;
  itemId: string | null;
}

export interface UpdateMedicalReportBill_updateMedicalReportBill_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface UpdateMedicalReportBill_updateMedicalReportBill_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface UpdateMedicalReportBill_updateMedicalReportBill {
  __typename: "MedicalReportModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  updatedBy: UpdateMedicalReportBill_updateMedicalReportBill_updatedBy | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  createdBy: UpdateMedicalReportBill_updateMedicalReportBill_createdBy;
  creatorId: string | null;
  creatorName: string | null;
  profile: UpdateMedicalReportBill_updateMedicalReportBill_profile | null;
  reportDate: any | null;
  reportType: UpdateMedicalReportBill_updateMedicalReportBill_reportType[] | null;
  patientClinifyId: string | null;
  patientFullname: string | null;
  patientPhone: string | null;
  patientEmail: string | null;
  report: string[] | null;
  rank: string | null;
  department: string | null;
  doctorName: string | null;
  specialty: string | null;
  serviceDetails: UpdateMedicalReportBill_updateMedicalReportBill_serviceDetails[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  hospital: UpdateMedicalReportBill_updateMedicalReportBill_hospital | null;
  hospitalId: string | null;
  status: MedicalReportStatus | null;
}

export interface UpdateMedicalReportBill {
  updateMedicalReportBill: UpdateMedicalReportBill_updateMedicalReportBill;
}

export interface UpdateMedicalReportBillVariables {
  input: MedicalReportInput;
}
