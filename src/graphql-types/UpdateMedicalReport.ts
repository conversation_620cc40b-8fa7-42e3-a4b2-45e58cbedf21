/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { MedicalReportInput, MedicalReportStatus } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateMedicalReport
// ====================================================

export interface UpdateMedicalReport_updateMedicalReport_updatedBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface UpdateMedicalReport_updateMedicalReport_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
  type: string;
}

export interface UpdateMedicalReport_updateMedicalReport_profile {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  clinifyId: string;
}

export interface UpdateMedicalReport_updateMedicalReport_reportType {
  __typename: "ReportTypeInputType";
  ref: string | null;
  name: string;
  itemId: string | null;
}

export interface UpdateMedicalReport_updateMedicalReport_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface UpdateMedicalReport_updateMedicalReport_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface UpdateMedicalReport_updateMedicalReport {
  __typename: "MedicalReportModel";
  id: string;
  createdDate: any;
  updatedDate: any;
  updatedBy: UpdateMedicalReport_updateMedicalReport_updatedBy | null;
  lastModifierId: string | null;
  lastModifierName: string | null;
  createdBy: UpdateMedicalReport_updateMedicalReport_createdBy;
  creatorId: string | null;
  creatorName: string | null;
  profile: UpdateMedicalReport_updateMedicalReport_profile | null;
  reportDate: any | null;
  reportType: UpdateMedicalReport_updateMedicalReport_reportType[] | null;
  patientClinifyId: string | null;
  patientFullname: string | null;
  patientPhone: string | null;
  patientEmail: string | null;
  report: string[] | null;
  rank: string | null;
  department: string | null;
  doctorName: string | null;
  specialty: string | null;
  serviceDetails: UpdateMedicalReport_updateMedicalReport_serviceDetails[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
  additionalNote: string | null;
  documentUrl: string[] | null;
  hospital: UpdateMedicalReport_updateMedicalReport_hospital | null;
  hospitalId: string | null;
  status: MedicalReportStatus | null;
}

export interface UpdateMedicalReport {
  updateMedicalReport: UpdateMedicalReport_updateMedicalReport;
}

export interface UpdateMedicalReportVariables {
  input: MedicalReportInput;
  pin?: string | null;
}
