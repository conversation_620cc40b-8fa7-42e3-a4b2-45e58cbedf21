/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HospitalProviderInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: updateHospitalProviders
// ====================================================

export interface updateHospitalProviders_updateHospitalProvider_providers {
  __typename: "HospitalProviders";
  id: string | null;
  name: string | null;
  code: string | null;
  createdOn: any | null;
  creatorName: string | null;
}

export interface updateHospitalProviders_updateHospitalProvider {
  __typename: "HospitalModel";
  id: string;
  providers: updateHospitalProviders_updateHospitalProvider_providers[] | null;
}

export interface updateHospitalProviders {
  updateHospitalProvider: updateHospitalProviders_updateHospitalProvider;
}

export interface updateHospitalProvidersVariables {
  provider: HospitalProviderInput;
  id: string;
}
