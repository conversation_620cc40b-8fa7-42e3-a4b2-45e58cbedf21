/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: InventoryUpdatedSubs
// ====================================================

export interface InventoryUpdatedSubs_InventoryUpdated {
  __typename: "InventoryModel";
  id: string;
  sn: number | null;
  supplier: string | null;
  invoiceNumber: string | null;
  name: string | null;
  type: string | null;
  size: string | null;
  group: string | null;
  flag: string | null;
  description: string | null;
  category: string | null;
  code: string | null;
  strength: string | null;
  ward: string | null;
  addedBy: string | null;
  addedDateTime: string | null;
  batchNumber: string | null;
  barcode: string | null;
  expiryDate: string | null;
  expiryStatus: string | null;
  damagedCount: string | null;
  bedNumber: string | null;
  markup: string | null;
  unitCost: string | null;
  averageCostPrice: string | null;
  colour: string | null;
  model: string | null;
  vin: string | null;
  plateNumber: string | null;
  year: string | null;
  purchasedBy: string | null;
  status: string | null;
  unitSellingPrice: string | null;
  bedAvailable: string | null;
  totalCost: string | null;
  totalSale: string | null;
  quantityRemaining: string | null;
  quantityPurchased: string | null;
  quantityAvailable: string | null;
  quantityDispensed: string | null;
  quantityOrdered: string | null;
  quantitySold: string | null;
  manufacturer: string | null;
  recievedDateTime: string | null;
  reorderLevel: string | null;
  receivedBy: string | null;
  comments: string | null;
  images: string | null;
  section: string | null;
  creatorId: string | null;
  class: string | null;
}

export interface InventoryUpdatedSubs {
  InventoryUpdated: InventoryUpdatedSubs_InventoryUpdated;
}

export interface InventoryUpdatedSubsVariables {
  hospitalId: string;
}
