/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { StocksAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetExpiredConsumables
// ====================================================

export interface GetExpiredConsumables_getExpiredConsumables {
  __typename: "DamagedOrExpiredResponse";
  name: number | null;
  totalRemaining: number | null;
  itemName: string | null;
  date: string | null;
  unitCost: number | null;
  totalCost: number | null;
  totalExpiredAmount: number | null;
}

export interface GetExpiredConsumables {
  getExpiredConsumables: GetExpiredConsumables_getExpiredConsumables[];
}

export interface GetExpiredConsumablesVariables {
  filter: StocksAnalyticsFilter;
}
