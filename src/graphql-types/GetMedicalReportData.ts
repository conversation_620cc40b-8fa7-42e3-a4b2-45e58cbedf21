/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ServicesAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetMedicalReportData
// ====================================================

export interface GetMedicalReportData_getMedicalReportData_byAgeRange {
  __typename: "ListByMedicalReportByAgeRange";
  medicalReportType: string | null;
  ageRange: string | null;
  totalMale: number | null;
  totalFemale: number | null;
  totalOther: number | null;
  totalFemaleAmount: number | null;
  totalMaleAmount: number | null;
  totalOtherAmount: number | null;
}

export interface GetMedicalReportData_getMedicalReportData_byPatientName {
  __typename: "ListByPatientName";
  reportedBy: string | null;
  reportDate: string | null;
  patientName: string | null;
  paymentType: string | null;
  totalAmount: number | null;
  clinifyId: string | null;
  name: string;
}

export interface GetMedicalReportData_getMedicalReportData {
  __typename: "MedicalReportSummeryResponse";
  name: number | null;
  totalMale: number | null;
  totalFemale: number | null;
  totalOther: number | null;
  totalOtherAmount: number | null;
  totalMaleAmount: number | null;
  totalFemaleAmount: number | null;
  byAgeRange: GetMedicalReportData_getMedicalReportData_byAgeRange[] | null;
  byPatientName: GetMedicalReportData_getMedicalReportData_byPatientName[] | null;
}

export interface GetMedicalReportData {
  getMedicalReportData: GetMedicalReportData_getMedicalReportData;
}

export interface GetMedicalReportDataVariables {
  filter?: ServicesAnalyticsFilter | null;
}
