/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmosAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetActuarialData
// ====================================================

export interface GetActuarialData_getActuarialData_membershipData {
  __typename: "ActuarialMembershipData";
  enrolleeId: string | null;
  gender: string | null;
  planCategory: string | null;
  planName: string | null;
  dateOfBirth: string | null;
  paymentDate: string | null;
  planStartDate: string | null;
  planEndDate: string | null;
  terminationDate: string | null;
  memberStatus: string | null;
  enrolleeWard: string | null;
  providerName: string | null;
  providerLga: string | null;
  groupName: string | null;
  subGroupName: string | null;
  companyName: string | null;
  enrolleeLga: string | null;
  planPremiumFlag: string | null;
  planPremiumAmount: string | null;
  paymentFrequency: string | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  beneficiaryDescription: string | null;
}

export interface GetActuarialData_getActuarialData_summaryClaimsData {
  __typename: "ActuarialClaimsData";
  enrolleeId: string | null;
  providerCode: string | null;
  providerName: string | null;
  providerLga: string | null;
  dateOfBirth: string | null;
  groupName: string | null;
  subGroupName: string | null;
  companyName: string | null;
  planName: string | null;
  planCategory: string | null;
  gender: string | null;
  treatmentStartDate: string | null;
  treatmentEndDate: string | null;
  treatmentType: string | null;
  claimId: string | null;
  diagnosisCodes: string | null;
  diagnosisNames: string | null;
  amountSubmitted: string | null;
  providerOwnership: string | null;
  providerLevel: string | null;
  admissionDays: string | null;
  procedureCodes: string | null;
  drugCodes: string | null;
  utilizationCategories: string | null;
  utilizationTypes: string | null;
  paymentModels: string | null;
  tariffCharge: string | null;
  amountCharged: string | null;
  amountPaid: string | null;
  paidDate: string | null;
}

export interface GetActuarialData_getActuarialData_proceduresClaimsData {
  __typename: "ActuarialClaimsData";
  enrolleeId: string | null;
  providerCode: string | null;
  providerName: string | null;
  providerLga: string | null;
  dateOfBirth: string | null;
  groupName: string | null;
  subGroupName: string | null;
  companyName: string | null;
  planName: string | null;
  planCategory: string | null;
  gender: string | null;
  treatmentStartDate: string | null;
  treatmentEndDate: string | null;
  treatmentType: string | null;
  claimId: string | null;
  diagnosisCodes: string | null;
  diagnosisNames: string | null;
  amountSubmitted: string | null;
  providerOwnership: string | null;
  providerLevel: string | null;
  admissionDays: string | null;
  procedureNames: string | null;
  procedureCodes: string | null;
  drugCodes: string | null;
  utilizationCategories: string | null;
  paymentModels: string | null;
  tariffCharge: string | null;
  amountCharged: string | null;
  amountPaid: string | null;
  paidDate: string | null;
}

export interface GetActuarialData_getActuarialData_drugsClaimsData {
  __typename: "ActuarialClaimsData";
  enrolleeId: string | null;
  providerCode: string | null;
  providerName: string | null;
  providerLga: string | null;
  dateOfBirth: string | null;
  groupName: string | null;
  subGroupName: string | null;
  companyName: string | null;
  planName: string | null;
  planCategory: string | null;
  gender: string | null;
  treatmentStartDate: string | null;
  treatmentEndDate: string | null;
  treatmentType: string | null;
  claimId: string | null;
  diagnosisCodes: string | null;
  diagnosisNames: string | null;
  amountSubmitted: string | null;
  providerOwnership: string | null;
  providerLevel: string | null;
  admissionDays: string | null;
  procedureCodes: string | null;
  drugNames: string | null;
  drugCodes: string | null;
  utilizationCategories: string | null;
  paymentModels: string | null;
  tariffCharge: string | null;
  amountCharged: string | null;
  amountPaid: string | null;
  paidDate: string | null;
}

export interface GetActuarialData_getActuarialData_capitationData {
  __typename: "ActuarialCapitationData";
  enrolleeName: string | null;
  enrolleeId: string | null;
  providerName: string | null;
  providerCode: string | null;
  capitationAmount: string | null;
}

export interface GetActuarialData_getActuarialData {
  __typename: "ActuarialReportResponse";
  membershipData: GetActuarialData_getActuarialData_membershipData[] | null;
  summaryClaimsData: GetActuarialData_getActuarialData_summaryClaimsData[] | null;
  proceduresClaimsData: GetActuarialData_getActuarialData_proceduresClaimsData[] | null;
  drugsClaimsData: GetActuarialData_getActuarialData_drugsClaimsData[] | null;
  capitationData: GetActuarialData_getActuarialData_capitationData[] | null;
}

export interface GetActuarialData {
  getActuarialData: GetActuarialData_getActuarialData;
}

export interface GetActuarialDataVariables {
  filter: HmosAnalyticsFilter;
}
