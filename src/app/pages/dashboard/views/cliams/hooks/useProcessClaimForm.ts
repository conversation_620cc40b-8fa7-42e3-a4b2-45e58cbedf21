import useAddForm from 'app/shared/hooks/forms/useAddForm';
import {
  FLAG_CLAIM,
  GET_USER_HMO_CLAIMS,
  PROCESS_CLAIM_STATUS,
  UPDATE_CLAIM_UTILIZATION_COVERED,
  UPDATE_UTILIZATION_QUANTITY,
} from 'apollo-queries/mains/hmoClaim';
import { useToasts } from 'react-toast-notifications';
import { useMutation } from '@apollo/client';
import {
  ProcessPreauthUtilizationStatus,
  ProcessPreauthUtilizationStatusVariables,
} from 'graphql-types/ProcessPreauthUtilizationStatus';
import {
  PROCESS_BULK_PREAUTH_UTILIZATIONS_STATUS,
  PROCESS_PREAUTH_UTILIZATION_STATUS,
} from 'apollo-queries/mains/preauthorizations';
import errorHandler from 'app/shared/utils/errorHandler';
import { useCallback, useEffect } from 'react';
import { ProcessClaimStatus, ProcessClaimStatusVariables } from 'graphql-types/ProcessClaimStatus';
import debounce from 'lodash.debounce';
import {
  UpdateUtilizationQuantity,
  UpdateUtilizationQuantityVariables,
} from 'graphql-types/UpdateUtilizationQuantity';
import { FlagClaim, FlagClaimVariables } from 'graphql-types/FlagClaim';
import { userType } from 'app/shared/utils/authTracker';
import {
  ProcessBulkPreauthUtilizationsStatus,
  ProcessBulkPreauthUtilizationsStatusVariables,
} from 'graphql-types/ProcessBulkPreauthUtilizationsStatus';
import { PaCodeStatus, UserType } from 'graphql-types/globalTypes';
import {
  UpdateClaimUtilizationCovered,
  UpdateClaimUtilizationCoveredVariables,
} from 'graphql-types/UpdateClaimUtilizationCovered';
import { claimsInitialState } from '../../submit-claims/constants';
import { claimsDefaultParams } from '../constants';
import { convertToNumber } from '../../invoice/utils';
import { OGSHIA_AGENCY } from '../../../../../shared/utils/is-clinify-hmo';
import { getUserPayload } from '../../../../../shared/utils/authentication';

type Args = {
  filterOptions: Record<string, unknown>;
  defaultId?: string;
  isOnModal?: boolean;
  clearAction?: () => void;
  refetchList?: () => void;
};
export default function useProcessClaimForm({
  filterOptions,
  defaultId,
  clearAction,
  refetchList,
}: Args) {
  const params = {
    ...claimsDefaultParams,
    cacheUpdateQuery: GET_USER_HMO_CLAIMS,
    initialState: {
      ...claimsInitialState,
    },
    filterOptions,
    defaultId,
    clearAction,
    refetchList,
  };
  const {
    writeAllowed,
    isEdit,
    startEdit,
    setStartEdit,
    handleInputChange,
    handleMultipleFieldsChange,
    handleArrayedStringChange,
    inputs,
    actionText,
    action,
    readOnly,
    id,
    disableActionButton,
    deleteRecordAction,
    fetchingData,
    errorFetching,
    showModalPrompt,
    toggle,
    disableDeleteButton,
    updateRecordAction,
    clinifyId,
    setInputs,
    addRecord,
  } = useAddForm(params);
  const { addToast } = useToasts();
  const loggedInUser = userType();
  const { orgName } = getUserPayload();
  const isOgshiaAgency = orgName === OGSHIA_AGENCY;

  const authDiagnosis =
    inputs?.diagnosis?.length && Array.isArray(inputs.diagnosis)
      ? inputs.diagnosis
      : [{ diagnosisICD10: null, diagnosisICD11: null, diagnosisSNOMED: null }];

  const [_processUtilizationStatus, { loading: processingUtilizationStatus }] = useMutation<
    ProcessPreauthUtilizationStatus,
    ProcessPreauthUtilizationStatusVariables
  >(PROCESS_PREAUTH_UTILIZATION_STATUS, {
    onCompleted: () => {
      addToast('Record Updated Successfully', {
        appearance: 'success',
      });
    },
    onError: (error) => {
      addToast(errorHandler(error), {
        appearance: 'error',
      });
    },
  });

  const [
    _processBulkUtilizationsStatus,
    { loading: processingBulkUtilizationsStatus },
  ] = useMutation<
    ProcessBulkPreauthUtilizationsStatus,
    ProcessBulkPreauthUtilizationsStatusVariables
  >(PROCESS_BULK_PREAUTH_UTILIZATIONS_STATUS, {
    onCompleted: () => {
      addToast('Record Updated Successfully', {
        appearance: 'success',
      });
    },
    onError: (error) => {
      addToast(errorHandler(error), { appearance: 'error' });
    },
  });

  const [_updateUtilizationQuantity] = useMutation<
    UpdateUtilizationQuantity,
    UpdateUtilizationQuantityVariables
  >(UPDATE_UTILIZATION_QUANTITY, {
    onCompleted: () => {
      addToast('Record Updated Successfully', {
        appearance: 'success',
      });
    },
    onError: (error) => {
      addToast(errorHandler(error), {
        appearance: 'error',
      });
    },
  });

  const [updateClaimUtilizationCoveredMutation] = useMutation<
    UpdateClaimUtilizationCovered,
    UpdateClaimUtilizationCoveredVariables
  >(UPDATE_CLAIM_UTILIZATION_COVERED, {
    onCompleted: () => {
      addToast('Record Updated Successfully', { appearance: 'success' });
    },
    onError: (error) => {
      addToast(errorHandler(error), { appearance: 'error' });
    },
  });

  const [_processClaimStatus, { loading: processingClaimStatus }] = useMutation<
    ProcessClaimStatus,
    ProcessClaimStatusVariables
  >(PROCESS_CLAIM_STATUS, {
    onCompleted: () => {
      addToast('Record Updated Successfully', {
        appearance: 'success',
      });
    },
    onError: (error) => {
      addToast(errorHandler(error), {
        appearance: 'error',
      });
    },
  });

  const [flagClaimMutationAction, { loading: updatingClaimFlag }] = useMutation<
    FlagClaim,
    FlagClaimVariables
  >(FLAG_CLAIM, {
    onCompleted: () => {
      addToast('Record Updated Successfully', {
        appearance: 'success',
      });
    },
    onError: (error) => {
      addToast(errorHandler(error), {
        appearance: 'error',
      });
    },
  });

  const processUtilizationStatus = useCallback(
    async (input: ProcessPreauthUtilizationStatusVariables['input'], onDone?: () => unknown) => {
      if (
        [
          UserType.ClaimConfirmation,
          UserType.ClaimAgent,
          UserType.ClaimAgentHOD,
          UserType.ClaimAccount,
        ].includes(loggedInUser)
      )
        return;
      await _processUtilizationStatus({
        variables: {
          input,
        },
      });
      onDone?.();
    },
    [_processUtilizationStatus, loggedInUser],
  );

  const processClaimStatus = useCallback(
    (status: string) => {
      const STATUS_MAP = {
        'Send Back': 'Sent Back',
        Return: 'Returned',
      };

      return _processClaimStatus({
        variables: {
          claimId: inputs.id,
          status: STATUS_MAP[status] || status,
        },
      });
    },
    [_processClaimStatus, inputs.id],
  );

  const processBulkUtilizationsStatus = useCallback(
    (ids: string[], status: PaCodeStatus | null) => {
      if (
        [
          UserType.ClaimConfirmation,
          UserType.ClaimAgent,
          UserType.ClaimAgentHOD,
          UserType.ClaimAccount,
        ].includes(loggedInUser)
      )
        return;
      return _processBulkUtilizationsStatus({
        variables: {
          input: {
            ids,
            status,
          },
        },
      });
    },
    [_processBulkUtilizationsStatus, loggedInUser],
  );

  const flagClaim = useCallback(
    (flag: string, unset: boolean) => {
      return flagClaimMutationAction({
        variables: {
          id: inputs.id,
          flag,
          unset,
          unsetAllExisting: isOgshiaAgency,
        },
      });
    },
    [flagClaimMutationAction, inputs.id, isOgshiaAgency],
  );

  const onQuantityChange = useCallback(
    debounce((quantity: string, id?: string) => {
      if (id) {
        _updateUtilizationQuantity({
          variables: {
            id,
            quantity: convertToNumber(quantity),
          },
        });
      }
    }, 1500),
    [],
  );

  const _onPercentageCoveredChange = useCallback(
    (utilizationId: string, percentageCovered: number | null, onSuccess?: () => void) => {
      if (id) {
        const ut = inputs.utilizations.find((utilization) => utilization.id === utilizationId);
        const serviceAmount = convertToNumber(ut.quantity) * convertToNumber(ut.price);
        const amountCovered =
          percentageCovered === null
            ? null
            : Number((serviceAmount * (percentageCovered / 100))?.toFixed(2));
        updateClaimUtilizationCoveredMutation({
          variables: {
            claimId: id,
            utilizationId,
            percentageCovered,
            amountCovered,
          },
        }).then(() => {
          onSuccess?.();
        });
      }
    },
    [id, inputs.id],
  );

  const _onAmountCoveredChange = useCallback(
    (utilizationId: string, amountCovered: number | null, onSuccess?: () => void) => {
      if (id) {
        const ut = inputs.utilizations.find((utilization) => utilization.id === utilizationId);
        const serviceAmount = convertToNumber(ut.quantity) * convertToNumber(ut.price);
        const percentageCovered =
          amountCovered === null
            ? null
            : Number(((amountCovered / serviceAmount) * 100)?.toFixed(2));
        updateClaimUtilizationCoveredMutation({
          variables: {
            claimId: id,
            utilizationId,
            amountCovered,
            percentageCovered,
          },
        }).then(() => {
          onSuccess?.();
        });
      }
    },
    [id, inputs.id],
  );

  const debouncedPercentageCoveredUpdate = debounce(_onPercentageCoveredChange, 1000);

  const debouncedAmountCoveredUpdate = debounce(_onAmountCoveredChange, 1000);

  const onPercentageCoveredChange = (
    utilizationId: string,
    percentageCovered: number | null,
    onSuccess?: () => void,
  ) => {
    debouncedPercentageCoveredUpdate(utilizationId, percentageCovered, onSuccess);
  };

  const onAmountCoveredChange = (
    utilizationId: string,
    amountCovered: number | null,
    onSuccess?: () => void,
  ) => {
    debouncedAmountCoveredUpdate(utilizationId, amountCovered, onSuccess);
  };

  useEffect(() => {
    if (inputs.status === 'submitted' && inputs.id) {
      processClaimStatus('Awaiting Adjudication');
    }
  }, []);

  return {
    writeAllowed,
    isEdit,
    startEdit,
    setStartEdit,
    handleInputChange,
    handleMultipleFieldsChange,
    inputs,
    actionText,
    action,
    readOnly,
    id,
    disableActionButton,
    deleteRecordAction,
    fetchingData,
    errorFetching,
    showModalPrompt,
    toggle,
    disableDeleteButton,
    updateRecordAction,
    clinifyId,
    handleArrayedStringChange,
    setInputs,
    addRecord,
    processUtilizationStatus,
    authDiagnosis,
    processClaimStatus,
    processingClaimStatus,
    processingUtilizationStatus,
    onQuantityChange,
    flagClaim,
    updatingClaimFlag,
    processBulkUtilizationsStatus,
    processingBulkUtilizationsStatus,
    onPercentageCoveredChange,
    onAmountCoveredChange,
  };
}
