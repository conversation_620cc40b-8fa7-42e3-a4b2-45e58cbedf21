import './styles.css';

import React, { useCallback, useMemo, useState, memo } from 'react';
import PlainTable, { PlainTableColumns } from 'app/shared/components/table/PlainTable';
import { HmoClaim_utilizations } from 'graphql-types/HmoClaim';
import { Preauthorization_preauthorization_diagnosis } from 'graphql-types/Preauthorization';
import { getUserPayload } from 'app/shared/utils/authentication';
import classnames from 'classnames';
import { PaCodeStatus, UserType } from 'graphql-types/globalTypes';
import { ProcessPreauthUtilizationStatusVariables } from 'graphql-types/ProcessPreauthUtilizationStatus';
import { formatTableDateTime } from 'app/shared/utils/formatTable';
import SectionReveal from 'app/shared/components/section-reveal/SectionReveal';
import { Popover } from '@mui/material';
import Button from 'app/shared/components/buttons/Button';
import TextArea from 'app/shared/components/inputs/TextArea';
import { useMutation, useQuery } from '@apollo/client';
import { useToasts } from 'react-toast-notifications';
import { UPDATE_CLAIM_UTILIZATION_CONFIRMATION } from 'apollo-queries/mains/hmoClaim';
import {
  UpdateClaimUtilizationConfirmation,
  UpdateClaimUtilizationConfirmationVariables,
} from 'graphql-types/UpdateClaimUtilizationConfirmation';
import { ReactComponent as AiIcon } from 'assets/icons/ai.svg';
import errorHandler from 'app/shared/utils/errorHandler';
import { userType } from 'app/shared/utils/authTracker';
import { FLAG_UTILIZATIONS } from 'apollo-queries/mains/preauthorizations';
import { FlagUtilizations, FlagUtilizationsVariables } from 'graphql-types/FlagUtilizations';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import useHospitalData from 'app/shared/hooks/client/useHospitalData';
import InfoIcon from '@material-ui/icons/Info';
import moment from 'moment';
import calculateTotalUtilization from 'app/shared/calculate-total-utilization';
import { GET_PATIENT_MEDICAL_SUMMARY } from 'apollo-queries/mains/user';
import {
  GetPatientMedicalSummary,
  GetPatientMedicalSummaryVariables,
} from 'graphql-types/GetPatientMedicalSummary';
import { LASHMA_AGENCY, LASHMA_AGENCY_2 } from 'app/shared/utils/is-clinify-hmo';
import Dropdown from 'app/shared/components/dropdown/Dropdown';
import { convertToCurrencyString, convertToNumber } from '../../invoice/utils';
import { ApproveIcon, RejectIcon } from '../../../../../../assets';
import { listAllDiagnosis } from '../../pre-authorisations/helpers/list-all-diagnosis';
import UtilizationApprovalGroupTable, {
  UtilizationOriginType,
} from './UtilizationApprovalGroupTable';
import HmoFlagSection, {
  FlagVariant,
} from '../../../components/forms/HmoFlagSection/HmoFlagSection';
import colors from '../../../../../../@constant/colors';
import useClaimsAiSuggestions from '../hooks/useClaimsAiSuggestions';
import { LASHMA_REASON_FOR_REJECTIONS } from '../../../../../../@constant/options';

// Extract inline styles to prevent recreation on each render
const OVERFLOW_AUTO_STYLE = { overflowX: 'auto' as const };
const MIN_WIDTH_STYLE = { minWidth: 1100 };
const REJECT_ICON_STYLE = {
  position: 'relative' as const,
  display: 'inline-block',
  padding: '4px',
};
const POPOVER_CONTAINER_STYLE = { paddingRight: 15, paddingBottom: 12 };

// Extract constant arrays
const BASIC_TABLE_COLUMNS = [
  { key: '_', className: 'min-width-200', title: '' },
  { key: '__', className: 'full-width min-width-200', title: '' },
];

const POPOVER_ANCHOR_ORIGIN = {
  vertical: 'bottom' as const,
  horizontal: 'center' as const,
};

const POPOVER_TRANSFORM_ORIGIN = {
  vertical: 'top' as const,
  horizontal: 'center' as const,
};

interface RejectIconWithPopoverProps {
  row: HmoClaim_utilizations;
  onProcessUtilization: (input: ProcessPreauthUtilizationStatusVariables['input']) => void;
  processingUtilizationStatus?: boolean;
}

interface BulkRejectIconWithPopoverProps {
  utilizations: HmoClaim_utilizations[];
  onProcessBulkUtilizations: (
    ids: string[],
    status: PaCodeStatus | null,
    rejectionReason?: string[] | null,
    specifyReasonForRejection?: string | null,
    statusDescription?: string | null,
  ) => void;
  processingBulkUtilizationsStatus?: boolean;
  isAllRejected: boolean;
}

type UtilizationCoveredAmountWithPopoverProps = {
  onValueChange:
    | ((utilId: string, value: number | null, onSuccess?: () => unknown) => void)
    | null
    | undefined;
  onCommentChange: (utilId: string, c: string | null | undefined) => void;
  row: HmoClaim_utilizations;
  type: 'AmountCovered' | 'PercentageCovered';
  disablePriceAdjustment?: boolean;
};

const UtilizationCoveredAmountWithPopover = memo(function UtilizationCoveredAmountWithPopover({
  onValueChange,
  row,
  onCommentChange,
  type,
  disablePriceAdjustment,
}: UtilizationCoveredAmountWithPopoverProps) {
  const { profileId } = getUserPayload();
  const [amountFieldPopoverAnchor, setAmountFieldPopoverAnchor] = useState<HTMLElement | null>(
    null,
  );
  const [isPopoverOpen, setIsPopoverOpen] = useState<boolean>(false);
  const [comment, setComment] = useState<string | null | undefined>('');
  const inputRef = React.useRef<HTMLInputElement>(null);

  const ownComment = row.utilisationStatus?.find(({ creatorId }) => creatorId === profileId)
    ?.comment;

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const anchorElement = event.currentTarget;
    setAmountFieldPopoverAnchor(anchorElement);
    setIsPopoverOpen(true);
  };

  const openPopoverAtInput = () => {
    if (inputRef.current) {
      setAmountFieldPopoverAnchor(inputRef.current);
      setIsPopoverOpen(true);
    }
  };

  const handleClosePopover = () => {
    setIsPopoverOpen(false);
  };

  const handleCommentConfirmation = () => {
    onCommentChange(row.id, comment);
    handleClosePopover();
  };

  const defaultValue = type === 'AmountCovered' ? row.amountCovered : row.percentageCovered;

  React.useEffect(() => {
    setComment(ownComment);
  }, [ownComment]);

  return (
    <>
      <div>
        <div className="flex flex-row gap-4 align-items-center">
          <input
            ref={inputRef}
            style={{ width: 70 }}
            defaultValue={defaultValue as any}
            onChange={(event) => {
              const {
                target: { value },
              } = event;
              if (type === 'PercentageCovered') {
                onValueChange?.(row.id, value ? convertToNumber(value) : null, openPopoverAtInput);
              } else if (type === 'AmountCovered') {
                onValueChange?.(row.id, value ? convertToNumber(value) : null, openPopoverAtInput);
              }
            }}
            disabled={userType() === UserType.ClaimConfirmation || disablePriceAdjustment}
          />
          {(row.amountCovered || row.percentageCovered) && (
            <div onClick={handleClick}>
              <InfoIcon fontSize="small" className="cursor-pointer" />
            </div>
          )}
        </div>
      </div>

      <Popover
        open={isPopoverOpen}
        anchorEl={amountFieldPopoverAnchor}
        onClose={handleClosePopover}
        anchorOrigin={POPOVER_ANCHOR_ORIGIN}
        transformOrigin={POPOVER_TRANSFORM_ORIGIN}
        container={amountFieldPopoverAnchor?.ownerDocument?.body}
        disablePortal={false}
      >
        <div className="flex flex-column" style={{ width: '450px' }}>
          <TextArea
            name="comment"
            label="Comment"
            fullWidth
            onChange={({ target: { value } }) => {
              setComment(value);
            }}
            value={comment}
          />

          <div className="flex flex-row gap-8 justify-content-end" style={POPOVER_CONTAINER_STYLE}>
            <Button text="Update" onClick={handleCommentConfirmation} />
          </div>
        </div>
      </Popover>
    </>
  );
});

const RejectIconWithPopover = memo(function RejectIconWithPopover({
  row,
  onProcessUtilization,
  processingUtilizationStatus,
}: RejectIconWithPopoverProps) {
  const { profileId, orgName } = getUserPayload();

  // Internal state for popover
  const [rejectPopoverAnchor, setRejectPopoverAnchor] = useState<HTMLElement | null>(null);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [comment, setComment] = useState('');
  const [localRejectionReasons, setLocalRejectionReasons] = useState<string[]>(['']);
  const [specifyReasonForRejection, setSpecifyReasonForRejection] = useState('');
  const [statusDescription, setStatusDescription] = useState<string | null>(null);

  const selUtilizationStatus = row.utilisationStatus?.find(
    ({ creatorId }) => creatorId === profileId,
  );
  const rejectionReasons =
    row.rejectionReason?.length && Array.isArray(row.rejectionReason) ? row.rejectionReason : [''];
  const selectedRejectionReasons = localRejectionReasons?.filter(Boolean);

  // Initialize state with existing values
  React.useEffect(() => {
    setLocalRejectionReasons(rejectionReasons);
    setSpecifyReasonForRejection(row.specifyReasonForRejection || '');
    setStatusDescription(row.statusDescription);
  }, [row.rejectionReason, row.specifyReasonForRejection, row.statusDescription]);

  // Helper function to generate rejection reason description
  const getRejectionReasonDescription = (reasons: string[], _specifyReason: string) => {
    return (
      statusDescription ||
      reasons
        ?.map((_reason) => (_reason === 'Others' ? _specifyReason : _reason))
        ?.filter(Boolean)
        ?.join(', ')
    );
  };

  const RejectionReasonOptions = [
    ...LASHMA_REASON_FOR_REJECTIONS,
    { value: 'Others', label: 'Others' },
  ]?.filter(({ value: _value }) => !selectedRejectionReasons.includes(_value));
  const isRejected = selUtilizationStatus?.status === PaCodeStatus.Rejected;

  // Handle opening popover
  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (processingUtilizationStatus) return;
    const anchorElement = event.currentTarget;
    setRejectPopoverAnchor(anchorElement);
    setIsPopoverOpen(true);
  };

  // Handle closing popover
  const handleClosePopover = () => {
    setIsPopoverOpen(false);
    setComment('');
  };

  // Handle rejection confirmation
  const handleRejectConfirmation = () => {
    const statusDescription = getRejectionReasonDescription(
      localRejectionReasons,
      specifyReasonForRejection,
    );
    onProcessUtilization({
      id: row.id,
      status: PaCodeStatus.Rejected,
      statusDescription,
      comment,
      rejectionReason: localRejectionReasons,
      specifyReasonForRejection,
    });
    handleClosePopover();
  };

  return (
    <>
      <div
        onClick={isRejected || processingUtilizationStatus ? undefined : handleClick}
        className={classnames('pointer', {
          'not-allowed': isRejected || processingUtilizationStatus,
          'opacity-30': !isRejected,
        })}
        style={REJECT_ICON_STYLE}
      >
        <div className="flex flex-row gap-4 align-items-center">
          <RejectIcon style={{ display: 'block', margin: '0 auto' }} />
          {isRejected && (
            <div onClick={handleClick}>
              <InfoIcon fontSize="small" className="cursor-pointer" color="error" />
            </div>
          )}
        </div>
      </div>

      <Popover
        open={isPopoverOpen}
        anchorEl={rejectPopoverAnchor}
        onClose={handleClosePopover}
        anchorOrigin={POPOVER_ANCHOR_ORIGIN}
        transformOrigin={POPOVER_TRANSFORM_ORIGIN}
        container={rejectPopoverAnchor?.ownerDocument?.body}
        disablePortal={false}
      >
        <div className="flex flex-column" style={{ width: '450px' }}>
          {[LASHMA_AGENCY, LASHMA_AGENCY_2].includes(orgName) ? (
            <>
              {localRejectionReasons.map((_rejectionReason, _idx) => (
                <React.Fragment key={_idx}>
                  <Dropdown
                    title="Rejection Reason"
                    options={[...RejectionReasonOptions]}
                    placeholder="Select One"
                    value={_rejectionReason || null}
                    onChange={({ value }) => {
                      const newReasons = [...localRejectionReasons];
                      newReasons[_idx] = value;
                      let newSpecifyReason = specifyReasonForRejection;
                      if (value === 'Others' && newReasons[_idx] !== 'Others') {
                        newSpecifyReason = '';
                      } else if (newReasons[_idx] === 'Others' && value !== 'Others') {
                        newSpecifyReason = '';
                      }
                      setLocalRejectionReasons(newReasons);
                      setSpecifyReasonForRejection(newSpecifyReason);
                    }}
                  />
                  {_rejectionReason === 'Others' && (
                    <TextArea
                      name="reasonForRejection"
                      label="Specify Rejection Reason"
                      fullWidth
                      value={specifyReasonForRejection}
                      onChange={({ target: { value } }) => {
                        setSpecifyReasonForRejection(value);
                      }}
                    />
                  )}
                </React.Fragment>
              ))}
            </>
          ) : (
            <TextArea
              name="reasonForRejection"
              label="Reason for Rejection"
              fullWidth
              onChange={({ target: { value } }) => {
                setStatusDescription(value);
              }}
              value={statusDescription}
            />
          )}

          <div className="flex flex-row gap-8 justify-content-end" style={POPOVER_CONTAINER_STYLE}>
            <Button text="Reject" onClick={handleRejectConfirmation} />
          </div>
        </div>
      </Popover>
    </>
  );
});

const BulkRejectIconWithPopover = memo(function BulkRejectIconWithPopover({
  utilizations,
  onProcessBulkUtilizations,
  processingBulkUtilizationsStatus,
  isAllRejected,
}: BulkRejectIconWithPopoverProps) {
  const { orgName } = getUserPayload();

  // Internal state for popover
  const [rejectPopoverAnchor, setRejectPopoverAnchor] = useState<HTMLElement | null>(null);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [localRejectionReasons, setLocalRejectionReasons] = useState<string[]>(['']);
  const [specifyReasonForRejection, setSpecifyReasonForRejection] = useState('');
  const [statusDescription, setStatusDescription] = useState<string | null>(null);

  const selectedRejectionReasons = localRejectionReasons?.filter(Boolean);

  const RejectionReasonOptions = [
    ...LASHMA_REASON_FOR_REJECTIONS,
    { value: 'Others', label: 'Others' },
  ]?.filter(({ value: _value }) => !selectedRejectionReasons.includes(_value));

  // Helper function to generate rejection reason description
  const getRejectionReasonDescription = (reasons: string[], _specifyReason: string) => {
    return (
      statusDescription ||
      reasons
        ?.map((_reason) => (_reason === 'Others' ? _specifyReason : _reason))
        ?.filter(Boolean)
        ?.join(', ')
    );
  };

  // Handle opening popover
  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (processingBulkUtilizationsStatus || isAllRejected) return;
    const anchorElement = event.currentTarget;
    setRejectPopoverAnchor(anchorElement);
    setIsPopoverOpen(true);
  };

  // Handle closing popover
  const handleClosePopover = () => {
    setIsPopoverOpen(false);
    setLocalRejectionReasons(['']);
    setSpecifyReasonForRejection('');
    setStatusDescription(null);
  };

  // Handle bulk rejection confirmation
  const handleBulkRejectConfirmation = () => {
    const statusDescriptionValue = getRejectionReasonDescription(
      localRejectionReasons,
      specifyReasonForRejection,
    );
    onProcessBulkUtilizations(
      utilizations.map(({ id }) => id),
      PaCodeStatus.Rejected,
      localRejectionReasons,
      specifyReasonForRejection,
      statusDescriptionValue,
    );
    handleClosePopover();
  };

  return (
    <>
      <div
        onClick={handleClick}
        className={classnames('pointer', {
          'not-allowed': isAllRejected || processingBulkUtilizationsStatus,
          'opacity-30': !isAllRejected,
        })}
        style={{
          position: 'relative',
          display: 'inline-block',
          padding: '4px',
        }}
      >
        <RejectIcon style={{ display: 'block', margin: '0 auto' }} />
      </div>

      <Popover
        open={isPopoverOpen}
        anchorEl={rejectPopoverAnchor}
        onClose={handleClosePopover}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        container={rejectPopoverAnchor?.ownerDocument?.body}
        disablePortal={false}
      >
        <div className="flex flex-column" style={{ width: '450px' }}>
          {[LASHMA_AGENCY, LASHMA_AGENCY_2].includes(orgName) ? (
            <>
              {localRejectionReasons.map((_rejectionReason, _idx) => (
                <React.Fragment key={_idx}>
                  <Dropdown
                    title="Rejection Reason"
                    options={[...RejectionReasonOptions]}
                    placeholder="Select One"
                    value={_rejectionReason || null}
                    onChange={({ value }) => {
                      const newReasons = [...localRejectionReasons];
                      newReasons[_idx] = value;
                      let newSpecifyReason = specifyReasonForRejection;
                      if (value === 'Others' && newReasons[_idx] !== 'Others') {
                        newSpecifyReason = '';
                      } else if (newReasons[_idx] === 'Others' && value !== 'Others') {
                        newSpecifyReason = '';
                      }
                      setLocalRejectionReasons(newReasons);
                      setSpecifyReasonForRejection(newSpecifyReason);
                    }}
                  />
                  {_rejectionReason === 'Others' && (
                    <TextArea
                      name="reasonForRejection"
                      label="Specify Rejection Reason"
                      fullWidth
                      value={specifyReasonForRejection}
                      onChange={({ target: { value } }) => {
                        setSpecifyReasonForRejection(value);
                      }}
                    />
                  )}
                </React.Fragment>
              ))}
            </>
          ) : (
            <TextArea
              name="reasonForRejection"
              label="Reason for Rejection"
              fullWidth
              onChange={({ target: { value } }) => {
                setStatusDescription(value);
              }}
              value={statusDescription}
            />
          )}

          <div
            className="flex flex-row gap-8 justify-content-end"
            style={{ paddingRight: 15, paddingBottom: 12 }}
          >
            <Button text="Reject All" onClick={handleBulkRejectConfirmation} />
          </div>
        </div>
      </Popover>
    </>
  );
});

function columns(
  onProcessUtilization: (input: ProcessPreauthUtilizationStatusVariables['input']) => void,
  onProcessBulkUtilizations: (
    ids: string[],
    status: PaCodeStatus | null,
    rejectionReason?: string[] | null,
    specifyReasonForRejection?: string | null,
    statusDescription?: string | null,
  ) => void,
  utilizations: HmoClaim_utilizations[],
  onConfirmation: (ids: string[], val: boolean) => unknown,
  showConfirmationControl?: boolean,
  showPercentageConvered?: boolean,
  showComments?: boolean,
  onPercentageCoveredChange?: (utilId: string, percentage: number | null) => void,
  onQuantityChange?: (quantity: string, id?: string) => void,
  onAmountCoveredChange?: (utilId: string, amount: number | null) => void,
  showFlagControl?: boolean,
  onUtilizationFlagged?: (utilizationIds: string[], flag: string, unset: boolean) => void,
  flagVariant?: FlagVariant,
  flagTooltipDescription?: Record<string, string>,
  processingUtilizationStatus?: boolean,
  processingBulkUtilizationsStatus?: boolean,
  hasAiSuggestions?: boolean,
  acceptedAiSuggestions?: boolean,
  acceptAiSuggestions?: () => unknown,
  revertAiSuggestions?: () => unknown,
  applyingAiSuggestions?: boolean,
  disablePriceAdjustment = false,
): PlainTableColumns {
  const { profileId } = getUserPayload();

  const isAllApproved = utilizations.every(({ utilisationStatus }) =>
    utilisationStatus?.some(
      ({ creatorId, status }) => creatorId === profileId && status === PaCodeStatus.Approved,
    ),
  );
  const isAllRejected = utilizations.every(({ utilisationStatus }) =>
    utilisationStatus?.some(
      ({ creatorId, status }) => creatorId === profileId && status === PaCodeStatus.Rejected,
    ),
  );
  const isAllConfirmed = utilizations.every(({ confirmation }) => confirmation);
  const getAllFlagSelection = () => {
    let firstValue: string[] | undefined | null = utilizations[0]?.flags
      ?.map(({ flag }) => flag as string)
      ?.filter((f) => !!f);
    const allSame = utilizations.every(({ flags }) =>
      flags?.some(({ flag }) => flag && firstValue?.includes(flag)),
    );

    // eslint-disable-next-line no-restricted-syntax
    for (const flag of firstValue || []) {
      if (
        !utilizations.every(({ flags }) =>
          flags?.some(({ flag: currentFlag }) => currentFlag === flag),
        )
      ) {
        firstValue = firstValue?.filter((f) => f !== flag);
      }
    }

    return allSame ? firstValue : null;
  };
  return [
    {
      key: 'sn',
      title: (
        <div className="flex flex-row justify-content-end">
          <span>S/N</span>
          {showConfirmationControl ? (
            <span>
              <input
                type="checkbox"
                checked={isAllConfirmed}
                className="m-1 cursor-pointer"
                onChange={({ target: { checked } }) => {
                  onConfirmation(
                    utilizations.map(({ id }) => id),
                    checked,
                  );
                }}
                disabled={
                  isAllConfirmed && utilizations.length
                    ? ![UserType.ClaimConfirmation, UserType.ClaimOfficer].includes(userType())
                    : userType() !== UserType.ClaimConfirmation
                }
              />
            </span>
          ) : null}
        </div>
      ),
      CustomElement({ row, idx }) {
        return (
          <div className="flex flex-row justify-content-end">
            <span>{idx + 1}</span>
            {showConfirmationControl ? (
              <input
                type="checkbox"
                checked={row.confirmation}
                className="m-1 cursor-pointer"
                onChange={({ target: { checked } }) => {
                  onConfirmation([row.id], checked);
                }}
                disabled={
                  row.confirmation
                    ? ![UserType.ClaimConfirmation, UserType.ClaimOfficer].includes(userType())
                    : userType() !== UserType.ClaimConfirmation
                }
              />
            ) : null}
          </div>
        );
      },
    },
    {
      key: 'claimNumber',
      title: (
        <div className="flex flex-row justify-content-between">
          <span>Description of Services Provided</span>
          {!!hasAiSuggestions && (
            <AiIcon
              className={classnames('ai-icon-svg cursor-pointer', {
                glow: applyingAiSuggestions,
                'not-allowed': applyingAiSuggestions,
              })}
              style={{
                fill: acceptedAiSuggestions ? colors.ash : colors.primaryColorHex,
              }}
              // onClick={acceptedAiSuggestions ? revertAiSuggestions : acceptAiSuggestions}
            />
          )}
        </div>
      ),
      CustomElement({ row }) {
        return (
          <div className="flex flex-row gap-16 align-items-center">
            <span>
              {row.category} - {row.type} {row.paCode ? `(${row.paCode})` : ''}
            </span>
            {row.aiReason?.reason && row?.aiReason?.status ? (
              <>
                <div
                  data-tooltip-content={row?.aiReason?.reason}
                  data-color={row?.aiReason?.status?.match(/rejected/i) ? 'red' : colors.lightGreen}
                  data-status={row?.aiReason?.status}
                  data-tooltip-id="aiReason"
                  data-html
                >
                  <AiIcon
                    className="ai-icon-svg cursor-pointer"
                    style={{
                      fill: row?.aiReason?.status?.match(/rejected/i) ? 'red' : colors.lightGreen,
                    }}
                  />
                </div>
              </>
            ) : null}
          </div>
        );
      },
    },
    {
      key: 'quantity',
      title: 'Quantity',
      showSum: true,
      CustomElement: onQuantityChange
        ? ({ row }) => {
            return (
              <input
                style={{ width: 70 }}
                defaultValue={row.quantity}
                onChange={({ target: { value } }) => onQuantityChange(value, row.id)}
                disabled={userType() === UserType.ClaimConfirmation}
              />
            );
          }
        : undefined,
      customSumValue(data) {
        return calculateTotalUtilization(data).totalQuantity;
      },
    },
    {
      key: 'price',
      title: 'Unit Price',
      CustomElement({ data }) {
        return <span>{`₦${convertToCurrencyString(data)}`}</span>;
      },
      showSum: true,
      sumFormatter: (data) => `₦${convertToCurrencyString(data as string)}`,
      customSumValue(data) {
        return `₦${convertToCurrencyString(
          calculateTotalUtilization(data, undefined).totalUnitPrice,
        )}`;
      },
    },
    {
      key: 'totalPrice',
      title: 'Total Price',
      CustomElement({ row }) {
        return (
          <span>
            {`₦${
              row.percentageCovered || row.amountCovered
                ? row.amountCovered
                  ? convertToCurrencyString(row.amountCovered)
                  : convertToCurrencyString(
                      (row.percentageCovered / 100) *
                        (convertToNumber(row.price) * convertToNumber(row.quantity)),
                    )
                : convertToCurrencyString(
                    convertToNumber(row.price) * convertToNumber(row.quantity),
                  )
            }`}
          </span>
        );
      },
      customValue(row) {
        return convertToNumber(row.price) * convertToNumber(row.quantity);
      },
      showSum: true,
      sumFormatter: (data) => `₦${convertToCurrencyString(data as string)}`,
      customSumValue(data) {
        return `₦${convertToCurrencyString(
          calculateTotalUtilization(data, undefined, true).grandTotal,
        )}`;
      },
    },
    ...(showPercentageConvered
      ? ([
          {
            key: 'percentageCovered',
            title: 'Percentage Covered (%)',
            CustomElement({ row }) {
              return (
                <UtilizationCoveredAmountWithPopover
                  onValueChange={onPercentageCoveredChange}
                  row={row}
                  onCommentChange={() => {
                    // todo
                  }}
                  type="PercentageCovered"
                  disablePriceAdjustment={disablePriceAdjustment}
                />
              );
            },
          },
          {
            key: 'amountCovered',
            title: 'Amount Covered (NGN)',
            CustomElement({ row }) {
              return (
                <UtilizationCoveredAmountWithPopover
                  onValueChange={onAmountCoveredChange}
                  row={row}
                  onCommentChange={() => {
                    // todo
                  }}
                  type="AmountCovered"
                  disablePriceAdjustment={disablePriceAdjustment}
                />
              );
            },
          },
        ] as PlainTableColumns)
      : []),
    ...(showComments
      ? ([
          {
            key: 'comments',
            title: 'Comments',
            style: { width: 500 },
            CustomElement: ({ row }) => {
              return (
                <div className="flex flex-column">
                  {row.utilisationStatus
                    ?.filter(({ comment }) => !!comment)
                    ?.map(({ comment, creatorName }) => (
                      <span>
                        <b>{creatorName}:&nbsp;</b>
                        {comment}
                      </span>
                    ))}
                </div>
              );
            },
          },
        ] as PlainTableColumns)
      : []),
    {
      key: 'approve',
      title: (
        <div
          className={classnames('pointer', {
            'not-allowed': isAllApproved || processingBulkUtilizationsStatus,
            'opacity-30': !isAllApproved,
          })}
          onClick={
            isAllApproved || processingBulkUtilizationsStatus
              ? undefined
              : () =>
                  onProcessBulkUtilizations(
                    utilizations.map(({ id }) => id),
                    PaCodeStatus.Approved,
                  )
          }
        >
          <ApproveIcon style={{ display: 'block', margin: '0 auto' }} />
        </div>
      ),
      CustomElement({ row }) {
        const selUtilizationStatus = row.utilisationStatus?.find(
          ({ creatorId }) => creatorId === profileId,
        );
        const comment = selUtilizationStatus?.comment;
        const isApproved = selUtilizationStatus?.status === PaCodeStatus.Approved;

        return (
          <div
            onClick={
              isApproved || processingUtilizationStatus
                ? undefined
                : () =>
                    onProcessUtilization({
                      id: row.id,
                      status: PaCodeStatus.Approved,
                      statusDescription: '',
                      comment,
                    })
            }
            className={classnames('pointer', {
              'not-allowed': isApproved || processingUtilizationStatus,
              'opacity-30': !isApproved,
            })}
          >
            <ApproveIcon style={{ display: 'block', margin: '0 auto' }} />
          </div>
        );
      },
    },
    {
      key: 'reject',
      title: (
        <BulkRejectIconWithPopover
          utilizations={utilizations}
          onProcessBulkUtilizations={onProcessBulkUtilizations}
          processingBulkUtilizationsStatus={processingBulkUtilizationsStatus}
          isAllRejected={isAllRejected}
        />
      ),
      CustomElement({ row }) {
        return (
          <RejectIconWithPopover
            row={row}
            onProcessUtilization={onProcessUtilization}
            processingUtilizationStatus={processingUtilizationStatus}
          />
        );
      },
    },
    ...(showFlagControl
      ? ([
          {
            key: 'flags',
            title: (
              <HmoFlagSection
                flags={getAllFlagSelection()}
                action={(val, unset) =>
                  onUtilizationFlagged?.(
                    utilizations.map(({ id }) => id),
                    val,
                    unset,
                  )
                }
                variant={flagVariant}
                tooltipDescription={flagTooltipDescription}
              />
            ),
            style: { width: 100 },
            CustomElement({ row }) {
              const rulesMapId = row.flags?.reduce((acc, curr) => {
                if (!curr.ruleId) return acc;
                acc[curr.flag] = [...(acc[curr.flag] || []), curr.ruleId];
                return acc;
              }, {});
              return (
                <HmoFlagSection
                  showCount
                  flags={row.flags?.map(({ flag }) => flag)}
                  action={(val, unset) => onUtilizationFlagged?.([row.id], val, unset)}
                  variant={flagVariant}
                  tooltipDescription={flagTooltipDescription}
                  rulesIdMap={rulesMapId}
                />
              );
            },
          },
        ] as PlainTableColumns)
      : []),
  ];
}

type Props = {
  utilizations: HmoClaim_utilizations[];
  providerInformation: { name: string; address?: string };
  enrolleeInformation: {
    fullName: string;
    diagnosis?: Preauthorization_preauthorization_diagnosis[];
    age?: string;
    gender?: string;
    phoneNumber?: string;
    secondaryPhoneNumber?: string;
    enrolleeNumber?: string;
    enrolleePlanType?: string;
    enrolleePlanCategory?: string;
    profileId: string;
  };
  referringProvider?: {
    from?: string;
    to?: string;
  };
  authorizationCode?: string;
  onProcessUtilization: (input: ProcessPreauthUtilizationStatusVariables['input']) => void;
  onProcessBulkUtilizations: (ids: string[], status: PaCodeStatus | null) => void;
  onQuantityChange?: (value: string, id?: string) => void;
  treatmentDateTime: Date;
  hmoClaimId: string;
  showConfirmationControl?: boolean;
  showPercentageCovered?: boolean;
  showComments?: boolean;
  onPercentageCoveredChange?: (utilId: string, percentage: number | null) => void;
  onAmountCoveredChange?: (utilId: string, amount: number | null) => void;
  showFlagControl?: boolean;
  utilizationOrigin: UtilizationOriginType;
  processingUtilizationStatus?: boolean;
  processingBulkUtilizationsStatus?: boolean;
  showVitalsSummary?: boolean;
};
export default memo(function ClaimSummaryTable(props: Props) {
  const {
    providerInformation,
    enrolleeInformation,
    utilizations = [],
    referringProvider,
    authorizationCode,
    onProcessUtilization,
    onProcessBulkUtilizations,
    treatmentDateTime,
    hmoClaimId,
    showConfirmationControl,
    showPercentageCovered,
    showComments,
    onPercentageCoveredChange,
    onAmountCoveredChange,
    onQuantityChange,
    showFlagControl,
    utilizationOrigin,
    processingUtilizationStatus,
    processingBulkUtilizationsStatus,
    showVitalsSummary,
  } = props;

  const { addToast } = useToasts();
  const { isLashmaAgency } = useHospitalData();

  const {
    hasAiSuggestions,
    acceptedAiSuggestions,
    revertAiSuggestions,
    acceptAiSuggestions,
    applyingAiSuggestions,
  } = useClaimsAiSuggestions({
    utilizations,
  });

  const { data: patientMedicalHistoryData } = useQuery<
    GetPatientMedicalSummary,
    GetPatientMedicalSummaryVariables
  >(GET_PATIENT_MEDICAL_SUMMARY, {
    variables: {
      profileId: enrolleeInformation.profileId,
      date: moment(treatmentDateTime).format('YYYY-MM-DD'),
    },
    fetchPolicy: 'cache-first',
    skip: !enrolleeInformation.profileId || !showVitalsSummary,
  });

  const [updateConfirmationMutation] = useMutation<
    UpdateClaimUtilizationConfirmation,
    UpdateClaimUtilizationConfirmationVariables
  >(UPDATE_CLAIM_UTILIZATION_CONFIRMATION, {
    onCompleted() {
      addToast('Record Updated Successfully', { appearance: 'success' });
    },
    onError(error) {
      addToast(errorHandler(error), { appearance: 'error' });
    },
  });

  const [flagUtilizationMutation] = useMutation<FlagUtilizations, FlagUtilizationsVariables>(
    FLAG_UTILIZATIONS,
    {
      onCompleted() {
        addToast('Record Updated Successfully', { appearance: 'success' });
      },
      onError(error) {
        addToast(errorHandler(error), { appearance: 'error' });
      },
    },
  );

  const onUtilizationFlagged = useCallback(
    (utilizationIds: string[], flag: string, unset: boolean) => {
      flagUtilizationMutation({
        variables: {
          utilizationIds,
          flag,
          unset,
        },
      });
    },
    [flagUtilizationMutation],
  );

  // Memoize the update confirmation callback
  const handleUpdateConfirmation = useCallback(
    (utilizationIds: string[], confirmation: boolean) => {
      updateConfirmationMutation({
        variables: {
          claimId: hmoClaimId,
          confirmation,
          utilizationIds,
        },
      });
    },
    [updateConfirmationMutation, hmoClaimId],
  );

  const utilData = useMemo(() => {
    return utilizations.reduce((acc, curr) => {
      acc.push({
        paCode: curr.paCode,
        utilizationId: curr.utilizationId as string,
      });

      return acc;
    }, [] as Array<{ paCode: string | null; utilizationId: string }>);
  }, [utilizations]);

  // Memoize the vitals text generation
  const vitalsText = useMemo(() => {
    if (!showVitalsSummary || !patientMedicalHistoryData?.getPatientMedicalSummary) {
      return null;
    }

    const summary = patientMedicalHistoryData.getPatientMedicalSummary;
    return generateVitalsText(
      summary.anthropometry?.height,
      summary.anthropometry?.weight,
      summary.anthropometry?.heightUnit,
      summary.anthropometry?.weightUnit,
      summary.temperature?.reading,
      summary.temperature?.readingUnit,
      summary.bloodPressure?.diastolic,
      summary.bloodPressure?.systolic,
      summary.pulseRate?.reading,
      summary.respiratoryRate?.reading,
      summary.respiratoryRate?.oxygenSaturation,
      summary.bloodGlucose?.reading,
      summary.bloodGlucose?.readingUnit,
    );
  }, [showVitalsSummary, patientMedicalHistoryData]);

  // Memoize the table data array
  const tableData = useMemo(() => {
    const baseData = [
      { _: 'Provider Name', __: providerInformation?.name || '--' },
      { _: 'Provider Address', __: providerInformation?.address || '--' },
      { _: 'Enrollee Full Name', __: enrolleeInformation?.fullName || '--' },
      { _: 'Enrollee Age', __: enrolleeInformation?.age },
      { _: 'Enrollee Gender', __: enrolleeInformation?.gender || '--' },
      { _: 'Enrollee ID', __: enrolleeInformation?.enrolleeNumber || '--' },
      { _: 'Enrollee Plan Type', __: enrolleeInformation?.enrolleePlanType || '--' },
      {
        _: 'Enrollee Plan Category',
        __: enrolleeInformation?.enrolleePlanCategory || '--',
      },
      { _: 'Enrollee Phone Number', __: enrolleeInformation?.phoneNumber || '--' },
      {
        _: 'Enrollee Secondary Phone Number',
        __: enrolleeInformation?.secondaryPhoneNumber || '--',
      },
      { _: 'Referring Provider (From)', __: referringProvider?.from || '--' },
      { _: 'Referring Provider (To)', __: referringProvider?.to || '--' },
      { _: 'Authorization Code', __: authorizationCode || '--' },
      {
        _: 'Clinical Diagnosis',
        __: listAllDiagnosis(enrolleeInformation?.diagnosis || []) || '--',
      },
      {
        _: 'Treatment Date and Time',
        __: formatTableDateTime({ value: treatmentDateTime }),
      },
    ];

    if (showVitalsSummary && vitalsText) {
      baseData.push({
        _: 'Vital Signs',
        __: vitalsText,
      });
    }

    return baseData;
  }, [
    providerInformation,
    enrolleeInformation,
    referringProvider,
    authorizationCode,
    treatmentDateTime,
    showVitalsSummary,
    vitalsText,
  ]);

  // Memoize utilizations based on their content to prevent unnecessary column re-creation
  const memoizedUtilizations = useMemo(() => utilizations, [
    utilizations
      .map((u) => `${u.id}-${u.quantity}-${u.amountCovered}-${u.percentageCovered}`)
      .join(','),
    utilizations
      .map((u) =>
        u.utilisationStatus?.map((s) => `${s.status}-${s.creatorId}-${s.comment}`).join(','),
      )
      .join('|'),
    utilizations.map((u) => u.confirmation).join(','),
  ]);

  // Memoize the columns configuration
  const memoizedColumns = useMemo(() => {
    return columns(
      onProcessUtilization,
      onProcessBulkUtilizations,
      memoizedUtilizations,
      handleUpdateConfirmation,
      showConfirmationControl,
      showPercentageCovered,
      showComments,
      onPercentageCoveredChange,
      onQuantityChange,
      onAmountCoveredChange,
      showFlagControl,
      onUtilizationFlagged,
      undefined,
      undefined,
      processingUtilizationStatus,
      processingBulkUtilizationsStatus,
      hasAiSuggestions,
      acceptedAiSuggestions,
      acceptAiSuggestions,
      revertAiSuggestions,
      applyingAiSuggestions,
    );
  }, [
    onProcessUtilization,
    onProcessBulkUtilizations,
    memoizedUtilizations,
    handleUpdateConfirmation,
    showConfirmationControl,
    showPercentageCovered,
    showComments,
    onPercentageCoveredChange,
    onQuantityChange,
    showFlagControl,
    onUtilizationFlagged,
    processingUtilizationStatus,
    processingBulkUtilizationsStatus,
    hasAiSuggestions,
    acceptedAiSuggestions,
    applyingAiSuggestions,
    isLashmaAgency,
  ]);

  return (
    <div className="flex flex-column">
      <div className="flex flex-column" style={OVERFLOW_AUTO_STYLE}>
        <div style={MIN_WIDTH_STYLE}>
          <PlainTable
            columns={BASIC_TABLE_COLUMNS}
            data={tableData}
            showHeader={false}
            showSerial={false}
            noColoredEvenRow
          />
          <PlainTable
            showSerial={false}
            columns={memoizedColumns}
            data={memoizedUtilizations}
            noColoredHeader
            fullWidth
          />
        </div>
      </div>

      <div className="mt-3">
        <SectionReveal
          title="Claim Approval Group"
          Element={
            <UtilizationApprovalGroupTable
              utilData={utilData}
              utilType="CLAIM"
              skipFetch={utilizationOrigin === 'CLAIM'}
              utilizations={utilizations}
            />
          }
        />
      </div>

      <div>
        <SectionReveal
          title="Preauthorization Approval Group"
          openState
          Element={
            <UtilizationApprovalGroupTable
              utilData={utilData}
              utilType="PREAUTH"
              skipFetch={utilizationOrigin === 'PREAUTH'}
              utilizations={utilizations}
            />
          }
        />
      </div>
      <ReactTooltip
        id="aiReason"
        place="bottom"
        variant="dark"
        className="tooltip-ai-reason"
        render={({ content, activeAnchor }) => {
          const color = activeAnchor?.dataset?.color;
          const status = activeAnchor?.dataset?.status;
          return (
            <div className="flex flex-column">
              <div className="flex flex-row gap-8 align-items-center">
                <svg
                  width="16px"
                  height="16px"
                  viewBox="0 0 24 24"
                  fill="inherit"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M7.45284 2.71266C7.8276 1.76244 9.1724 1.76245 9.54716 2.71267L10.7085 5.65732C10.8229 5.94743 11.0526 6.17707 11.3427 6.29148L14.2873 7.45284C15.2376 7.8276 15.2376 9.1724 14.2873 9.54716L11.3427 10.7085C11.0526 10.8229 10.8229 11.0526 10.7085 11.3427L9.54716 14.2873C9.1724 15.2376 7.8276 15.2376 7.45284 14.2873L6.29148 11.3427C6.17707 11.0526 5.94743 10.8229 5.65732 10.7085L2.71266 9.54716C1.76244 9.1724 1.76245 7.8276 2.71267 7.45284L5.65732 6.29148C5.94743 6.17707 6.17707 5.94743 6.29148 5.65732L7.45284 2.71266Z"
                    fill={color || '#00abe2'}
                  />
                  <path
                    d="M16.9245 13.3916C17.1305 12.8695 17.8695 12.8695 18.0755 13.3916L18.9761 15.6753C19.039 15.8348 19.1652 15.961 19.3247 16.0239L21.6084 16.9245C22.1305 17.1305 22.1305 17.8695 21.6084 18.0755L19.3247 18.9761C19.1652 19.039 19.039 19.1652 18.9761 19.3247L18.0755 21.6084C17.8695 22.1305 17.1305 22.1305 16.9245 21.6084L16.0239 19.3247C15.961 19.1652 15.8348 19.039 15.6753 18.9761L13.3916 18.0755C12.8695 17.8695 12.8695 17.1305 13.3916 16.9245L15.6753 16.0239C15.8348 15.961 15.961 15.8348 16.0239 15.6753L16.9245 13.3916Z"
                    fill={color || '#00abe2'}
                  />
                </svg>
                <span>Clinify AI</span>
              </div>
              <span>
                <b>{status}</b> - {content}
              </span>
            </div>
          );
        }}
      />
    </div>
  );
});

export function generateVitalsText(
  height?: number | string | null,
  weight?: number | string | null,
  heightUnit?: string | null,
  weightUnit?: string | null,
  temperature?: string | number | null,
  temperatureUnit?: string | null,
  bloodPressureDiastolic?: string | number | null,
  bloodPressureSystolic?: string | number | null,
  pulseRate?: string | number | null,
  respiratorRateReading?: string | number | null,
  oxygenSaturation?: string | number | null,
  bloodGlucose?: string | number | null,
  bloodGlucoseUnit?: string | null,
): string {
  const vitalsArray: string[] = [];

  // Height
  if (height) {
    const heightText = heightUnit ? `${height}${heightUnit}` : height.toString();
    vitalsArray.push(`Height: ${heightText}`);
  }

  // Weight
  if (weight) {
    const weightText = weightUnit ? `${weight}${weightUnit}` : weight.toString();
    vitalsArray.push(`Weight: ${weightText}`);
  }

  // Temperature
  if (temperature) {
    const tempText = temperatureUnit ? `${temperature}${temperatureUnit}` : temperature.toString();
    vitalsArray.push(`Temperature: ${tempText}`);
  }

  // Blood Pressure
  if (bloodPressureSystolic && bloodPressureDiastolic) {
    vitalsArray.push(`Blood Pressure: ${bloodPressureSystolic}/${bloodPressureDiastolic} mmHg`);
  } else if (bloodPressureSystolic) {
    vitalsArray.push(`Systolic BP: ${bloodPressureSystolic} mmHg`);
  } else if (bloodPressureDiastolic) {
    vitalsArray.push(`Diastolic BP: ${bloodPressureDiastolic} mmHg`);
  }

  // Pulse Rate
  if (pulseRate) {
    vitalsArray.push(`Pulse Rate: ${pulseRate} bpm`);
  }

  // Respiratory Rate
  if (respiratorRateReading) {
    vitalsArray.push(`Respiratory Rate: ${respiratorRateReading} cpm`);
  }

  // Oxygen Saturation
  if (oxygenSaturation) {
    vitalsArray.push(`Oxygen Saturation: ${oxygenSaturation}%`);
  }

  // Blood Glucose
  if (bloodGlucose) {
    vitalsArray.push(`Blood Glucose: ${bloodGlucose}${bloodGlucoseUnit || ''}`);
  }

  // Return formatted string or default message
  return vitalsArray.length > 0 ? vitalsArray.join('; ') : '--';
}
