import { IMandatoryFieldsModuleMap } from '../mandatory-fields-modules';
import { AdmissionInputMandatoryFieldsModule } from '../mandatory-fields-modules/admission-input-module';
import { AdmissionLinesMandatoryFieldsModule } from '../mandatory-fields-modules/admission-lines-module';
import { AdmissionMandatoryFieldsModule } from '../mandatory-fields-modules/admission-module';
import { AdmissionOutputMandatoryFieldsModule } from '../mandatory-fields-modules/admission-output-module';
import { AllergyMandatoryFieldsModule } from '../mandatory-fields-modules/allergy-module';
import { AntenatalMandatoryFieldsModule } from '../mandatory-fields-modules/antenatal-module';
import { BloodTransfusionMandatoryFieldsModule } from '../mandatory-fields-modules/blood-transfusion-module';
import { ConsultationMandatoryFieldsModule } from '../mandatory-fields-modules/consultation-module';
import { DischargePatientsMandatoryFieldsModule } from '../mandatory-fields-modules/discharge-patients-module';
import { DispenseMedicationMandatoryFieldsModule } from '../mandatory-fields-modules/dispense-medication-module';
import { MyHealthMandatoryFieldsModule } from '../mandatory-fields-modules/health-module';
import { ImmunizationMandatoryFieldsModule } from '../mandatory-fields-modules/Immunization-module';
import { MedicationMandatoryFieldsModule } from '../mandatory-fields-modules/medication-module';
import { PostnatalMandatoryFieldsModule } from '../mandatory-fields-modules/postnatal-module';
import { ProcedureMandatoryFieldsModule } from '../mandatory-fields-modules/procedure-module';
import {
  ProcessInvestigationMandatoryFieldsModule,
  ProcessRadiologyInvestigationMandatoryFieldsModule,
} from '../mandatory-fields-modules/process-investigation-module';
import { InvestigationMandatoryFieldsModule } from '../mandatory-fields-modules/request-investigation-module';
import { TransferPatientsMandatoryFieldsModule } from '../mandatory-fields-modules/transfer-patients-module';
import { TreatmentPlanMandatoryFieldsModule } from '../mandatory-fields-modules/treatment-plan-module';
import { VitalMandatoryFieldsModule } from '../mandatory-fields-modules/vital-signs-module';
import {
  InvoiceItemMandatoryFieldsModule,
  InvoiceMandatoryFieldsModule,
  InvoicePaymentMandatoryFieldsModule,
} from '../mandatory-fields-modules/invoice.module';
import { ClaimMandatoryFieldsModule } from '../mandatory-fields-modules/claim-module';
import { LabourAndDeliveryMandatoryFieldsModule } from '../mandatory-fields-modules/labour-delivery-module';
import {
  HandoverNoteItemMandatoryFieldsModule,
  HandoverNoteMandatoryFieldsModule,
  HandoverStaffMandatoryFieldsModule,
} from '../mandatory-fields-modules/handover-note-module';
import { MedicalReportInputMandatoryFieldsModule } from '../mandatory-fields-modules/medical-report-module';
import { NextOfKinMandatoryFieldsModule } from '../mandatory-fields-modules/next-of-kin-module';
import { DependentsMandatoryFieldsModule } from '../mandatory-fields-modules/dependents-module';
import { AdmissionNoteMandatoryFieldsModule } from '../mandatory-fields-modules/admission-note.module';
import { NursingServicesMandatoryFieldsModule } from '../mandatory-fields-modules/nursing-services.module';
import { OncologyMandatoryFieldsModule } from '../mandatory-fields-modules/oncology.module';
import { RequestProcedureMandatoryFieldsModule } from '../mandatory-fields-modules/request-procedure.module';
import { LaboratoryMandatoryFieldsModule } from '../mandatory-fields-modules/laboratory.module';
import { RadiologyMandatoryFieldsModule } from '../mandatory-fields-modules/radiology.module';
import { PostOperationChecklistMandatoryFieldsModule } from '../mandatory-fields-modules/post-operation-checklist.module';
import { PreChemoEducationMandatoryFieldsModule } from '../mandatory-fields-modules/pre-chemo-education.module';
import { CancerScreeningMandatoryFieldsModule } from '../mandatory-fields-modules/cancer-screening.module';
import { RequestPreauthorizationMandatoryFieldsModule } from '../mandatory-fields-modules/request-preauthorization-module';
import { RequestReferralMandatoryFieldsModule } from '../mandatory-fields-modules/request-referral-module';

export const mandatoryFieldsMap: IMandatoryFieldsModuleMap = {
  Investigation: InvestigationMandatoryFieldsModule,
  'Process Investigation': ProcessInvestigationMandatoryFieldsModule,
  'Process Investigation (Radiology)': ProcessRadiologyInvestigationMandatoryFieldsModule,
  Vitals: VitalMandatoryFieldsModule,
  Admission: AdmissionMandatoryFieldsModule,
  'Discharge Patients': DischargePatientsMandatoryFieldsModule,
  'Transfer Patients': TransferPatientsMandatoryFieldsModule,
  'Blood Transfusion': BloodTransfusionMandatoryFieldsModule,
  'Admission Input': AdmissionInputMandatoryFieldsModule,
  'Admission Output': AdmissionOutputMandatoryFieldsModule,
  'Admission Lines': AdmissionLinesMandatoryFieldsModule,
  Allergy: AllergyMandatoryFieldsModule,
  Antenatal: AntenatalMandatoryFieldsModule,
  LabourAndDelivery: LabourAndDeliveryMandatoryFieldsModule,
  Postnatal: PostnatalMandatoryFieldsModule,
  Consultation: ConsultationMandatoryFieldsModule,
  'Treatment Plan': TreatmentPlanMandatoryFieldsModule,
  Immunization: ImmunizationMandatoryFieldsModule,
  Medication: MedicationMandatoryFieldsModule,
  'Dispense Medication': DispenseMedicationMandatoryFieldsModule,
  Surgery: ProcedureMandatoryFieldsModule,
  Health: MyHealthMandatoryFieldsModule,
  Invoice: InvoiceMandatoryFieldsModule,
  invoiceItems: InvoiceItemMandatoryFieldsModule,
  invoicePayments: InvoicePaymentMandatoryFieldsModule,
  'Hmo Claims': ClaimMandatoryFieldsModule,
  HandoverNote: HandoverNoteMandatoryFieldsModule,
  handoverNoteItems: HandoverNoteItemMandatoryFieldsModule,
  handoverStaffs: HandoverStaffMandatoryFieldsModule,
  MedicalReport: MedicalReportInputMandatoryFieldsModule,
  NextOfKin: NextOfKinMandatoryFieldsModule,
  Dependents: DependentsMandatoryFieldsModule,
  AdmissionNote: AdmissionNoteMandatoryFieldsModule,
  NursingServices: NursingServicesMandatoryFieldsModule,
  OncologyConsultationHistory: OncologyMandatoryFieldsModule,
  RequestProcedure: RequestProcedureMandatoryFieldsModule,
  LabTest: LaboratoryMandatoryFieldsModule,
  Radiology: RadiologyMandatoryFieldsModule,
  PostOperationChecklist: PostOperationChecklistMandatoryFieldsModule,
  PreChemoEducation: PreChemoEducationMandatoryFieldsModule,
  CancerScreening: CancerScreeningMandatoryFieldsModule,
  Preauthorization: RequestPreauthorizationMandatoryFieldsModule,
  PreauthorizationReferral: RequestReferralMandatoryFieldsModule,
};
